#!/usr/bin/env python3
"""
LegalAI V4 Demo Data Seeding Script

This script seeds the database with realistic demo data including:
- Demo users with different roles
- Demo workspaces for different industries
- Realistic contract templates
- Sample contracts with proper data

Usage:
    python seed_demo_data.py                    # Seed with existing data
    python seed_demo_data.py --clear           # Clear existing demo data first
    python seed_demo_data.py --env-file .env   # Use specific .env file
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.db.seed_data import DataSeeder

def main():
    """Main function to run the seeding script."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Seed LegalAI V4 database with demo data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python seed_demo_data.py                    # Seed with existing data
  python seed_demo_data.py --clear           # Clear existing demo data first
  python seed_demo_data.py --env-file .env   # Use specific .env file
        """
    )
    parser.add_argument(
        "--clear", 
        action="store_true", 
        help="Clear existing demo data before seeding"
    )
    parser.add_argument(
        "--env-file", 
        type=str, 
        help="Path to .env file (optional)"
    )
    
    args = parser.parse_args()
    
    # Load environment variables from .env file if specified
    if args.env_file:
        try:
            from dotenv import load_dotenv
            load_dotenv(args.env_file)
            print(f"📁 Loaded environment variables from {args.env_file}")
        except ImportError:
            print("⚠️  Warning: python-dotenv not installed. Install with: pip install python-dotenv")
        except Exception as e:
            print(f"⚠️  Warning: Could not load .env file: {e}")
    
    # Check for required environment variables
    required_vars = ["SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Error: Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these environment variables or use --env-file option.")
        sys.exit(1)
    
    try:
        print("🔧 Initializing data seeder...")
        seeder = DataSeeder()
        seeder.run_full_seed(clear_existing=args.clear)
    except Exception as e:
        print(f"❌ Error during seeding: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check your Supabase credentials")
        print("2. Ensure your database schema is up to date")
        print("3. Verify network connectivity to Supabase")
        sys.exit(1)

if __name__ == "__main__":
    main()
