-- Schema for LegalAI Contract Management System
-- This file contains SQL statements to set up the database schema in Supabase

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    title TEXT,
    company TEXT,
    timezone TEXT DEFAULT 'UTC',
    bio TEXT,
    avatar TEXT,
    initials TEXT NOT NULL,
    notification_preferences JSONB NOT NULL DEFAULT '{"email_approvals": true, "email_updates": true, "email_reminders": true, "email_comments": true, "system_approvals": true, "browser_notifications": true, "email_digest_frequency": "daily"}',
    workspaces TEXT[] DEFAULT '{}',
    workspace_roles JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Workspaces table
CREATE TABLE IF NOT EXISTS workspaces (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    members INTEGER DEFAULT 0,
    contracts INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE
);

-- Workspace members table
CREATE TABLE IF NOT EXISTS workspace_members (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id TEXT NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(workspace_id, user_id)
);

-- Contracts table
CREATE TABLE IF NOT EXISTS contracts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    type TEXT NOT NULL,
    jurisdiction TEXT,
    effective_date DATE,
    expiry_date DATE,
    description TEXT,
    counterparty TEXT,
    value TEXT,
    currency TEXT DEFAULT 'USD',
    status TEXT NOT NULL DEFAULT 'draft',
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    parties JSONB,
    clauses JSONB,
    attachments JSONB,
    approvers JSONB,
    approval_process TEXT DEFAULT 'sequential',
    tags TEXT[],
    custom_fields JSONB,
    starred BOOLEAN DEFAULT FALSE,
    folder_id TEXT
);

-- Templates table
CREATE TABLE IF NOT EXISTS templates (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL,
    complexity TEXT NOT NULL,
    industry TEXT,
    tags TEXT[],
    icon TEXT,
    created_by JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    rating FLOAT,
    is_user_created BOOLEAN DEFAULT FALSE,
    folder_id TEXT,
    content JSONB NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Folders table
CREATE TABLE IF NOT EXISTS folders (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    parent_id TEXT REFERENCES folders(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Contract Signers table
CREATE TABLE IF NOT EXISTS contract_signers (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    role TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    initials TEXT NOT NULL,
    order INTEGER NOT NULL,
    avatar TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    signed_at TIMESTAMP WITH TIME ZONE
);

-- Clauses table
CREATE TABLE IF NOT EXISTS clauses (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    version TEXT NOT NULL,
    approved BOOLEAN DEFAULT FALSE,
    author TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    filename TEXT NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    content TEXT,
    file_url TEXT,
    file_path TEXT,
    file_info JSONB,
    folder TEXT,
    status TEXT NOT NULL DEFAULT 'draft',
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Document Signers table
CREATE TABLE IF NOT EXISTS document_signers (
    id TEXT PRIMARY KEY,
    document_id TEXT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    role TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    order INTEGER NOT NULL,
    avatar TEXT,
    signature_data JSONB,
    completed_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE,
    decline_reason TEXT
);

-- Permissions table (system-wide permission definitions)
CREATE TABLE IF NOT EXISTS permissions (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Roles table (workspace-specific role definitions)
CREATE TABLE IF NOT EXISTS roles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    permissions TEXT[] DEFAULT '{}',
    is_system BOOLEAN DEFAULT FALSE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(name, workspace_id)
);

-- Notifications table (user notification system)
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL, -- 'approval', 'contract', 'system', 'mention'
    status TEXT NOT NULL DEFAULT 'unread', -- 'unread', 'read'
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    sender_id TEXT REFERENCES users(id),
    entity_id TEXT,
    entity_type TEXT,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Activity Logs table (audit trail and activity tracking)
CREATE TABLE IF NOT EXISTS activity_logs (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    user_id TEXT NOT NULL REFERENCES users(id),
    user_name TEXT NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    workspace_name TEXT NOT NULL,
    target_id TEXT,
    target_type TEXT,
    target_name TEXT,
    details JSONB,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Export History table (document export tracking)
CREATE TABLE IF NOT EXISTS export_history (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    contract_title TEXT NOT NULL,
    format TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    download_url TEXT NOT NULL,
    template_used TEXT,
    branding_settings JSONB,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    exported_by JSONB NOT NULL,
    exported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    download_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- AI Analysis Results table (AI contract analysis storage)
CREATE TABLE IF NOT EXISTS ai_analysis_results (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    risk_score FLOAT NOT NULL,
    compliance_score FLOAT NOT NULL,
    language_clarity FLOAT NOT NULL,
    key_risks JSONB DEFAULT '[]',
    suggestions JSONB DEFAULT '[]',
    extracted_clauses JSONB DEFAULT '[]',
    compliance_issues JSONB DEFAULT '[]',
    obligations JSONB DEFAULT '[]',
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Performance Indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_workspace ON notifications(user_id, workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
CREATE INDEX IF NOT EXISTS idx_activity_logs_workspace_created ON activity_logs(workspace_id, created_at);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_export_history_workspace_contract ON export_history(workspace_id, contract_id);
CREATE INDEX IF NOT EXISTS idx_export_history_status ON export_history(status);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_contract ON ai_analysis_results(contract_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_workspace ON ai_analysis_results(workspace_id);
CREATE INDEX IF NOT EXISTS idx_roles_workspace ON roles(workspace_id);
CREATE INDEX IF NOT EXISTS idx_clauses_workspace ON clauses(workspace_id);

-- Row Level Security Policies

-- Users table policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY users_select_policy ON users
    FOR SELECT USING (true);  -- Anyone can view users

CREATE POLICY users_insert_policy ON users
    FOR INSERT WITH CHECK (auth.uid()::text = id);  -- Only the user can insert their own record

CREATE POLICY users_update_policy ON users
    FOR UPDATE USING (auth.uid()::text = id);  -- Only the user can update their own record

-- Workspaces table policies
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;

CREATE POLICY workspaces_select_policy ON workspaces
    FOR SELECT USING (
        id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view workspaces they are members of

CREATE POLICY workspaces_insert_policy ON workspaces
    FOR INSERT WITH CHECK (created_by = auth.uid()::text);  -- Only the creator can insert a workspace

CREATE POLICY workspaces_update_policy ON workspaces
    FOR UPDATE USING (
        id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can update workspaces

CREATE POLICY workspaces_delete_policy ON workspaces
    FOR DELETE USING (
        id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete workspaces

-- Workspace members table policies
ALTER TABLE workspace_members ENABLE ROW LEVEL SECURITY;

CREATE POLICY workspace_members_select_policy ON workspace_members
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view members of workspaces they are members of

CREATE POLICY workspace_members_insert_policy ON workspace_members
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        (user_id = auth.uid()::text AND EXISTS (
            SELECT 1 FROM workspaces WHERE id = workspace_id AND created_by = auth.uid()::text
        ))
    );  -- Only workspace admins can add members, or users can add themselves to workspaces they created

-- Contracts table policies
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;

CREATE POLICY contracts_select_policy ON contracts
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view contracts in workspaces they are members of

CREATE POLICY contracts_insert_policy ON contracts
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create contracts in workspaces they are members of

CREATE POLICY contracts_update_policy ON contracts
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update contracts in workspaces they are members of

CREATE POLICY contracts_delete_policy ON contracts
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        (created_by->>'id' = auth.uid()::text)
    );  -- Only workspace admins or the creator can delete contracts

-- Templates table policies
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY templates_select_policy ON templates
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view templates in workspaces they are members of

CREATE POLICY templates_insert_policy ON templates
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create templates in workspaces they are members of

CREATE POLICY templates_update_policy ON templates
    FOR UPDATE USING (
        (created_by->>'id' = auth.uid()::text) OR
        (workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ))
    );  -- Only the creator or workspace admins can update templates

CREATE POLICY templates_delete_policy ON templates
    FOR DELETE USING (
        (created_by->>'id' = auth.uid()::text) OR
        (workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ))
    );  -- Only the creator or workspace admins can delete templates

-- Folders table policies
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;

CREATE POLICY folders_select_policy ON folders
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view folders in workspaces they are members of

CREATE POLICY folders_insert_policy ON folders
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create folders in workspaces they are members of

CREATE POLICY folders_update_policy ON folders
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update folders in workspaces they are members of

CREATE POLICY folders_delete_policy ON folders
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        (created_by = auth.uid()::text)
    );  -- Only workspace admins or the creator can delete folders

-- Contract signers table policies
ALTER TABLE contract_signers ENABLE ROW LEVEL SECURITY;

CREATE POLICY contract_signers_select_policy ON contract_signers
    FOR SELECT USING (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only view signers for contracts in workspaces they are members of

CREATE POLICY contract_signers_insert_policy ON contract_signers
    FOR INSERT WITH CHECK (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only add signers to contracts in workspaces they are members of

CREATE POLICY contract_signers_update_policy ON contract_signers
    FOR UPDATE USING (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only update signers for contracts in workspaces they are members of

CREATE POLICY contract_signers_delete_policy ON contract_signers
    FOR DELETE USING (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only delete signers for contracts in workspaces they are members of

-- Clauses table policies
ALTER TABLE clauses ENABLE ROW LEVEL SECURITY;

CREATE POLICY clauses_select_policy ON clauses
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view clauses in workspaces they are members of

CREATE POLICY clauses_insert_policy ON clauses
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create clauses in workspaces they are members of

CREATE POLICY clauses_update_policy ON clauses
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND (
            created_by = auth.uid()::text OR
            auth.uid() IN (
                SELECT user_id FROM workspace_members
                WHERE workspace_id = clauses.workspace_id AND role_id = 'role-admin'
            )
        )
    );  -- Only the creator or workspace admins can update clauses in their workspace

CREATE POLICY clauses_delete_policy ON clauses
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND (
            created_by = auth.uid()::text OR
            auth.uid() IN (
                SELECT user_id FROM workspace_members
                WHERE workspace_id = clauses.workspace_id AND role_id = 'role-admin'
            )
        )
    );  -- Only the creator or workspace admins can delete clauses in their workspace

-- Documents table policies
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY documents_select_policy ON documents
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view documents in workspaces they are members of

CREATE POLICY documents_insert_policy ON documents
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create documents in workspaces they are members of

CREATE POLICY documents_update_policy ON documents
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update documents in workspaces they are members of

CREATE POLICY documents_delete_policy ON documents
    FOR DELETE USING (
        (created_by = auth.uid()::text) OR
        (workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ))
    );  -- Only the creator or workspace admins can delete documents

-- Document Signers table policies
ALTER TABLE document_signers ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_signers_select_policy ON document_signers
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only view signers for documents in workspaces they are members of

CREATE POLICY document_signers_insert_policy ON document_signers
    FOR INSERT WITH CHECK (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only add signers to documents in workspaces they are members of

CREATE POLICY document_signers_update_policy ON document_signers
    FOR UPDATE USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only update signers for documents in workspaces they are members of

CREATE POLICY document_signers_delete_policy ON document_signers
    FOR DELETE USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only delete signers for documents in workspaces they are members of

-- Permissions table policies
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY permissions_select_policy ON permissions
    FOR SELECT USING (true);  -- All authenticated users can view permissions

CREATE POLICY permissions_insert_policy ON permissions
    FOR INSERT WITH CHECK (false);  -- Only system can insert permissions

CREATE POLICY permissions_update_policy ON permissions
    FOR UPDATE USING (false);  -- Only system can update permissions

CREATE POLICY permissions_delete_policy ON permissions
    FOR DELETE USING (false);  -- Only system can delete permissions

-- Roles table policies
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY roles_select_policy ON roles
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view roles in workspaces they are members of

CREATE POLICY roles_insert_policy ON roles
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can create roles

CREATE POLICY roles_update_policy ON roles
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) AND is_system = false
    );  -- Only workspace admins can update non-system roles

CREATE POLICY roles_delete_policy ON roles
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) AND is_system = false
    );  -- Only workspace admins can delete non-system roles

-- Notifications table policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY notifications_select_policy ON notifications
    FOR SELECT USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view their own notifications in workspaces they are members of

CREATE POLICY notifications_insert_policy ON notifications
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create notifications in workspaces they are members of

CREATE POLICY notifications_update_policy ON notifications
    FOR UPDATE USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update their own notifications

CREATE POLICY notifications_delete_policy ON notifications
    FOR DELETE USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only delete their own notifications

-- Activity Logs table policies
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY activity_logs_select_policy ON activity_logs
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view activity logs in workspaces they are members of

CREATE POLICY activity_logs_insert_policy ON activity_logs
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create activity logs in workspaces they are members of

CREATE POLICY activity_logs_update_policy ON activity_logs
    FOR UPDATE USING (false);  -- Activity logs cannot be updated

CREATE POLICY activity_logs_delete_policy ON activity_logs
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete activity logs

-- Export History table policies
ALTER TABLE export_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY export_history_select_policy ON export_history
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view export history in workspaces they are members of

CREATE POLICY export_history_insert_policy ON export_history
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create export history in workspaces they are members of

CREATE POLICY export_history_update_policy ON export_history
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update export history in workspaces they are members of

CREATE POLICY export_history_delete_policy ON export_history
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete export history

-- AI Analysis Results table policies
ALTER TABLE ai_analysis_results ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_analysis_results_select_policy ON ai_analysis_results
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_insert_policy ON ai_analysis_results
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_update_policy ON ai_analysis_results
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_delete_policy ON ai_analysis_results
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only delete AI analysis results in workspaces they are members of
