from supabase import create_client, Client
from app.core.config import settings

def get_supabase_client() -> Client:
    """
    Create and return a Supabase client instance.
    """
    try:
        supabase_client: Client = create_client(
            settings.SUPABASE_URL,
            settings.SUPABASE_KEY
        )
        return supabase_client
    except Exception as e:
        # Log the error
        print(f"Error creating Supabase client: {e}")
        raise
