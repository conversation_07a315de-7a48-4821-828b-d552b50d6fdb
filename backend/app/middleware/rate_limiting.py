"""
Rate limiting middleware for LegalAI API.

This module provides comprehensive rate limiting functionality with different
limits for different types of operations and user tiers.
"""

import time
import json
import hashlib
from typing import Dict, Optional, Tuple
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import logging
from datetime import datetime, timedelta
from app.core.config import settings

logger = logging.getLogger(__name__)

class InMemoryRateLimiter:
    """
    In-memory rate limiter using sliding window algorithm.
    For production, consider using Redis for distributed rate limiting.
    """
    
    def __init__(self):
        self.requests: Dict[str, list] = {}
        self.last_cleanup = time.time()
        
    def _cleanup_old_requests(self):
        """Remove old request records to prevent memory leaks."""
        current_time = time.time()
        
        # Only cleanup every 5 minutes to avoid performance impact
        if current_time - self.last_cleanup < 300:
            return
            
        cutoff_time = current_time - 3600  # Remove requests older than 1 hour
        
        for key in list(self.requests.keys()):
            self.requests[key] = [
                req_time for req_time in self.requests[key] 
                if req_time > cutoff_time
            ]
            
            # Remove empty entries
            if not self.requests[key]:
                del self.requests[key]
                
        self.last_cleanup = current_time
        
    def is_allowed(self, key: str, limit: int, window_seconds: int) -> Tuple[bool, Dict]:
        """
        Check if a request is allowed based on rate limiting rules.
        
        Args:
            key: Unique identifier for the rate limit (e.g., user_id, ip_address)
            limit: Maximum number of requests allowed in the window
            window_seconds: Time window in seconds
            
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        current_time = time.time()
        window_start = current_time - window_seconds
        
        # Cleanup old requests periodically
        self._cleanup_old_requests()
        
        # Get existing requests for this key
        if key not in self.requests:
            self.requests[key] = []
            
        # Remove requests outside the current window
        self.requests[key] = [
            req_time for req_time in self.requests[key] 
            if req_time > window_start
        ]
        
        # Check if limit is exceeded
        current_count = len(self.requests[key])
        is_allowed = current_count < limit
        
        if is_allowed:
            # Add current request
            self.requests[key].append(current_time)
            current_count += 1
            
        # Calculate reset time
        if self.requests[key]:
            oldest_request = min(self.requests[key])
            reset_time = oldest_request + window_seconds
        else:
            reset_time = current_time + window_seconds
            
        rate_limit_info = {
            "limit": limit,
            "remaining": max(0, limit - current_count),
            "reset": int(reset_time),
            "window_seconds": window_seconds
        }
        
        return is_allowed, rate_limit_info

# Global rate limiter instance
rate_limiter = InMemoryRateLimiter()

class RateLimitConfig:
    """Configuration for different rate limiting tiers and endpoints."""
    
    # Default rate limits (requests per minute)
    DEFAULT_LIMITS = {
        "general": {"limit": 100, "window": 60},  # 100 requests per minute
        "auth": {"limit": 10, "window": 60},      # 10 auth requests per minute
        "upload": {"limit": 20, "window": 60},    # 20 uploads per minute
        "export": {"limit": 30, "window": 60},    # 30 exports per minute
        "ai_analysis": {"limit": 10, "window": 60}, # 10 AI analysis per minute
    }
    
    # Premium user limits (higher limits for paid users)
    PREMIUM_LIMITS = {
        "general": {"limit": 500, "window": 60},
        "auth": {"limit": 20, "window": 60},
        "upload": {"limit": 100, "window": 60},
        "export": {"limit": 100, "window": 60},
        "ai_analysis": {"limit": 50, "window": 60},
    }
    
    # Endpoint-specific rate limit categories
    ENDPOINT_CATEGORIES = {
        "/api/auth/": "auth",
        "/api/documents/upload": "upload",
        "/api/contracts/generate-document": "export",
        "/api/contracts/batch-generate-documents": "export",
        "/api/ai/analyze": "ai_analysis",
    }

def get_client_identifier(request: Request) -> str:
    """
    Generate a unique identifier for rate limiting.
    Uses user ID if authenticated, otherwise falls back to IP address.
    """
    # Try to get user ID from request (if authenticated)
    user_id = getattr(request.state, 'user_id', None)
    if user_id:
        return f"user:{user_id}"
    
    # Fall back to IP address
    client_ip = request.client.host if request.client else "unknown"
    
    # Handle forwarded headers for reverse proxies
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        client_ip = forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        client_ip = real_ip
        
    return f"ip:{client_ip}"

def get_rate_limit_category(request: Request) -> str:
    """Determine the rate limit category for a request."""
    path = request.url.path
    
    # Check for specific endpoint matches
    for endpoint_pattern, category in RateLimitConfig.ENDPOINT_CATEGORIES.items():
        if path.startswith(endpoint_pattern):
            return category
            
    return "general"

def is_premium_user(request: Request) -> bool:
    """
    Check if the user has premium rate limits.
    This is a placeholder - implement based on your user tier system.
    """
    # TODO: Implement premium user detection based on your business logic
    # For now, return False (all users get default limits)
    return False

async def rate_limit_middleware(request: Request, call_next):
    """
    Rate limiting middleware for FastAPI.
    """
    try:
        # Skip rate limiting for health checks and docs
        if request.url.path in ["/api/health", "/api/docs", "/api/redoc", "/api/openapi.json"]:
            response = await call_next(request)
            return response
        
        # Get client identifier and rate limit category
        client_id = get_client_identifier(request)
        category = get_rate_limit_category(request)
        
        # Determine rate limits based on user tier
        if is_premium_user(request):
            limits = RateLimitConfig.PREMIUM_LIMITS.get(category, RateLimitConfig.PREMIUM_LIMITS["general"])
        else:
            limits = RateLimitConfig.DEFAULT_LIMITS.get(category, RateLimitConfig.DEFAULT_LIMITS["general"])
        
        # Create rate limit key
        rate_limit_key = f"{client_id}:{category}"
        
        # Check rate limit
        is_allowed, rate_info = rate_limiter.is_allowed(
            rate_limit_key, 
            limits["limit"], 
            limits["window"]
        )
        
        if not is_allowed:
            # Rate limit exceeded
            logger.warning(
                f"Rate limit exceeded for {client_id} on {category} endpoint: {request.url.path}"
            )
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": f"Rate limit exceeded. Maximum {limits['limit']} requests per {limits['window']} seconds.",
                    "rate_limit": rate_info,
                    "retry_after": rate_info["reset"] - int(time.time())
                },
                headers={
                    "X-RateLimit-Limit": str(rate_info["limit"]),
                    "X-RateLimit-Remaining": str(rate_info["remaining"]),
                    "X-RateLimit-Reset": str(rate_info["reset"]),
                    "Retry-After": str(max(1, rate_info["reset"] - int(time.time())))
                }
            )
        
        # Process the request
        response = await call_next(request)
        
        # Add rate limit headers to successful responses
        response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
        response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
        response.headers["X-RateLimit-Reset"] = str(rate_info["reset"])
        
        return response
        
    except Exception as e:
        logger.error(f"Error in rate limiting middleware: {e}")
        # Continue processing if rate limiting fails
        response = await call_next(request)
        return response
