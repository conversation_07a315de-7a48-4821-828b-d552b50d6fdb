from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class WorkspaceBase(BaseModel):
    name: str
    description: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None

class WorkspaceCreate(WorkspaceBase):
    id: Optional[str] = None  # Allow custom ID (e.g., from Clerk organization)

class WorkspaceUpdate(WorkspaceBase):
    name: Optional[str] = None
    description: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class WorkspaceInDB(WorkspaceBase):
    id: str
    created_by: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    members: Optional[int] = 0
    contracts: Optional[int] = 0
    is_active: bool = True

class Workspace(WorkspaceInDB):
    pass

class WorkspaceMemberBase(BaseModel):
    workspace_id: str
    user_id: str
    role_id: str
    status: str = "active"

class WorkspaceMemberCreate(WorkspaceMemberBase):
    pass

class WorkspaceMemberUpdate(BaseModel):
    role_id: Optional[str] = None
    status: Optional[str] = None

class WorkspaceMember(WorkspaceMemberBase):
    id: str
    joined_at: datetime
    user: Optional[Dict[str, Any]] = None

class WorkspaceWithMembers(Workspace):
    members_list: List[WorkspaceMember] = []
