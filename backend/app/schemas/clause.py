from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class ClauseBase(BaseModel):
    """Base model for a clause."""
    title: str = Field(..., description="The title of the clause")
    content: str = Field(..., description="The content of the clause")
    category: str = Field(..., description="The category of the clause (e.g., 'liability', 'confidentiality')")
    tags: List[str] = Field(default=[], description="Tags associated with the clause")
    version: str = Field(..., description="The version of the clause")
    approved: bool = Field(default=False, description="Whether the clause is approved")
    author: Optional[str] = Field(None, description="The author of the clause")
    is_favorite: Optional[bool] = Field(None, description="Whether the clause is a favorite")

class ClauseCreate(ClauseBase):
    """Model for creating a new clause."""
    pass

class ClauseUpdate(BaseModel):
    """Model for updating an existing clause."""
    title: Optional[str] = None
    content: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    version: Optional[str] = None
    approved: Optional[bool] = None
    author: Optional[str] = None
    is_favorite: Optional[bool] = None
    last_used: Optional[datetime] = None

class Clause(ClauseBase):
    """Model for a clause with all fields."""
    id: str = Field(..., description="The unique identifier for the clause")
    created_at: datetime = Field(..., description="When the clause was created")
    created_by: str = Field(..., description="The ID of the user who created this clause")
    last_updated: datetime = Field(..., description="When the clause was last updated")
    last_used: Optional[datetime] = Field(None, description="When the clause was last used")
    
    class Config:
        from_attributes = True
