from fastapi import APIRouter
from app.api.api_v1.endpoints import contracts, workspaces, users, templates, signers, clauses, documents, storage, analytics, demo, clerk_webhooks, export_history, monitoring

api_router = APIRouter()

api_router.include_router(contracts.router, prefix="/contracts", tags=["contracts"])
api_router.include_router(workspaces.router, prefix="/workspaces", tags=["workspaces"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(templates.router, prefix="/templates", tags=["templates"])
api_router.include_router(documents.router, prefix="/documents", tags=["documents"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
api_router.include_router(signers.router, tags=["signers"])
api_router.include_router(clauses.router, tags=["clauses"])
api_router.include_router(storage.router, prefix="/storage", tags=["storage"])
api_router.include_router(demo.router, prefix="/demo", tags=["demo"])
api_router.include_router(clerk_webhooks.router, prefix="/clerk-webhooks", tags=["clerk-webhooks"])
api_router.include_router(export_history.router, prefix="/export-history", tags=["export-history"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
