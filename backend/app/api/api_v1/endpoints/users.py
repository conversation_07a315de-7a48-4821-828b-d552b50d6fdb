from fastapi import APIRouter, Depends, HTTPException, Path, Query
from typing import List, Optional
from app.schemas.user import User, UserCreate, UserUpdate, UserWithPermissions
from app.core.auth import get_current_user
from app.db.database import get_supabase_client
from datetime import datetime
import httpx

router = APIRouter()

@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve the current user's information.
    """
    supabase = get_supabase_client()

    response = supabase.table("users").select("*").eq("id", current_user["id"]).execute()

    # Handle Supabase response properly
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data

    if has_error:
        raise HTTPException(status_code=400, detail=str(response.error))

    if not has_data:
        # User exists in Clerk but not in our database, create a new user record
        # In a real app, you'd fetch more details from Clerk's API

        # Generate initials from user ID for now
        initials = current_user["id"][:2].upper()

        new_user = {
            "id": current_user["id"],
            "email": "<EMAIL>",  # Placeholder, would come from Clerk
            "first_name": "New",  # Placeholder
            "last_name": "User",  # Placeholder
            "created_at": datetime.utcnow().isoformat(),
            "initials": initials,
            "notification_preferences": {
                "email_approvals": True,
                "email_updates": True,
                "email_reminders": True,
                "email_comments": True,
                "system_approvals": True,
                "browser_notifications": True,
                "email_digest_frequency": "daily"
            }
        }

        create_response = supabase.table("users").insert(new_user).execute()

        # Handle create response properly
        create_has_error = hasattr(create_response, 'error') and create_response.error
        create_has_data = hasattr(create_response, 'data') and create_response.data

        if create_has_error:
            raise HTTPException(status_code=400, detail=str(create_response.error))

        if create_has_data:
            return create_response.data[0]
        else:
            # Return the new_user data if no response data
            return new_user

    return response.data[0]

@router.put("/me", response_model=User)
async def update_current_user(
    user_update: UserUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    Update the current user's information.
    """
    supabase = get_supabase_client()

    # Prepare update data
    update_data = user_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()

    # If first_name or last_name is updated, update initials
    if "first_name" in update_data or "last_name" in update_data:
        # Get current user data
        user_response = supabase.table("users").select("*").eq("id", current_user["id"]).execute()

        # Handle user data response
        user_data_result = handle_supabase_response(user_response, "Failed to retrieve user data")

        if not user_data_result:
            raise HTTPException(status_code=404, detail="User not found")

        user_data = user_data_result[0]

        # Update first_name and last_name with new values or keep existing ones
        first_name = update_data.get("first_name", user_data["first_name"])
        last_name = update_data.get("last_name", user_data["last_name"])

        # Generate new initials
        update_data["initials"] = (first_name[0] + last_name[0]).upper() if first_name and last_name else "??"

    # Update the user
    response = supabase.table("users").update(update_data).eq("id", current_user["id"]).execute()

    # Handle user update response
    updated_data = handle_supabase_response(response, "Failed to update user")

    if not updated_data:
        raise HTTPException(status_code=500, detail="User update failed - no data returned")

    return updated_data[0]

@router.get("/workspace/{workspace_id}", response_model=List[User])
async def get_workspace_users(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve all users in a specific workspace.
    """
    supabase = get_supabase_client()

    # Check if user is a member of this workspace
    member_check = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Get all members of the workspace
    members_response = supabase.table("workspace_members").select("user_id, role_id").eq("workspace_id", workspace_id).execute()

    # Handle members response
    members_data = handle_supabase_response(members_response, "Failed to retrieve workspace members")

    if not members_data:
        return []

    # Extract user IDs
    user_ids = [member["user_id"] for member in members_data]

    # Get user details
    users_response = supabase.table("users").select("*").in_("id", user_ids).execute()

    # Handle users response
    users_data = handle_supabase_response(users_response, "Failed to retrieve user details")

    # Add role information to each user
    users = users_data
    role_map = {member["user_id"]: member["role_id"] for member in members_data}

    for user in users:
        user["workspace_roles"] = {workspace_id: role_map.get(user["id"], "")}

    return users

@router.post("/workspace/{workspace_id}/invite", response_model=dict)
async def invite_user_to_workspace(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    email: str = Query(..., description="Email of the user to invite"),
    role_id: str = Query(..., description="Role ID to assign to the user"),
    current_user: dict = Depends(get_current_user)
):
    """
    Invite a user to a workspace.
    """
    supabase = get_supabase_client()

    # Check if user is an admin of this workspace
    member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    if member_data[0]["role_id"] != "role-admin":
        raise HTTPException(status_code=403, detail="Only workspace administrators can invite users")

    # In a real app, you would:
    # 1. Check if the user exists in your system
    # 2. If not, create an invitation record
    # 3. Send an email invitation

    # For this example, we'll just return a success message
    return {
        "message": f"Invitation sent to {email} for workspace {workspace_id} with role {role_id}",
        "status": "pending"
    }
