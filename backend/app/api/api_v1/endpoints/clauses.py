from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Path, Query, Body
from typing import List, Optional
from app.schemas.clause import Clause, ClauseCreate, ClauseUpdate
from app.core.auth import get_current_user
from app.db.database import get_supabase_client
from datetime import datetime
import uuid

router = APIRouter()

@router.get("/clauses", response_model=List[Clause])
async def get_clauses(
    workspace_id: str = Query(..., description="Workspace ID (required for security)"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve clauses for a specific workspace that the user has access to.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Get clauses filtered by workspace
    response = supabase.table("clauses").select("*").eq("workspace_id", workspace_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data

@router.post("/clauses", response_model=Clause)
async def create_clause(
    clause: ClauseCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new clause.
    """
    supabase = get_supabase_client()

    # Prepare the clause data
    clause_data = clause.model_dump()
    clause_data["id"] = f"cl-{str(uuid.uuid4())}"
    clause_data["created_at"] = datetime.utcnow().isoformat()
    clause_data["created_by"] = current_user["id"]
    clause_data["last_updated"] = datetime.utcnow().isoformat()

    # Insert the clause
    response = supabase.table("clauses").insert(clause_data).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data[0]

@router.get("/clauses/{clause_id}", response_model=Clause)
async def get_clause(
    clause_id: str = Path(..., description="The ID of the clause"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific clause.
    """
    supabase = get_supabase_client()

    # Get the clause
    response = supabase.table("clauses").select("*").eq("id", clause_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    if not response.data:
        raise HTTPException(status_code=404, detail="Clause not found")

    return response.data[0]

@router.put("/clauses/{clause_id}", response_model=Clause)
async def update_clause(
    clause_update: ClauseUpdate,
    clause_id: str = Path(..., description="The ID of the clause"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific clause.
    """
    supabase = get_supabase_client()

    # Check if the clause exists
    check_response = supabase.table("clauses").select("*").eq("id", clause_id).execute()

    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)

    if not check_response.data:
        raise HTTPException(status_code=404, detail="Clause not found")

    # Prepare the update data
    update_data = clause_update.model_dump(exclude_unset=True)
    update_data["last_updated"] = datetime.utcnow().isoformat()

    # Update the clause
    response = supabase.table("clauses").update(update_data).eq("id", clause_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data[0]

@router.put("/clauses/{clause_id}/use", response_model=Clause)
async def update_clause_usage(
    clause_id: str = Path(..., description="The ID of the clause"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update the last used timestamp of a clause.
    """
    supabase = get_supabase_client()

    # Check if the clause exists
    check_response = supabase.table("clauses").select("*").eq("id", clause_id).execute()

    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)

    if not check_response.data:
        raise HTTPException(status_code=404, detail="Clause not found")

    # Update the last used timestamp
    update_data = {
        "last_used": datetime.utcnow().isoformat()
    }

    # Update the clause
    response = supabase.table("clauses").update(update_data).eq("id", clause_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data[0]

@router.delete("/clauses/{clause_id}")
async def delete_clause(
    clause_id: str = Path(..., description="The ID of the clause"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a specific clause.
    """
    supabase = get_supabase_client()

    # Check if the clause exists
    check_response = supabase.table("clauses").select("*").eq("id", clause_id).execute()

    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)

    if not check_response.data:
        raise HTTPException(status_code=404, detail="Clause not found")

    # Delete the clause
    response = supabase.table("clauses").delete().eq("id", clause_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return {"message": "Clause deleted successfully"}
