"""
Advanced Document Generation Service

This service provides comprehensive document generation capabilities including:
- PDF generation with custom styling and branding
- Multiple export formats (PDF, DOCX, HTML, TXT)
- Template-based document creation
- Batch document generation
- Custom letterheads and branding
- Advanced formatting options
"""

import os
import uuid
import tempfile
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path
import json
import base64

# PDF generation - Try WeasyPrint first, fall back to ReportLab
try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False
    HTML = None
    CSS = None
    FontConfiguration = None

# ReportLab imports (always available as fallback)
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY

# DOCX generation
from docx import Document as DocxDocument
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE

# HTML/Template processing
from jinja2 import Environment, FileSystemLoader, Template
import markdown

# Storage
from app.services.storage import StorageService
from app.db.database import get_supabase_client


class DocumentGeneratorService:
    """Advanced document generation service with multiple format support."""

    def __init__(self):
        # Initialize font configuration for WeasyPrint if available
        if WEASYPRINT_AVAILABLE:
            self.font_config = FontConfiguration()
        else:
            self.font_config = None

        self.template_dir = Path(__file__).parent.parent / "templates" / "documents"
        self.template_dir.mkdir(parents=True, exist_ok=True)

        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            autoescape=True
        )

    async def generate_contract_document(
        self,
        contract_data: Dict[str, Any],
        format_type: str = "pdf",
        template_name: Optional[str] = None,
        branding_options: Optional[Dict[str, Any]] = None,
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a contract document in the specified format.

        Args:
            contract_data: Contract data from the database
            format_type: Output format (pdf, docx, html, txt, markdown)
            template_name: Custom template to use
            branding_options: Custom branding and styling options
            export_options: Additional export configuration

        Returns:
            Dict containing file information and download URL
        """
        try:
            # Prepare document context
            context = self._prepare_document_context(contract_data, branding_options)

            # Generate document based on format
            if format_type.lower() == "pdf":
                return await self._generate_pdf(context, template_name, export_options)
            elif format_type.lower() == "docx":
                return await self._generate_docx(context, template_name, export_options)
            elif format_type.lower() == "html":
                return await self._generate_html(context, template_name, export_options)
            elif format_type.lower() == "txt":
                return await self._generate_txt(context, export_options)
            elif format_type.lower() == "markdown":
                return await self._generate_markdown(context, export_options)
            else:
                raise ValueError(f"Unsupported format: {format_type}")

        except Exception as e:
            raise Exception(f"Document generation failed: {str(e)}")

    def _prepare_document_context(
        self,
        contract_data: Dict[str, Any],
        branding_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Prepare the context data for document generation."""

        # Default branding options
        default_branding = {
            "company_name": "LegalAI",
            "company_logo": None,
            "letterhead": True,
            "footer_text": "Generated by LegalAI Contract Management System",
            "color_scheme": {
                "primary": "#1e293b",
                "secondary": "#64748b",
                "accent": "#3b82f6"
            },
            "fonts": {
                "heading": "Arial, sans-serif",
                "body": "Times New Roman, serif"
            }
        }

        # Merge with custom branding
        branding = {**default_branding, **(branding_options or {})}

        # Prepare parties information
        parties = contract_data.get("parties", [])
        formatted_parties = []

        for i, party in enumerate(parties):
            formatted_party = {
                "index": i,
                "letter": chr(65 + i),  # A, B, C, etc.
                "name": party.get("name", f"Party {chr(65 + i)}"),
                "type": party.get("type", "individual"),
                "address": party.get("address", ""),
                "representative": party.get("representative", ""),
                "title": party.get("title", ""),
                "role": party.get("role", ""),
                **party  # Include all other party fields
            }
            formatted_parties.append(formatted_party)

        # Prepare clauses
        clauses = []
        clause_counter = 1

        # Standard clauses
        for clause_id in contract_data.get("standard_clauses", []):
            clauses.append({
                "number": clause_counter,
                "id": clause_id,
                "title": clause_id.replace("_", " ").title(),
                "content": self._get_standard_clause_content(clause_id),
                "type": "standard"
            })
            clause_counter += 1

        # Library clauses
        for clause in contract_data.get("clauses", []):
            if isinstance(clause, dict):
                clauses.append({
                    "number": clause_counter,
                    "title": clause.get("title", f"Clause {clause_counter}"),
                    "content": clause.get("content", ""),
                    "type": "library"
                })
                clause_counter += 1

        # Custom clauses
        for clause in contract_data.get("custom_clauses", []):
            content = clause if isinstance(clause, str) else clause.get("content", "")
            title = f"Custom Clause {clause_counter}" if isinstance(clause, str) else clause.get("title", f"Custom Clause {clause_counter}")

            clauses.append({
                "number": clause_counter,
                "title": title,
                "content": content,
                "type": "custom"
            })
            clause_counter += 1

        # Prepare context
        context = {
            "contract": {
                **contract_data,
                "formatted_parties": formatted_parties,
                "formatted_clauses": clauses,
                "effective_date_formatted": self._format_date(contract_data.get("effective_date")),
                "expiry_date_formatted": self._format_date(contract_data.get("expiry_date")),
                "created_at_formatted": self._format_date(contract_data.get("created_at")),
                "contract_number": f"CONT-{uuid.uuid4().hex[:8].upper()}-{datetime.now().year}"
            },
            "branding": branding,
            "generation": {
                "timestamp": datetime.now().isoformat(),
                "formatted_timestamp": datetime.now().strftime("%B %d, %Y at %I:%M %p"),
                "generator": "LegalAI Advanced Document Generator v2.0"
            }
        }

        return context

    def _get_standard_clause_content(self, clause_id: str) -> str:
        """Get content for standard legal clauses."""
        standard_clauses = {
            "confidentiality": """Each Party shall maintain the confidentiality of all Confidential Information disclosed to it by the other Party and shall not use such Confidential Information for any purpose other than as expressly permitted under this Agreement. "Confidential Information" means any non-public information that is designated as confidential or that, given the nature of the information or circumstances surrounding its disclosure, reasonably should be considered as confidential.""",

            "limitation_of_liability": """IN NO EVENT SHALL EITHER PARTY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, ARISING OUT OF OR RELATING TO THIS AGREEMENT, WHETHER BASED ON CONTRACT, TORT, NEGLIGENCE, STRICT LIABILITY OR OTHERWISE, EVEN IF SUCH PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.""",

            "termination": """Either Party may terminate this Agreement at any time without cause upon thirty (30) days' prior written notice to the other Party. In the event of such termination, Client shall pay Contractor for all services performed and expenses incurred up to the effective date of termination.""",

            "force_majeure": """Neither Party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including but not limited to acts of God, natural disasters, terrorism, riots, or war. The affected Party shall promptly notify the other Party of the force majeure event and use reasonable efforts to resume performance as soon as possible.""",

            "dispute_resolution": """Any dispute arising out of or relating to this Agreement shall first be attempted to be resolved through good faith negotiation between the Parties. If such negotiation fails, the dispute shall be resolved by binding arbitration in accordance with the rules of the American Arbitration Association, and judgment upon the award may be entered in any court having jurisdiction.""",

            "intellectual_property": """All intellectual property rights, including patents, trademarks, copyrights, and trade secrets, in and to the deliverables shall be owned exclusively by the Client upon full payment of all fees due under this Agreement. Contractor hereby assigns all right, title, and interest in such intellectual property to Client.""",

            "governing_law": """This Agreement shall be governed by and construed in accordance with the laws of the specified jurisdiction, without giving effect to any choice of law or conflict of law provisions. The Parties consent to the exclusive jurisdiction and venue in the courts of the specified jurisdiction for any disputes arising out of or relating to this Agreement."""
        }

        return standard_clauses.get(clause_id, f"Standard clause content for {clause_id}")

    def _format_date(self, date_value: Union[str, datetime, None]) -> str:
        """Format date for display in documents."""
        if not date_value:
            return "Not specified"

        if isinstance(date_value, str):
            try:
                date_obj = datetime.fromisoformat(date_value.replace('Z', '+00:00'))
            except:
                return date_value
        else:
            date_obj = date_value

        return date_obj.strftime("%B %d, %Y")

    async def _generate_pdf(
        self,
        context: Dict[str, Any],
        template_name: Optional[str] = None,
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate PDF document using WeasyPrint or ReportLab fallback."""

        if WEASYPRINT_AVAILABLE:
            return await self._generate_pdf_weasyprint(context, template_name, export_options)
        else:
            return await self._generate_pdf_reportlab(context, template_name, export_options)

    async def _generate_pdf_weasyprint(
        self,
        context: Dict[str, Any],
        template_name: Optional[str] = None,
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate PDF document using WeasyPrint."""

        # Get HTML content
        html_content = self._render_html_template(context, template_name or "contract_template.html")

        # CSS styling
        css_content = self._get_pdf_css(context["branding"], export_options)

        # Generate PDF
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            try:
                html_doc = HTML(string=html_content)
                css_doc = CSS(string=css_content, font_config=self.font_config)

                html_doc.write_pdf(
                    temp_file.name,
                    stylesheets=[css_doc],
                    font_config=self.font_config
                )

                # Upload to storage
                file_info = await self._upload_generated_file(
                    temp_file.name,
                    f"{context['contract']['title']}.pdf",
                    "application/pdf",
                    context["contract"]["workspace_id"]
                )

                return file_info

            finally:
                # Clean up temp file
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

    async def _generate_pdf_reportlab(
        self,
        context: Dict[str, Any],
        template_name: Optional[str] = None,
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate PDF document using ReportLab as fallback."""

        contract = context["contract"]
        branding = context["branding"]

        # Generate PDF
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            try:
                # Create PDF document
                doc = SimpleDocTemplate(
                    temp_file.name,
                    pagesize=A4,
                    rightMargin=72,
                    leftMargin=72,
                    topMargin=72,
                    bottomMargin=72
                )

                # Build content
                story = []
                styles = getSampleStyleSheet()

                # Custom styles
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontSize=20,
                    spaceAfter=30,
                    alignment=TA_CENTER,
                    textColor=colors.HexColor(branding.get("color_scheme", {}).get("primary", "#1e293b"))
                )

                heading_style = ParagraphStyle(
                    'CustomHeading',
                    parent=styles['Heading2'],
                    fontSize=16,
                    spaceAfter=12,
                    textColor=colors.HexColor(branding.get("color_scheme", {}).get("primary", "#1e293b"))
                )

                # Add letterhead if enabled
                if branding.get("letterhead", True):
                    company_style = ParagraphStyle(
                        'CompanyName',
                        parent=styles['Normal'],
                        fontSize=18,
                        alignment=TA_CENTER,
                        textColor=colors.HexColor(branding.get("color_scheme", {}).get("primary", "#1e293b")),
                        spaceAfter=20
                    )
                    story.append(Paragraph(branding.get("company_name", "LegalAI"), company_style))
                    story.append(Spacer(1, 12))

                # Add title
                story.append(Paragraph(contract["title"], title_style))
                story.append(Spacer(1, 20))

                # Add contract metadata
                metadata_data = [
                    ["Contract Type:", contract.get("type", "N/A")],
                    ["Effective Date:", contract.get("effective_date_formatted", "N/A")],
                    ["Contract Number:", contract.get("contract_number", "N/A")],
                    ["Jurisdiction:", contract.get("jurisdiction", "N/A")]
                ]

                metadata_table = Table(metadata_data, colWidths=[2*inch, 4*inch])
                metadata_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), colors.HexColor("#f8fafc")),
                    ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 12),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ]))

                story.append(metadata_table)
                story.append(Spacer(1, 20))

                # Add parties section
                story.append(Paragraph("Parties", heading_style))
                story.append(Spacer(1, 12))

                for party in contract.get("formatted_parties", []):
                    party_title = f"Party {party['letter']}: {party['name']}"
                    story.append(Paragraph(party_title, styles['Heading3']))

                    party_details = [
                        f"<b>Type:</b> {party['type'].title()}",
                        f"<b>Address:</b> {party.get('address', 'N/A')}"
                    ]

                    if party.get('representative'):
                        party_details.append(f"<b>Representative:</b> {party['representative']}")

                    for detail in party_details:
                        story.append(Paragraph(detail, styles['Normal']))

                    story.append(Spacer(1, 12))

                # Add terms section
                story.append(Paragraph("Terms and Conditions", heading_style))
                story.append(Spacer(1, 12))

                # Add description if available
                if contract.get("description"):
                    story.append(Paragraph("<b>Description:</b>", styles['Heading4']))
                    story.append(Paragraph(contract["description"], styles['Normal']))
                    story.append(Spacer(1, 12))

                # Add clauses
                for clause in contract.get("formatted_clauses", []):
                    clause_title = f"{clause['number']}. {clause['title']}"
                    story.append(Paragraph(clause_title, styles['Heading4']))
                    story.append(Paragraph(clause['content'], styles['Normal']))
                    story.append(Spacer(1, 12))

                # Add signature section
                story.append(Spacer(1, 20))
                story.append(Paragraph("Signatures", heading_style))
                story.append(Spacer(1, 12))

                story.append(Paragraph(
                    "IN WITNESS WHEREOF, the parties hereto have executed this Agreement as of the Effective Date first written above.",
                    styles['Normal']
                ))
                story.append(Spacer(1, 20))

                # Add signature blocks
                for party in contract.get("formatted_parties", []):
                    signature_data = [
                        [f"Party {party['letter']}: {party['name']}", ""],
                        ["Signature: ________________________", "Date: ________________________"],
                        [f"Name: {party['name']}", f"Title: {party.get('title', 'N/A')}"]
                    ]

                    signature_table = Table(signature_data, colWidths=[3*inch, 3*inch])
                    signature_table.setStyle(TableStyle([
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ]))

                    story.append(signature_table)
                    story.append(Spacer(1, 20))

                # Add footer
                footer_text = branding.get("footer_text", "Generated by LegalAI Contract Management System")
                footer_style = ParagraphStyle(
                    'Footer',
                    parent=styles['Normal'],
                    fontSize=10,
                    alignment=TA_CENTER,
                    textColor=colors.HexColor(branding.get("color_scheme", {}).get("secondary", "#64748b"))
                )
                story.append(Spacer(1, 30))
                story.append(Paragraph(footer_text, footer_style))
                story.append(Paragraph(f"Generated on: {context['generation']['formatted_timestamp']}", footer_style))

                # Build PDF
                doc.build(story)

                # Upload to storage
                file_info = await self._upload_generated_file(
                    temp_file.name,
                    f"{context['contract']['title']}.pdf",
                    "application/pdf",
                    context["contract"]["workspace_id"]
                )

                return file_info

            finally:
                # Clean up temp file
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

    async def _generate_docx(
        self,
        context: Dict[str, Any],
        template_name: Optional[str] = None,
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate DOCX document."""

        doc = DocxDocument()

        # Set document styles
        self._setup_docx_styles(doc, context["branding"])

        # Add letterhead if enabled
        if context["branding"].get("letterhead", True):
            self._add_docx_letterhead(doc, context)

        # Add title
        title = doc.add_heading(context["contract"]["title"], 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add contract metadata
        self._add_docx_metadata(doc, context)

        # Add parties section
        self._add_docx_parties(doc, context)

        # Add clauses
        self._add_docx_clauses(doc, context)

        # Add signature section
        self._add_docx_signatures(doc, context)

        # Save to temp file and upload
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
            try:
                doc.save(temp_file.name)

                file_info = await self._upload_generated_file(
                    temp_file.name,
                    f"{context['contract']['title']}.docx",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    context["contract"]["workspace_id"]
                )

                return file_info

            finally:
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

    async def _generate_html(
        self,
        context: Dict[str, Any],
        template_name: Optional[str] = None,
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate HTML document."""

        html_content = self._render_html_template(context, template_name or "contract_template.html")

        # Add CSS styling
        css_content = self._get_html_css(context["branding"], export_options)
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{context['contract']['title']}</title>
            <style>{css_content}</style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """

        # Save to temp file and upload
        with tempfile.NamedTemporaryFile(mode='w', suffix=".html", delete=False, encoding='utf-8') as temp_file:
            try:
                temp_file.write(full_html)
                temp_file.flush()

                file_info = await self._upload_generated_file(
                    temp_file.name,
                    f"{context['contract']['title']}.html",
                    "text/html",
                    context["contract"]["workspace_id"]
                )

                return file_info

            finally:
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

    async def _generate_txt(
        self,
        context: Dict[str, Any],
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate plain text document."""

        content_lines = []
        contract = context["contract"]

        # Title and metadata
        content_lines.extend([
            "=" * 60,
            contract["title"].upper().center(60),
            "=" * 60,
            "",
            f"Contract Type: {contract.get('type', 'N/A')}",
            f"Effective Date: {contract.get('effective_date_formatted', 'N/A')}",
            f"Jurisdiction: {contract.get('jurisdiction', 'N/A')}",
            "",
            "-" * 60,
            "PARTIES",
            "-" * 60,
            ""
        ])

        # Parties
        for party in contract.get("formatted_parties", []):
            content_lines.extend([
                f"Party {party['letter']}: {party['name']}",
                f"Type: {party['type'].title()}",
                f"Address: {party.get('address', 'N/A')}",
                ""
            ])

        # Contract terms
        content_lines.extend([
            "-" * 60,
            "TERMS AND CONDITIONS",
            "-" * 60,
            ""
        ])

        if contract.get("description"):
            content_lines.extend([
                "Description:",
                contract["description"],
                ""
            ])

        # Clauses
        for clause in contract.get("formatted_clauses", []):
            content_lines.extend([
                f"{clause['number']}. {clause['title']}",
                clause['content'],
                ""
            ])

        # Footer
        content_lines.extend([
            "=" * 60,
            f"Generated by {context['generation']['generator']}",
            f"Generated on: {context['generation']['formatted_timestamp']}",
            "=" * 60
        ])

        full_content = "\n".join(content_lines)

        # Save to temp file and upload
        with tempfile.NamedTemporaryFile(mode='w', suffix=".txt", delete=False, encoding='utf-8') as temp_file:
            try:
                temp_file.write(full_content)
                temp_file.flush()

                file_info = await self._upload_generated_file(
                    temp_file.name,
                    f"{context['contract']['title']}.txt",
                    "text/plain",
                    context["contract"]["workspace_id"]
                )

                return file_info

            finally:
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

    async def _generate_markdown(
        self,
        context: Dict[str, Any],
        export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate Markdown document."""

        content_lines = []
        contract = context["contract"]

        # Title and metadata
        content_lines.extend([
            f"# {contract['title']}",
            "",
            f"**Contract Type:** {contract.get('type', 'N/A')}  ",
            f"**Effective Date:** {contract.get('effective_date_formatted', 'N/A')}  ",
            f"**Jurisdiction:** {contract.get('jurisdiction', 'N/A')}  ",
            "",
            "---",
            "",
            "## Parties",
            ""
        ])

        # Parties
        for party in contract.get("formatted_parties", []):
            content_lines.extend([
                f"### Party {party['letter']}: {party['name']}",
                "",
                f"- **Type:** {party['type'].title()}",
                f"- **Address:** {party.get('address', 'N/A')}",
                ""
            ])

        # Contract terms
        content_lines.extend([
            "---",
            "",
            "## Terms and Conditions",
            ""
        ])

        if contract.get("description"):
            content_lines.extend([
                "### Description",
                "",
                contract["description"],
                ""
            ])

        # Clauses
        content_lines.append("### Clauses")
        content_lines.append("")

        for clause in contract.get("formatted_clauses", []):
            content_lines.extend([
                f"#### {clause['number']}. {clause['title']}",
                "",
                clause['content'],
                ""
            ])

        # Footer
        content_lines.extend([
            "---",
            "",
            f"*Generated by {context['generation']['generator']}*  ",
            f"*Generated on: {context['generation']['formatted_timestamp']}*"
        ])

        full_content = "\n".join(content_lines)

        # Save to temp file and upload
        with tempfile.NamedTemporaryFile(mode='w', suffix=".md", delete=False, encoding='utf-8') as temp_file:
            try:
                temp_file.write(full_content)
                temp_file.flush()

                file_info = await self._upload_generated_file(
                    temp_file.name,
                    f"{context['contract']['title']}.md",
                    "text/markdown",
                    context["contract"]["workspace_id"]
                )

                return file_info

            finally:
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

    def _render_html_template(self, context: Dict[str, Any], template_name: str) -> str:
        """Render HTML template with context data."""

        # Try to load custom template first, fall back to default
        try:
            template = self.jinja_env.get_template(template_name)
            return template.render(**context)
        except:
            # Use default template if custom template not found
            return self._get_default_html_template(context)

    def _get_default_html_template(self, context: Dict[str, Any]) -> str:
        """Get default HTML template for contracts."""

        contract = context["contract"]
        branding = context["branding"]

        # Build parties HTML
        parties_html = ""
        for party in contract.get("formatted_parties", []):
            parties_html += f"""
            <div class="party-block">
                <h3>Party {party['letter']}: {party['name']}</h3>
                <p><strong>Type:</strong> {party['type'].title()}</p>
                <p><strong>Address:</strong> {party.get('address', 'N/A')}</p>
                {f"<p><strong>Representative:</strong> {party['representative']}</p>" if party.get('representative') else ""}
            </div>
            """

        # Build clauses HTML
        clauses_html = ""
        for clause in contract.get("formatted_clauses", []):
            clauses_html += f"""
            <div class="clause">
                <h4>{clause['number']}. {clause['title']}</h4>
                <p>{clause['content']}</p>
            </div>
            """

        return f"""
        <div class="contract-document">
            {self._get_letterhead_html(context) if branding.get('letterhead') else ''}

            <div class="contract-header">
                <h1>{contract['title']}</h1>
                <div class="contract-meta">
                    <p><strong>Contract Type:</strong> {contract.get('type', 'N/A')}</p>
                    <p><strong>Effective Date:</strong> {contract.get('effective_date_formatted', 'N/A')}</p>
                    <p><strong>Contract Number:</strong> {contract.get('contract_number', 'N/A')}</p>
                </div>
            </div>

            <div class="parties-section">
                <h2>Parties</h2>
                {parties_html}
            </div>

            <div class="terms-section">
                <h2>Terms and Conditions</h2>
                {f"<div class='description'><p>{contract['description']}</p></div>" if contract.get('description') else ''}
                {clauses_html}
            </div>

            <div class="signature-section">
                <h2>Signatures</h2>
                <div class="signature-blocks">
                    {self._get_signature_blocks_html(contract.get('formatted_parties', []))}
                </div>
            </div>

            <div class="footer">
                <p>{branding.get('footer_text', '')}</p>
                <p>Generated on: {context['generation']['formatted_timestamp']}</p>
            </div>
        </div>
        """

    def _get_letterhead_html(self, context: Dict[str, Any]) -> str:
        """Generate letterhead HTML."""
        branding = context["branding"]

        return f"""
        <div class="letterhead">
            {f"<img src='{branding['company_logo']}' alt='Company Logo' class='logo'>" if branding.get('company_logo') else ''}
            <h2 class="company-name">{branding.get('company_name', 'LegalAI')}</h2>
        </div>
        """

    def _get_signature_blocks_html(self, parties: List[Dict[str, Any]]) -> str:
        """Generate signature blocks HTML."""

        signature_html = ""
        for party in parties:
            signature_html += f"""
            <div class="signature-block">
                <h4>Party {party['letter']}: {party['name']}</h4>
                <div class="signature-line">
                    <p>Signature: _________________________</p>
                    <p>Date: _________________________</p>
                    {f"<p>Name: {party['name']}</p>" if party.get('name') else ''}
                    {f"<p>Title: {party['title']}</p>" if party.get('title') else ''}
                </div>
            </div>
            """

        return signature_html

    def _get_pdf_css(self, branding: Dict[str, Any], export_options: Optional[Dict[str, Any]] = None) -> str:
        """Generate CSS for PDF documents."""

        colors = branding.get("color_scheme", {})
        fonts = branding.get("fonts", {})

        return f"""
        @page {{
            size: A4;
            margin: 1in;
            @top-center {{
                content: "{branding.get('company_name', 'LegalAI')}";
                font-size: 10pt;
                color: {colors.get('secondary', '#64748b')};
            }}
            @bottom-center {{
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10pt;
                color: {colors.get('secondary', '#64748b')};
            }}
        }}

        body {{
            font-family: {fonts.get('body', 'Times New Roman, serif')};
            font-size: 12pt;
            line-height: 1.6;
            color: {colors.get('primary', '#1e293b')};
            margin: 0;
            padding: 0;
        }}

        .letterhead {{
            text-align: center;
            margin-bottom: 2em;
            border-bottom: 2px solid {colors.get('primary', '#1e293b')};
            padding-bottom: 1em;
        }}

        .company-name {{
            font-size: 18pt;
            font-weight: bold;
            color: {colors.get('primary', '#1e293b')};
            margin: 0;
        }}

        .logo {{
            max-height: 60px;
            margin-bottom: 10px;
        }}

        h1 {{
            font-family: {fonts.get('heading', 'Arial, sans-serif')};
            font-size: 20pt;
            font-weight: bold;
            text-align: center;
            color: {colors.get('primary', '#1e293b')};
            margin: 1em 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        h2 {{
            font-family: {fonts.get('heading', 'Arial, sans-serif')};
            font-size: 16pt;
            font-weight: bold;
            color: {colors.get('primary', '#1e293b')};
            margin: 1.5em 0 0.5em 0;
            border-bottom: 1px solid {colors.get('secondary', '#64748b')};
            padding-bottom: 0.2em;
        }}

        h3, h4 {{
            font-family: {fonts.get('heading', 'Arial, sans-serif')};
            color: {colors.get('primary', '#1e293b')};
            margin: 1em 0 0.5em 0;
        }}

        .contract-meta {{
            text-align: center;
            margin: 1em 0;
            padding: 1em;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
        }}

        .party-block {{
            margin: 1em 0;
            padding: 1em;
            border: 1px solid #e2e8f0;
            background-color: #f8fafc;
        }}

        .clause {{
            margin: 1.5em 0;
            padding: 1em;
            border-left: 3px solid {colors.get('accent', '#3b82f6')};
            padding-left: 1.5em;
        }}

        .signature-section {{
            margin-top: 3em;
            page-break-inside: avoid;
        }}

        .signature-block {{
            margin: 2em 0;
            padding: 1em;
            border: 1px solid #e2e8f0;
            display: inline-block;
            width: 45%;
            vertical-align: top;
        }}

        .signature-line p {{
            margin: 0.5em 0;
            border-bottom: 1px solid #000;
            padding-bottom: 0.2em;
            min-height: 1.5em;
        }}

        .footer {{
            margin-top: 2em;
            text-align: center;
            font-size: 10pt;
            color: {colors.get('secondary', '#64748b')};
            border-top: 1px solid #e2e8f0;
            padding-top: 1em;
        }}
        """

    def _get_html_css(self, branding: Dict[str, Any], export_options: Optional[Dict[str, Any]] = None) -> str:
        """Generate CSS for HTML documents."""

        colors = branding.get("color_scheme", {})
        fonts = branding.get("fonts", {})

        return f"""
        body {{
            font-family: {fonts.get('body', 'Times New Roman, serif')};
            font-size: 14px;
            line-height: 1.6;
            color: {colors.get('primary', '#1e293b')};
            max-width: 800px;
            margin: 0 auto;
            padding: 2em;
            background-color: #ffffff;
        }}

        .contract-document {{
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 2em;
        }}

        .letterhead {{
            text-align: center;
            margin-bottom: 2em;
            border-bottom: 2px solid {colors.get('primary', '#1e293b')};
            padding-bottom: 1em;
        }}

        .company-name {{
            font-size: 24px;
            font-weight: bold;
            color: {colors.get('primary', '#1e293b')};
            margin: 0;
        }}

        h1 {{
            font-family: {fonts.get('heading', 'Arial, sans-serif')};
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            color: {colors.get('primary', '#1e293b')};
            margin: 1em 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        h2 {{
            font-family: {fonts.get('heading', 'Arial, sans-serif')};
            font-size: 20px;
            font-weight: bold;
            color: {colors.get('primary', '#1e293b')};
            margin: 2em 0 1em 0;
            border-bottom: 2px solid {colors.get('accent', '#3b82f6')};
            padding-bottom: 0.5em;
        }}

        .contract-meta {{
            text-align: center;
            margin: 2em 0;
            padding: 1.5em;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }}

        .party-block {{
            margin: 1.5em 0;
            padding: 1.5em;
            border: 1px solid #e2e8f0;
            background-color: #f8fafc;
            border-radius: 8px;
        }}

        .clause {{
            margin: 2em 0;
            padding: 1.5em;
            border-left: 4px solid {colors.get('accent', '#3b82f6')};
            background-color: #f8fafc;
            border-radius: 0 8px 8px 0;
        }}

        .signature-blocks {{
            display: flex;
            flex-wrap: wrap;
            gap: 2em;
            margin-top: 2em;
        }}

        .signature-block {{
            flex: 1;
            min-width: 300px;
            padding: 1.5em;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: #f8fafc;
        }}

        .signature-line p {{
            margin: 1em 0;
            border-bottom: 2px solid #000;
            padding-bottom: 0.5em;
            min-height: 2em;
        }}

        .footer {{
            margin-top: 3em;
            text-align: center;
            font-size: 12px;
            color: {colors.get('secondary', '#64748b')};
            border-top: 1px solid #e2e8f0;
            padding-top: 1em;
        }}

        @media print {{
            body {{ margin: 0; padding: 1in; }}
            .contract-document {{ box-shadow: none; }}
        }}
        """

    def _setup_docx_styles(self, doc: DocxDocument, branding: Dict[str, Any]) -> None:
        """Setup custom styles for DOCX document."""

        styles = doc.styles

        # Create custom heading style
        try:
            heading_style = styles.add_style('CustomHeading', WD_STYLE_TYPE.PARAGRAPH)
            heading_font = heading_style.font
            heading_font.name = branding.get("fonts", {}).get("heading", "Arial")
            heading_font.size = Pt(16)
            heading_font.bold = True
        except:
            pass  # Style might already exist

    def _add_docx_letterhead(self, doc: DocxDocument, context: Dict[str, Any]) -> None:
        """Add letterhead to DOCX document."""

        branding = context["branding"]

        # Add company name as header
        header_para = doc.add_paragraph()
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = header_para.add_run(branding.get("company_name", "LegalAI"))
        run.font.size = Pt(18)
        run.font.bold = True

        # Add separator
        doc.add_paragraph("_" * 60).alignment = WD_ALIGN_PARAGRAPH.CENTER

    def _add_docx_metadata(self, doc: DocxDocument, context: Dict[str, Any]) -> None:
        """Add contract metadata to DOCX document."""

        contract = context["contract"]

        # Contract metadata table
        table = doc.add_table(rows=4, cols=2)
        table.style = 'Table Grid'

        # Add metadata rows
        metadata_items = [
            ("Contract Type:", contract.get("type", "N/A")),
            ("Effective Date:", contract.get("effective_date_formatted", "N/A")),
            ("Jurisdiction:", contract.get("jurisdiction", "N/A")),
            ("Contract Number:", contract.get("contract_number", "N/A"))
        ]

        for i, (label, value) in enumerate(metadata_items):
            row = table.rows[i]
            row.cells[0].text = label
            row.cells[1].text = value

            # Make label bold
            for paragraph in row.cells[0].paragraphs:
                for run in paragraph.runs:
                    run.font.bold = True

    def _add_docx_parties(self, doc: DocxDocument, context: Dict[str, Any]) -> None:
        """Add parties section to DOCX document."""

        doc.add_heading("Parties", level=2)

        for party in context["contract"].get("formatted_parties", []):
            # Party heading
            party_heading = doc.add_paragraph()
            party_heading.add_run(f"Party {party['letter']}: {party['name']}").bold = True

            # Party details
            details = [
                f"Type: {party['type'].title()}",
                f"Address: {party.get('address', 'N/A')}"
            ]

            if party.get('representative'):
                details.append(f"Representative: {party['representative']}")

            for detail in details:
                doc.add_paragraph(detail, style='List Bullet')

    def _add_docx_clauses(self, doc: DocxDocument, context: Dict[str, Any]) -> None:
        """Add clauses section to DOCX document."""

        doc.add_heading("Terms and Conditions", level=2)

        # Add description if available
        if context["contract"].get("description"):
            doc.add_paragraph("Description:")
            doc.add_paragraph(context["contract"]["description"])

        # Add clauses
        for clause in context["contract"].get("formatted_clauses", []):
            # Clause heading
            clause_heading = doc.add_heading(f"{clause['number']}. {clause['title']}", level=3)

            # Clause content
            doc.add_paragraph(clause['content'])

    def _add_docx_signatures(self, doc: DocxDocument, context: Dict[str, Any]) -> None:
        """Add signature section to DOCX document."""

        doc.add_heading("Signatures", level=2)

        doc.add_paragraph("IN WITNESS WHEREOF, the parties hereto have executed this Agreement as of the Effective Date first written above.")

        # Add signature blocks
        for party in context["contract"].get("formatted_parties", []):
            doc.add_paragraph()  # Add space

            # Party name
            party_para = doc.add_paragraph()
            party_para.add_run(f"Party {party['letter']}: {party['name']}").bold = True

            # Signature lines
            doc.add_paragraph("Signature: _________________________")
            doc.add_paragraph("Date: _________________________")

            if party.get('name'):
                doc.add_paragraph(f"Name: {party['name']}")
            if party.get('title'):
                doc.add_paragraph(f"Title: {party['title']}")

    async def _upload_generated_file(
        self,
        file_path: str,
        filename: str,
        content_type: str,
        workspace_id: str
    ) -> Dict[str, Any]:
        """Upload generated file to storage and return file info."""

        try:
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Create a mock UploadFile object for StorageService
            class MockUploadFile:
                def __init__(self, content: bytes, filename: str, content_type: str):
                    self.content = content
                    self.filename = filename
                    self.content_type = content_type
                    self._position = 0

                async def read(self) -> bytes:
                    return self.content

                async def seek(self, position: int) -> None:
                    self._position = position

            mock_file = MockUploadFile(file_content, filename, content_type)

            # Upload to storage
            upload_result = await StorageService.upload_file(
                file=mock_file,
                workspace_id=workspace_id,
                folder="generated_documents"
            )

            return {
                "id": upload_result["id"],
                "filename": filename,
                "content_type": content_type,
                "size": len(file_content),
                "url": upload_result["url"],
                "path": upload_result["path"],
                "generated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise Exception(f"Failed to upload generated file: {str(e)}")

    async def generate_batch_documents(
        self,
        contracts: List[Dict[str, Any]],
        format_type: str = "pdf",
        branding_options: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Generate multiple contract documents in batch."""

        results = []

        for contract_data in contracts:
            try:
                result = await self.generate_contract_document(
                    contract_data=contract_data,
                    format_type=format_type,
                    branding_options=branding_options
                )
                results.append({
                    "contract_id": contract_data.get("id"),
                    "success": True,
                    "file_info": result
                })
            except Exception as e:
                results.append({
                    "contract_id": contract_data.get("id"),
                    "success": False,
                    "error": str(e)
                })

        return results