import { OrganizationMembershipResource } from "@clerk/types";

// Define the permission structure
export interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

// Define the role structure
export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isSystem?: boolean;
}

// Define the default permissions
export const DEFAULT_PERMISSIONS: Permission[] = [
  { id: "perm-1", name: "contracts.view", description: "View contracts", category: "contracts" },
  { id: "perm-2", name: "contracts.create", description: "Create contracts", category: "contracts" },
  { id: "perm-3", name: "contracts.edit", description: "Edit contracts", category: "contracts" },
  { id: "perm-4", name: "contracts.delete", description: "Delete contracts", category: "contracts" },
  { id: "perm-5", name: "contracts.approve", description: "Approve contracts", category: "contracts" },
  { id: "perm-6", name: "templates.view", description: "View templates", category: "templates" },
  { id: "perm-7", name: "templates.create", description: "Create templates", category: "templates" },
  { id: "perm-8", name: "templates.edit", description: "Edit templates", category: "templates" },
  { id: "perm-9", name: "templates.delete", description: "Delete templates", category: "templates" },
  { id: "perm-10", name: "approvals.manage", description: "Manage approval workflows", category: "approvals" },
  { id: "perm-11", name: "users.view", description: "View users", category: "users" },
  { id: "perm-12", name: "users.invite", description: "Invite users", category: "users" },
  { id: "perm-13", name: "users.manage", description: "Manage users", category: "users" },
  { id: "perm-14", name: "roles.manage", description: "Manage roles", category: "roles" },
  { id: "perm-15", name: "analytics.view", description: "View analytics", category: "analytics" },
  { id: "perm-16", name: "workspace.manage", description: "Manage workspace settings", category: "workspace" },
];

// Define the default roles
export const DEFAULT_ROLES: Role[] = [
  {
    id: "role-admin",
    name: "Administrator",
    description: "Full system access",
    permissions: DEFAULT_PERMISSIONS.map(p => p.id),
    isSystem: true,
  },
  {
    id: "role-manager",
    name: "Contract Manager",
    description: "Manage contracts and templates",
    permissions: [
      "perm-1", "perm-2", "perm-3", "perm-4", "perm-5",
      "perm-6", "perm-7", "perm-8", "perm-9", "perm-10",
      "perm-15",
    ],
  },
  {
    id: "role-reviewer",
    name: "Legal Reviewer",
    description: "Review and approve contracts",
    permissions: ["perm-1", "perm-3", "perm-5", "perm-6", "perm-9"],
  },
  {
    id: "role-readonly",
    name: "Read Only",
    description: "View-only access",
    permissions: ["perm-1", "perm-6", "perm-9", "perm-15"],
  },
];

// Get role ID from Clerk organization membership
export function getRoleIdFromMembership(membership: OrganizationMembershipResource): string {
  // Check if the role is stored in the publicMetadata
  const metadata = membership.publicMetadata || {};
  const roleId = metadata.roleId as string;
  
  // If no role is assigned, use the default role based on the membership role
  if (!roleId) {
    // Map Clerk's built-in roles to our custom roles
    if (membership.role === 'org:admin') {
      return 'role-admin';
    } else if (membership.role === 'org:member') {
      return 'role-readonly';
    }
    return 'role-readonly'; // Default fallback
  }
  
  return roleId;
}

// Get role from role ID
export function getRoleById(roleId: string, roles: Role[]): Role | undefined {
  return roles.find(role => role.id === roleId);
}

// Check if a user has a specific permission
export function hasPermission(
  roleId: string, 
  permissionId: string, 
  roles: Role[]
): boolean {
  const role = getRoleById(roleId, roles);
  if (!role) return false;
  return role.permissions.includes(permissionId);
}

// Check if a user has any of the specified permissions
export function hasAnyPermission(
  roleId: string, 
  permissionIds: string[], 
  roles: Role[]
): boolean {
  const role = getRoleById(roleId, roles);
  if (!role) return false;
  return permissionIds.some(permId => role.permissions.includes(permId));
}

// Check if a user has all of the specified permissions
export function hasAllPermissions(
  roleId: string, 
  permissionIds: string[], 
  roles: Role[]
): boolean {
  const role = getRoleById(roleId, roles);
  if (!role) return false;
  return permissionIds.every(permId => role.permissions.includes(permId));
}

// Get all permissions for a role
export function getRolePermissions(
  roleId: string, 
  roles: Role[], 
  permissions: Permission[]
): Permission[] {
  const role = getRoleById(roleId, roles);
  if (!role) return [];
  return permissions.filter(perm => role.permissions.includes(perm.id));
}

// Group permissions by category
export function groupPermissionsByCategory(permissions: Permission[]): Record<string, Permission[]> {
  return permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);
}

// Initialize roles for a new organization
export async function initializeOrganizationRoles(organization: any): Promise<void> {
  try {
    // Check if roles are already initialized
    const metadata = organization.publicMetadata || {};
    if (metadata.rolesInitialized) {
      return;
    }
    
    // Update the organization metadata with default roles
    await organization.update({
      publicMetadata: {
        ...metadata,
        roles: DEFAULT_ROLES,
        permissions: DEFAULT_PERMISSIONS,
        rolesInitialized: true,
      }
    });
  } catch (error) {
    console.error("Error initializing organization roles:", error);
    throw error;
  }
}
