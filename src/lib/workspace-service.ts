import { useWorkspaceStore } from './workspace-store';
import { WorkspaceService as APIWorkspaceService } from '../services/api-services';
import { Workspace } from './workspace-provider';

// Request deduplication map
const activeRequests = new Map<string, Promise<any>>();

// Request cancellation map
const abortControllers = new Map<string, AbortController>();

/**
 * Centralized workspace service with request deduplication and caching
 */
export class WorkspaceService {
  private static instance: WorkspaceService;
  
  static getInstance(): WorkspaceService {
    if (!WorkspaceService.instance) {
      WorkspaceService.instance = new WorkspaceService();
    }
    return WorkspaceService.instance;
  }

  /**
   * Fetch workspaces with deduplication and caching
   */
  async fetchWorkspaces(getToken: () => Promise<string | null>, force = false): Promise<Workspace[]> {
    const store = useWorkspaceStore.getState();
    const requestId = 'fetch-workspaces';

    // Check if we should fetch
    if (!force && !store.shouldFetch()) {
      console.log('🔄 WorkspaceService: Using cached data, skipping fetch');
      return store.workspaces;
    }

    // Check if request is already active
    if (activeRequests.has(requestId)) {
      console.log('🔄 WorkspaceService: Request already active, waiting for result');
      return activeRequests.get(requestId)!;
    }

    // Cancel any previous request
    this.cancelRequest(requestId);

    // Create new abort controller
    const abortController = new AbortController();
    abortControllers.set(requestId, abortController);

    // Mark request as active
    store.addActiveRequest(requestId);

    if (!force) {
      store.setLoading(true);
    } else {
      store.setRefreshing(true);
    }

    console.log('🔄 WorkspaceService: Starting workspace fetch...');

    const requestPromise = this.performFetch(abortController.signal, getToken);
    activeRequests.set(requestId, requestPromise);

    try {
      const workspaces = await requestPromise;

      // Update store with results
      store.setWorkspaces(workspaces);
      console.log('✅ WorkspaceService: Successfully fetched workspaces:', workspaces.length);

      return workspaces;
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('🚫 WorkspaceService: Request was cancelled');
        throw error;
      }

      console.error('❌ WorkspaceService: Failed to fetch workspaces:', error);
      store.setError(error.message || 'Failed to fetch workspaces');
      throw error;
    } finally {
      // Cleanup
      store.removeActiveRequest(requestId);
      store.setLoading(false);
      store.setRefreshing(false);
      activeRequests.delete(requestId);
      abortControllers.delete(requestId);
    }
  }

  /**
   * Perform the actual API fetch
   */
  private async performFetch(signal: AbortSignal, getToken: () => Promise<string | null>): Promise<Workspace[]> {
    // Get auth token
    const token = await getToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    // Make API call
    const response = await APIWorkspaceService.getWorkspaces(token);

    // Check if request was cancelled
    if (signal.aborted) {
      throw new Error('Request was cancelled');
    }

    if (!response.data) {
      throw new Error('No workspace data received from backend');
    }

    // Map backend workspace data to frontend format
    const workspaces: Workspace[] = response.data.map((workspace: any) => ({
      id: workspace.id,
      name: workspace.name,
      description: workspace.description || '',
      members: workspace.members || 0,
      contracts: workspace.contracts || 0,
      createdBy: workspace.created_by || "Unknown",
      createdDate: workspace.created_at
        ? new Date(workspace.created_at).toISOString().split('T')[0]
        : new Date().toISOString().split('T')[0],
      isActive: false, // Will be set by the workspace provider
    }));

    return workspaces;
  }

  /**
   * Cancel a specific request
   */
  cancelRequest(requestId: string): void {
    const controller = abortControllers.get(requestId);
    if (controller) {
      controller.abort();
      abortControllers.delete(requestId);
      activeRequests.delete(requestId);
      console.log(`🚫 WorkspaceService: Cancelled request: ${requestId}`);
    }
  }

  /**
   * Cancel all active requests
   */
  cancelAllRequests(): void {
    console.log('🚫 WorkspaceService: Cancelling all active requests');
    abortControllers.forEach((controller, requestId) => {
      controller.abort();
      console.log(`🚫 WorkspaceService: Cancelled request: ${requestId}`);
    });
    abortControllers.clear();
    activeRequests.clear();
    
    // Clear active requests from store
    const store = useWorkspaceStore.getState();
    store.reset();
  }

  /**
   * Refresh workspaces (force fetch)
   */
  async refreshWorkspaces(getToken: () => Promise<string | null>): Promise<Workspace[]> {
    return this.fetchWorkspaces(getToken, true);
  }

  /**
   * Get cached workspaces
   */
  getCachedWorkspaces(): Workspace[] {
    return useWorkspaceStore.getState().workspaces;
  }

  /**
   * Check if workspaces are loading
   */
  isLoading(): boolean {
    const store = useWorkspaceStore.getState();
    return store.isLoading || store.activeRequests.size > 0;
  }
}

// Export singleton instance
export const workspaceService = WorkspaceService.getInstance();
