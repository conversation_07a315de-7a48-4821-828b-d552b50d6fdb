import React from 'react';
import { ThemeProvider } from './theme-provider';

interface LandingThemeProviderProps {
  children: React.ReactNode;
}

/**
 * Landing page theme provider that forces light mode
 * This ensures the landing page always uses light theme for consistent marketing appearance
 * while preserving the user's theme preference for the authenticated application
 */
export const LandingThemeProvider: React.FC<LandingThemeProviderProps> = ({ children }) => {
  return (
    <ThemeProvider 
      defaultTheme="light" 
      storageKey="legalai-theme"
      forcedTheme="light"
    >
      {children}
    </ThemeProvider>
  );
};
