import { useState, useEffect, useCallback } from 'react';
import { useError } from './error-provider';
import { useLoading } from './loading-provider';
import { api, ApiResponse } from './api';
import { useAuth } from '@clerk/clerk-react';

interface UseApiQueryOptions<T> {
  /**
   * The API endpoint to call
   */
  endpoint: string;

  /**
   * Additional fetch options
   */
  options?: RequestInit;

  /**
   * Whether to fetch data automatically on mount
   * @default true
   */
  autoFetch?: boolean;

  /**
   * Loading message to display
   * @default "Loading..."
   */
  loadingMessage?: string;

  /**
   * Error message to display if the API call fails
   * @default "Failed to fetch data"
   */
  errorMessage?: string;

  /**
   * Transform the API response before returning it
   */
  transform?: (data: T) => any;

  /**
   * Dependencies that trigger a refetch when changed
   */
  deps?: any[];
}

/**
 * Hook for making API calls with built-in loading and error handling
 */
export function useApiQuery<T = any>(options: UseApiQueryOptions<T>) {
  const {
    endpoint,
    options: fetchOptions,
    autoFetch = true,
    loadingMessage = "Loading...",
    errorMessage = "Failed to fetch data",
    transform,
    deps = [],
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(autoFetch);
  const [error, setError] = useState<any>(null);
  const { handleApiError } = useError();
  const { startLoading, stopLoading } = useLoading();
  const { getToken } = useAuth();

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      startLoading(loadingMessage);

      // Get authentication token
      const token = await getToken();

      const response = await api.get<T>(endpoint, fetchOptions, token);
      const responseData = response.data;

      // Transform the data if a transform function is provided
      const transformedData = transform ? transform(responseData) : responseData;
      setData(transformedData);

      return transformedData;
    } catch (err) {
      setError(err);
      handleApiError(err, errorMessage);
      return null;
    } finally {
      setIsLoading(false);
      stopLoading();
    }
  }, [endpoint, JSON.stringify(fetchOptions), loadingMessage, errorMessage, transform]);

  // Fetch data on mount or when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [autoFetch, fetchData, ...deps]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
    setData,
  };
}

/**
 * Hook for making API mutations (POST, PUT, DELETE) with built-in loading and error handling
 */
export function useApiMutation<T = any, R = any>(
  method: 'post' | 'put' | 'delete',
  endpoint: string,
  options?: {
    loadingMessage?: string;
    errorMessage?: string;
    onSuccess?: (data: T) => void;
    onError?: (error: any) => void;
  }
) {
  const {
    loadingMessage = "Processing...",
    errorMessage = "Operation failed",
    onSuccess,
    onError,
  } = options || {};

  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<any>(null);
  const { handleApiError } = useError();
  const { startLoading, stopLoading } = useLoading();
  const { getToken } = useAuth();

  const mutate = async (payload?: R): Promise<T | null> => {
    try {
      setIsLoading(true);
      setError(null);
      startLoading(loadingMessage);

      // Get authentication token
      const token = await getToken();

      let response: ApiResponse<T>;

      if (method === 'delete') {
        response = await api.delete<T>(endpoint, {}, token);
      } else if (method === 'put') {
        response = await api.put<T>(endpoint, payload, {}, token);
      } else {
        response = await api.post<T>(endpoint, payload, {}, token);
      }

      setData(response.data);
      onSuccess?.(response.data);
      return response.data;
    } catch (err) {
      setError(err);
      handleApiError(err, errorMessage);
      onError?.(err);
      return null;
    } finally {
      setIsLoading(false);
      stopLoading();
    }
  };

  return {
    mutate,
    data,
    isLoading,
    error,
    reset: () => {
      setData(null);
      setError(null);
    },
  };
}
