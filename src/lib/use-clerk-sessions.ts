import { useState, useEffect } from 'react';
import { useUser, useClerk } from '@clerk/clerk-react';

export interface SessionData {
  id: string;
  deviceType: string;
  browser: string;
  os: string;
  location: string;
  lastActiveAt: Date;
  isCurrent: boolean;
}

export const useClerkSessions = () => {
  const { user, isLoaded } = useUser();
  const { client, session } = useClerk();
  const [sessions, setSessions] = useState<SessionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Load sessions from Clerk
  const loadSessions = async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get all sessions for the user
      const userSessions = await user.getSessions();

      // Format session data
      const formattedSessions = userSessions.map(s => {
        // Extract browser and OS info from user agent
        const userAgent = s.lastActiveAt || '';
        const browser = getBrowserInfo(typeof userAgent === 'string' ? userAgent : '');
        const os = getOSInfo(typeof userAgent === 'string' ? userAgent : '');

        // Get device type
        const deviceType = getDeviceType(typeof userAgent === 'string' ? userAgent : '');

        // Get location info
        const city = 'Unknown';
        const country = '';
        const location = country ? `${city}, ${country}` : city;

        // Format last active time
        const lastActiveAt = new Date(s.lastActiveAt);

        // Check if this is the current session
        const isCurrent = s.id === session?.id;

        return {
          id: s.id,
          deviceType,
          browser,
          os,
          location,
          lastActiveAt,
          isCurrent,
        };
      });

      setSessions(formattedSessions);
    } catch (err) {
      console.error('Error loading sessions:', err);
      setError('Failed to load sessions');
    } finally {
      setIsLoading(false);
    }
  };

  // Load sessions when user is loaded
  useEffect(() => {
    if (isLoaded && user) {
      loadSessions();
    } else if (isLoaded && !user) {
      setIsLoading(false);
      setError('User not authenticated');
    }
  }, [isLoaded, user]);

  // Revoke a specific session
  const revokeSession = async (sessionId: string) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get all sessions
      const sessions = await user.getSessions();

      // Find the session to revoke
      const sessionToRevoke = sessions.find(s => s.id === sessionId);

      if (!sessionToRevoke) {
        throw new Error('Session not found');
      }

      // Revoke the session
      await sessionToRevoke.revoke();

      // Reload sessions
      await loadSessions();

      setSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error revoking session:', err);
      setError('Failed to revoke session');
    } finally {
      setIsLoading(false);
    }
  };

  // Revoke all other sessions
  const revokeAllOtherSessions = async () => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Revoke all other sessions
      await user.getSessions().then(sessions => {
        return Promise.all(
          sessions
            .filter(s => s.id !== session?.id)
            .map(s => s.revoke())
        );
      });

      // Reload sessions
      await loadSessions();

      setSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error revoking all sessions:', err);
      setError('Failed to revoke all sessions');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    sessions,
    isLoading,
    error,
    success,
    loadSessions,
    revokeSession,
    revokeAllOtherSessions,
  };
};

// Helper functions to parse user agent
function getBrowserInfo(userAgent: string): string {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('MSIE') || userAgent.includes('Trident/')) return 'Internet Explorer';
  return 'Unknown Browser';
}

function getOSInfo(userAgent: string): string {
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Mac')) return 'macOS';
  if (userAgent.includes('Linux')) return 'Linux';
  if (userAgent.includes('Android')) return 'Android';
  if (userAgent.includes('iOS') || userAgent.includes('iPhone') || userAgent.includes('iPad')) return 'iOS';
  return 'Unknown OS';
}

function getDeviceType(userAgent: string): string {
  if (userAgent.includes('Mobile')) return 'Mobile';
  if (userAgent.includes('Tablet')) return 'Tablet';
  return 'Desktop';
}

// Helper function to format date
export function formatLastActive(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) return 'Just now';
  if (diffMin < 60) return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  if (diffHour < 24) return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  if (diffDay < 7) return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;

  return date.toLocaleDateString();
}
