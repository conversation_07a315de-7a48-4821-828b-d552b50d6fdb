import React, { createContext, useContext, useState, useEffect } from "react";

export interface Workspace {
  id: string;
  name: string;
  description?: string;
  members?: number;
  contracts?: number;
  createdBy?: string;
  createdDate?: string;
  isActive?: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  initials: string;
  workspaces: string[]; // Array of workspace IDs the user belongs to
  workspaceRoles?: Record<string, string>; // Map of workspace IDs to role IDs
}

interface WorkspaceContextType {
  currentWorkspace: Workspace | null;
  setCurrentWorkspace: (workspace: Workspace) => void;
  currentUser: User | null;
  setCurrentUser: (user: User) => void;
  userWorkspaces: Workspace[];
  setUserWorkspaces: (workspaces: Workspace[]) => void;
  isUserInWorkspace: (workspaceId: string) => boolean;
  isContentInWorkspace: (workspaceId: string | undefined) => boolean;

  // CRUD operations for workspaces
  createWorkspace: (workspace: Omit<Workspace, "id">) => Workspace;
  updateWorkspace: (id: string, workspace: Partial<Workspace>) => Workspace | null;
  deleteWorkspace: (id: string) => boolean;
  getWorkspaceById: (id: string) => Workspace | undefined;

  // User-workspace relationship functions
  getUserWorkspaces: () => Workspace[];
  canAccessWorkspace: (workspaceId: string) => boolean;
  canAccessContent: (workspaceId: string | undefined) => boolean;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

export const WorkspaceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Mock current user data - in a real app, this would come from authentication
  const [currentUser, setCurrentUser] = useState<User | null>({
    id: "user-1",
    name: "John Doe",
    email: "<EMAIL>",
    role: "admin",
    initials: "JD",
    workspaces: ["1", "2"] // User belongs to workspaces with IDs "1" and "2"
  });

  // Mock workspaces data - in a real app, this would come from an API
  const [userWorkspaces, setUserWorkspaces] = useState<Workspace[]>([
    {
      id: "1",
      name: "Legal Team",
      description: "Main workspace for legal team operations",
      members: 12,
      contracts: 45,
      createdBy: "John Doe",
      createdDate: "2023-01-15",
      isActive: true,
    },
    {
      id: "2",
      name: "Corporate Contracts",
      description: "Corporate-level agreements and partnerships",
      members: 8,
      contracts: 32,
      createdBy: "Jane Smith",
      createdDate: "2023-02-20",
    },
  ]);

  // Set default current workspace to the first workspace in the user's list
  const [currentWorkspace, setCurrentWorkspaceState] = useState<Workspace | null>(null);

  // Wrapper for setCurrentWorkspace that checks if the user has access to the workspace
  const setCurrentWorkspace = (workspace: Workspace) => {
    // Only allow setting workspaces the user has access to
    if (currentUser && currentUser.workspaces.includes(workspace.id)) {
      setCurrentWorkspaceState(workspace);
    } else {
      console.warn(`User does not have access to workspace: ${workspace.id}`);
    }
  };

  // Check if user belongs to a workspace
  const isUserInWorkspace = (workspaceId: string): boolean => {
    return currentUser?.workspaces.includes(workspaceId) || false;
  };

  // Check if content belongs to the current workspace
  const isContentInWorkspace = (workspaceId: string | undefined): boolean => {
    if (!workspaceId || !currentWorkspace) return false;
    return workspaceId === currentWorkspace.id;
  };

  // CRUD operations for workspaces
  const createWorkspace = (workspaceData: Omit<Workspace, "id">): Workspace => {
    // Generate a unique ID - in a real app, this would be handled by the backend
    const newId = `ws-${Date.now()}`;

    // Create the new workspace with current user as creator
    const newWorkspace: Workspace = {
      id: newId,
      ...workspaceData,
      createdBy: currentUser?.name || "Unknown",
      createdDate: new Date().toISOString().split('T')[0],
      members: 1, // Start with just the creator
      contracts: 0, // Start with no contracts
    };

    // Add the workspace to the user's workspaces
    setUserWorkspaces([...userWorkspaces, newWorkspace]);

    // Add the workspace ID to the current user's workspaces
    if (currentUser) {
      setCurrentUser({
        ...currentUser,
        workspaces: [...currentUser.workspaces, newId]
      });
    }

    return newWorkspace;
  };

  const updateWorkspace = (id: string, workspaceData: Partial<Workspace>): Workspace | null => {
    // Find the workspace to update
    const workspaceIndex = userWorkspaces.findIndex(ws => ws.id === id);
    if (workspaceIndex === -1) return null;

    // Create the updated workspace
    const updatedWorkspace = {
      ...userWorkspaces[workspaceIndex],
      ...workspaceData
    };

    // Update the workspace in the list
    const updatedWorkspaces = [...userWorkspaces];
    updatedWorkspaces[workspaceIndex] = updatedWorkspace;
    setUserWorkspaces(updatedWorkspaces);

    // If the current workspace is being updated, update it as well
    if (currentWorkspace && currentWorkspace.id === id) {
      setCurrentWorkspace(updatedWorkspace);
    }

    return updatedWorkspace;
  };

  const deleteWorkspace = (id: string): boolean => {
    // Check if the workspace exists
    const workspaceIndex = userWorkspaces.findIndex(ws => ws.id === id);
    if (workspaceIndex === -1) return false;

    // Remove the workspace from the list
    const updatedWorkspaces = userWorkspaces.filter(ws => ws.id !== id);
    setUserWorkspaces(updatedWorkspaces);

    // Remove the workspace ID from the current user's workspaces
    if (currentUser) {
      setCurrentUser({
        ...currentUser,
        workspaces: currentUser.workspaces.filter(wsId => wsId !== id)
      });
    }

    // If the current workspace is being deleted, set the current workspace to the first available workspace
    if (currentWorkspace && currentWorkspace.id === id) {
      if (updatedWorkspaces.length > 0) {
        setCurrentWorkspace(updatedWorkspaces[0]);
      } else {
        setCurrentWorkspace(null);
      }
    }

    return true;
  };

  const getWorkspaceById = (id: string): Workspace | undefined => {
    return userWorkspaces.find(ws => ws.id === id);
  };

  // Get workspaces that the current user is a member of
  const getUserWorkspaces = (): Workspace[] => {
    if (!currentUser) return [];
    return userWorkspaces.filter(workspace =>
      currentUser.workspaces.includes(workspace.id)
    );
  };

  // Check if the user can access a specific workspace
  const canAccessWorkspace = (workspaceId: string): boolean => {
    if (!currentUser) return false;
    return currentUser.workspaces.includes(workspaceId);
  };

  // Check if the user can access content from a specific workspace
  const canAccessContent = (workspaceId: string | undefined): boolean => {
    if (!workspaceId || !currentUser) return false;
    return currentUser.workspaces.includes(workspaceId);
  };

  // Initialize current workspace when user or workspaces change
  useEffect(() => {
    if (userWorkspaces.length > 0 && !currentWorkspace) {
      // Only set workspaces the user has access to
      const accessibleWorkspaces = getUserWorkspaces();
      if (accessibleWorkspaces.length > 0) {
        // Use setCurrentWorkspaceState directly to bypass the access check
        // since we've already filtered for accessible workspaces
        setCurrentWorkspaceState(accessibleWorkspaces[0]);
      }
    }

    // If current workspace is set but user doesn't have access to it anymore,
    // reset to an accessible workspace or null
    if (currentWorkspace && currentUser && !currentUser.workspaces.includes(currentWorkspace.id)) {
      const accessibleWorkspaces = getUserWorkspaces();
      if (accessibleWorkspaces.length > 0) {
        setCurrentWorkspaceState(accessibleWorkspaces[0]);
      } else {
        setCurrentWorkspaceState(null);
      }
    }
  }, [userWorkspaces, currentWorkspace, currentUser]);

  return (
    <WorkspaceContext.Provider
      value={{
        currentWorkspace,
        setCurrentWorkspace,
        currentUser,
        setCurrentUser,
        userWorkspaces,
        setUserWorkspaces,
        isUserInWorkspace,
        isContentInWorkspace,
        createWorkspace,
        updateWorkspace,
        deleteWorkspace,
        getWorkspaceById,
        getUserWorkspaces,
        canAccessWorkspace,
        canAccessContent,
      }}
    >
      {children}
    </WorkspaceContext.Provider>
  );
};

export const useWorkspace = () => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error("useWorkspace must be used within a WorkspaceProvider");
  }
  return context;
};
