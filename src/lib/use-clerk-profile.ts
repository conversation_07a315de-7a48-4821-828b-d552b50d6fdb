import { useState, useEffect } from 'react';
import { useUser, useClerk } from '@clerk/clerk-react';

export interface NotificationPreferences {
  emailApprovals: boolean;
  emailUpdates: boolean;
  emailReminders: boolean;
  emailComments: boolean;
  systemApprovals: boolean;
  browserNotifications: boolean;
  emailDigestFrequency: string;
}

export interface ClerkProfileData {
  firstName: string;
  lastName: string;
  email: string;
  title: string;
  company: string;
  timezone: string;
  bio: string;
  workspace: string;
  notifications?: NotificationPreferences;
}

export const useClerkProfile = () => {
  const { user, isLoaded } = useUser();
  const { client } = useClerk();
  const [profileData, setProfileData] = useState<ClerkProfileData>({
    firstName: '',
    lastName: '',
    email: '',
    title: '',
    company: '',
    timezone: '',
    bio: '',
    workspace: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Load user data from Clerk
  useEffect(() => {
    if (isLoaded && user) {
      // Get the primary email address
      const email = user.primaryEmailAddress?.emailAddress || '';

      // Get public metadata for additional fields
      const publicMetadata = user.publicMetadata || {};

      // Default notification preferences
      const defaultNotifications: NotificationPreferences = {
        emailApprovals: true,
        emailUpdates: true,
        emailReminders: false,
        emailComments: true,
        systemApprovals: true,
        browserNotifications: false,
        emailDigestFrequency: 'daily',
      };

      // Get notification preferences from metadata or use defaults
      const notifications = (publicMetadata.notifications as NotificationPreferences) || defaultNotifications;

      setProfileData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: email,
        title: (publicMetadata.title as string) || '',
        company: (publicMetadata.company as string) || '',
        timezone: (publicMetadata.timezone as string) || '',
        bio: (publicMetadata.bio as string) || '',
        workspace: (publicMetadata.workspace as string) || '',
        notifications: notifications,
      });

      setIsLoading(false);
    } else if (isLoaded && !user) {
      setIsLoading(false);
      setError('User not authenticated');
    }
  }, [isLoaded, user]);

  // Save profile data to Clerk
  const saveProfile = async (data: ClerkProfileData) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Update the user's first and last name
      await user.update({
        firstName: data.firstName,
        lastName: data.lastName,
      });

      // Update public metadata for additional fields
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          title: data.title,
          company: data.company,
          timezone: data.timezone,
          bio: data.bio,
          workspace: data.workspace,
          notifications: data.notifications,
        },
      });

      // Update the profile data state
      setProfileData(data);
      setSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  // Save only notification preferences
  const saveNotificationPreferences = async (preferences: NotificationPreferences) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get current public metadata
      const currentMetadata = user.publicMetadata || {};

      // Update notification preferences in unsafe metadata
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          notifications: preferences,
        },
      });

      // Update the profile data state with new notification preferences
      setProfileData(prev => ({
        ...prev,
        notifications: preferences,
      }));

      setSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error updating notification preferences:', err);
      setError('Failed to update notification preferences');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    profileData,
    isLoading,
    error,
    success,
    saveProfile,
    saveNotificationPreferences,
  };
};
