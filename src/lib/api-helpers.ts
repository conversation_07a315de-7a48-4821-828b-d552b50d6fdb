import { useAuth } from '@clerk/clerk-react';
import { useApi, type ApiResponse } from './api';
import { useWorkspace } from './workspace-provider';

/**
 * Custom hook that provides authenticated API calls with workspace context
 */
export function useAuthenticatedApi() {
  const { fetch } = useApi();
  const { currentWorkspace } = useWorkspace();

  /**
   * Make an authenticated API call with automatic token injection
   */
  const fetchWithAuth = async <T>(
    apiCall: (token?: string) => Promise<ApiResponse<T>>,
    loadingMessage = "Loading...",
    errorMessage = "Failed to fetch data"
  ): Promise<T | null> => {
    return fetch(
      async (token) => {
        // Set workspace context if available
        if (currentWorkspace) {
          // This will be handled by the API service's workspace header
        }
        return apiCall(token);
      },
      loadingMessage,
      errorMessage
    );
  };

  /**
   * Helper to fetch data for the current workspace
   */
  const fetchForWorkspace = async <T>(
    apiCall: (workspaceId: string, token?: string) => Promise<ApiResponse<T>>,
    loadingMessage = "Loading...",
    errorMessage = "Failed to fetch data"
  ): Promise<T | null> => {
    if (!currentWorkspace) {
      console.warn("No workspace selected");
      return null;
    }

    return fetchWithAuth(
      (token) => apiCall(currentWorkspace.id, token),
      loadingMessage,
      errorMessage
    );
  };

  return {
    fetchWithAuth,
    fetchForWorkspace,
    currentWorkspace,
  };
}

/**
 * Helper function to ensure data is an array
 */
export function ensureArray<T>(data: T | T[] | null | undefined): T[] {
  if (!data) return [];
  return Array.isArray(data) ? data : [data];
}

/**
 * Helper function to transform API response data
 */
export function transformApiData<T, R>(
  data: T,
  transformer: (item: T) => R
): R {
  return transformer(data);
}

/**
 * Helper function to handle API errors consistently
 */
export function handleApiError(error: any, context: string = "API call") {
  console.error(`${context} failed:`, error);
  
  if (error?.status === 401) {
    console.warn("Authentication required - redirecting to login");
    // Handle authentication error
  } else if (error?.status === 403) {
    console.warn("Access denied - insufficient permissions");
    // Handle authorization error
  } else if (error?.status === 404) {
    console.warn("Resource not found");
    // Handle not found error
  } else {
    console.error("Unexpected error:", error);
  }
  
  return error;
}
