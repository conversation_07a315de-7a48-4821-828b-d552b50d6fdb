// API Types for the LegalAI application
// These types match the backend schemas

// Contract Types
export interface Party {
  type: string;  // 'individual' or 'company'
  name: string;
  address?: string;
  representative?: string;
  title?: string;
  role?: string;
}

export interface Clause {
  title: string;
  content: string;
}

export interface Attachment {
  name: string;
  file_path: string;
  file_type: string;
  size?: number;
  uploaded_at: string; // ISO date string
}

export interface Approver {
  user_id: string;
  name: string;
  email: string;
  status: string; // 'pending', 'approved', 'rejected'
  comments?: string;
  approved_at?: string; // ISO date string
}

export type ContractStatus =
  | 'draft'
  | 'active'
  | 'expired'
  | 'terminated'
  | 'pending_approval'
  | 'rejected';

export interface ContractBase {
  title: string;
  type: string;
  jurisdiction?: string;
  effective_date?: string; // ISO date string
  expiry_date?: string; // ISO date string
  description?: string;
  counterparty?: string;
  value?: string;
  currency?: string;
  status: ContractStatus;
  workspace_id: string;
}

export interface ContractCreate extends ContractBase {
  parties?: Party[];
  clauses?: Clause[];
  attachments?: Attachment[];
  approvers?: Approver[];
  approval_process?: 'sequential' | 'parallel';
  tags?: string[];
  custom_fields?: Record<string, any>;
}

export interface ContractUpdate {
  title?: string;
  type?: string;
  jurisdiction?: string;
  effective_date?: string; // ISO date string
  expiry_date?: string; // ISO date string
  description?: string;
  counterparty?: string;
  value?: string;
  currency?: string;
  status?: ContractStatus;
  parties?: Party[];
  clauses?: Clause[];
  attachments?: Attachment[];
  approvers?: Approver[];
  approval_process?: 'sequential' | 'parallel';
  tags?: string[];
  custom_fields?: Record<string, any>;
  workspace_id?: string;
}

export interface Contract extends ContractBase {
  id: string;
  created_by: {
    id: string;
    name: string;
  };
  created_at: string; // ISO date string
  updated_at?: string; // ISO date string
  parties?: Party[];
  clauses?: Clause[];
  attachments?: Attachment[];
  approvers?: Approver[];
  approval_process: 'sequential' | 'parallel';
  tags?: string[];
  custom_fields?: Record<string, any>;
  starred?: boolean;
  folder_id?: string;
}

// Template Types
export type ComplexityLevel = 'simple' | 'medium' | 'complex';

export interface TemplateBase {
  title: string;
  description: string;
  type: string;
  complexity: ComplexityLevel;
  industry?: string;
  tags?: string[];
  icon?: string;
}

export interface TemplateCreate extends TemplateBase {
  content: Record<string, any>; // Template content structure
  workspace_id: string;
}

export interface TemplateUpdate {
  title?: string;
  description?: string;
  type?: string;
  complexity?: ComplexityLevel;
  industry?: string;
  tags?: string[];
  icon?: string;
  content?: Record<string, any>;
  workspace_id?: string;
}

export interface Template extends TemplateBase {
  id: string;
  created_by: {
    id: string;
    name: string;
  };
  created_at: string; // ISO date string
  updated_at?: string; // ISO date string
  usage_count: number;
  rating?: number;
  is_user_created: boolean;
  folder_id?: string;
  content: Record<string, any>; // Template content structure
  workspace_id: string;
}

// Workspace Types
export interface WorkspaceBase {
  name: string;
  description?: string;
  settings?: Record<string, any>;
}

export interface WorkspaceCreate extends WorkspaceBase {}

export interface WorkspaceUpdate {
  name?: string;
  description?: string;
}

export interface Workspace extends WorkspaceBase {
  id: string;
  created_by: string;
  created_at: string; // ISO date string
  updated_at?: string; // ISO date string
  members?: number;
  contracts?: number;
  is_active: boolean;
  members_list?: User[]; // Only included when include_members=true
}

// AI Analysis Types
export interface AIAnalysisRisk {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  recommendation: string;
  location: string;
  context?: string;
}

export interface AIAnalysisSuggestion {
  id: string;
  title: string;
  description: string;
  target: string;
  severity: 'low' | 'medium' | 'high';
}

export interface AIAnalysisClause {
  id: string;
  title: string;
  content: string;
  location: string;
  category?: string;
  risk_level?: 'low' | 'medium' | 'high';
}

export interface AIAnalysisComplianceIssue {
  id: string;
  title: string;
  details: string;
  status: 'compliant' | 'non-compliant' | 'warning';
  regulation?: string;
}

export interface AIAnalysisObligation {
  id: string;
  title: string;
  due_date: string;
  status: 'pending' | 'completed' | 'overdue';
  assigned_to?: string;
  description: string;
}

export interface AIAnalysisResult {
  id: string;
  contract_id: string;
  risk_score: number;
  compliance_score: number;
  language_clarity: number;
  key_risks: AIAnalysisRisk[];
  suggestions: AIAnalysisSuggestion[];
  extracted_clauses: AIAnalysisClause[];
  compliance_issues: AIAnalysisComplianceIssue[];
  obligations: AIAnalysisObligation[];
  created_at: string;
  updated_at?: string;
}

export interface AIAnalysisRequest {
  contract_id: string;
  document_text?: string;
  options?: {
    include_suggestions?: boolean;
    include_compliance_check?: boolean;
    include_obligations?: boolean;
    regulations?: string[];
  };
}

// Analytics Types
export interface DashboardSummary {
  total_contracts: number;
  pending_approvals: number;
  expiring_soon: number;
  compliance_rate: number;
  workspace_id: string;
}

export interface ContractActivity {
  month: string;
  created: number;
  completed: number;
}

export interface ContractTypeDistribution {
  type: string;
  count: number;
  color?: string;
}

export interface RecentActivity {
  id: string;
  type: string;
  contract_id?: string;
  contract_name?: string;
  user_id: string;
  user_name: string;
  timestamp: string;
  details?: string;
}

export interface AnalyticsSummary {
  total_contracts: number;
  active_contracts: number;
  pending_approvals: number;
  compliance_rate: number;
  change_from_last_month: number;
  change_from_last_week: number;
}

export interface PerformanceMetric {
  name: string;
  value: string | number;
  change: string;
}

// Role and Permission Types
export interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  is_system?: boolean;
  user_count?: number;
  workspace_id: string;
}

export interface RoleCreate {
  name: string;
  description: string;
  permissions: string[];
  workspace_id: string;
}

export interface RoleUpdate {
  name?: string;
  description?: string;
  permissions?: string[];
}

// User Types
export interface NotificationPreferences {
  email_approvals: boolean;
  email_updates: boolean;
  email_reminders: boolean;
  email_comments: boolean;
  system_approvals: boolean;
  browser_notifications: boolean;
  email_digest_frequency: string; // 'daily', 'weekly', 'never'
}

export interface UserBase {
  email: string;
  first_name: string;
  last_name: string;
  title?: string;
  company?: string;
  timezone?: string;
  bio?: string;
}

export interface UserUpdate {
  first_name?: string;
  last_name?: string;
  title?: string;
  company?: string;
  timezone?: string;
  bio?: string;
  notification_preferences?: NotificationPreferences;
}

export interface User extends UserBase {
  id: string;
  created_at: string; // ISO date string
  updated_at?: string; // ISO date string
  workspaces: string[]; // List of workspace IDs
  workspace_roles: Record<string, string>; // Map of workspace IDs to role IDs
  notification_preferences: NotificationPreferences;
  avatar?: string;
  initials: string;
  permissions?: string[]; // Only included in UserWithPermissions
}

export interface WorkspaceMember {
  user_id: string;
  workspace_id: string;
  role_id: string;
  joined_at: string;
  status: 'active' | 'inactive' | 'pending';
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    initials: string;
  };
}

// Document Types
export interface FileInfo {
  filename: string;
  content_type: string;
  size: number;
  path: string;
  url: string;
  uploaded_at: string; // ISO date string
}

export interface DocumentSigner {
  id: string;
  document_id: string;
  name: string;
  email: string;
  role: string;
  status: string; // 'pending', 'completed', 'declined', 'current'
  order: number;
  avatar?: string;
  completed_at?: string; // ISO date string
  declined_at?: string; // ISO date string
  decline_reason?: string;
  signature_data?: Record<string, any>;
}

export interface DocumentBase {
  title: string;
  filename: string;
  workspace_id: string;
  content?: string;
  file_url?: string;
  file_path?: string;
  file_info?: FileInfo;
  folder?: string;
  expires_at?: string; // ISO date string
}

export interface DocumentCreate extends DocumentBase {}

export interface DocumentUpdate {
  title?: string;
  filename?: string;
  workspace_id?: string;
  content?: string;
  file_url?: string;
  file_path?: string;
  file_info?: FileInfo;
  folder?: string;
  status?: string;
  expires_at?: string; // ISO date string
}

export interface Document extends DocumentBase {
  id: string;
  created_at: string; // ISO date string
  created_by: string;
  updated_at?: string; // ISO date string
  status: string; // 'draft', 'in_progress', 'completed', 'declined'
  signers?: DocumentSigner[];
}
