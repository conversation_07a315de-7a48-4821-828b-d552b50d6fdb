import { useState, useMemo, useCallback } from "react";

export interface UseGridStateOptions<T> {
  data: T[];
  getItemId?: (item: T) => string;
  defaultSelected?: string[];
  filterFn?: (item: T) => boolean;
  sortFn?: (a: T, b: T) => number;
}

export interface UseGridStateReturn<T> {
  // Data
  filteredData: T[];
  setFilterFn: (fn: (item: T) => boolean) => void;
  setSortFn: (fn: (a: T, b: T) => number) => void;
  
  // Selection
  selectedItems: string[];
  setSelectedItems: (items: string[]) => void;
  isItemSelected: (itemId: string) => boolean;
  toggleItemSelection: (itemId: string) => void;
  selectItem: (itemId: string) => void;
  deselectItem: (itemId: string) => void;
  selectAll: () => void;
  deselectAll: () => void;
  toggleSelectAll: () => void;
  
  // Computed states
  allSelected: boolean;
  someSelected: boolean;
  selectedCount: number;
  
  // Handlers for grid component
  handleItemSelect: (itemId: string, selected: boolean) => void;
  handleSelectAll: (selected: boolean) => void;
}

export function useGridState<T extends Record<string, any>>({
  data,
  getItemId = (item: T) => item.id,
  defaultSelected = [],
  filterFn,
  sortFn,
}: UseGridStateOptions<T>): UseGridStateReturn<T> {
  const [selectedItems, setSelectedItems] = useState<string[]>(defaultSelected);
  const [currentFilterFn, setFilterFn] = useState<((item: T) => boolean) | undefined>(filterFn);
  const [currentSortFn, setSortFn] = useState<((a: T, b: T) => number) | undefined>(sortFn);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let result = data;

    // Apply filter
    if (currentFilterFn) {
      result = result.filter(currentFilterFn);
    }

    // Apply sort
    if (currentSortFn) {
      result = [...result].sort(currentSortFn);
    }

    return result;
  }, [data, currentFilterFn, currentSortFn]);

  // Selection utilities
  const isItemSelected = useCallback((itemId: string) => {
    return selectedItems.includes(itemId);
  }, [selectedItems]);

  const toggleItemSelection = useCallback((itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  }, []);

  const selectItem = useCallback((itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) ? prev : [...prev, itemId]
    );
  }, []);

  const deselectItem = useCallback((itemId: string) => {
    setSelectedItems(prev => prev.filter(id => id !== itemId));
  }, []);

  const selectAll = useCallback(() => {
    setSelectedItems(filteredData.map(getItemId));
  }, [filteredData, getItemId]);

  const deselectAll = useCallback(() => {
    setSelectedItems([]);
  }, []);

  const toggleSelectAll = useCallback(() => {
    if (selectedItems.length === filteredData.length) {
      deselectAll();
    } else {
      selectAll();
    }
  }, [selectedItems.length, filteredData.length, selectAll, deselectAll]);

  // Computed states
  const allSelected = filteredData.length > 0 && selectedItems.length === filteredData.length;
  const someSelected = selectedItems.length > 0 && selectedItems.length < filteredData.length;
  const selectedCount = selectedItems.length;

  // Handlers for grid component
  const handleItemSelect = useCallback((itemId: string, selected: boolean) => {
    if (selected) {
      selectItem(itemId);
    } else {
      deselectItem(itemId);
    }
  }, [selectItem, deselectItem]);

  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      selectAll();
    } else {
      deselectAll();
    }
  }, [selectAll, deselectAll]);

  return {
    // Data
    filteredData,
    setFilterFn,
    setSortFn,
    
    // Selection
    selectedItems,
    setSelectedItems,
    isItemSelected,
    toggleItemSelection,
    selectItem,
    deselectItem,
    selectAll,
    deselectAll,
    toggleSelectAll,
    
    // Computed states
    allSelected,
    someSelected,
    selectedCount,
    
    // Handlers
    handleItemSelect,
    handleSelectAll,
  };
}
