import { useState, useMemo, useCallback, useEffect } from "react";
import { SearchQuery } from "@/components/ui/enhanced-search";
import { FilterValues } from "@/components/ui/enhanced-filter-panel";

export interface SearchAndFilterOptions<T> {
  data: T[];
  searchFields?: string[];
  filterFunctions?: Record<string, (item: T, value: any) => boolean>;
  defaultSearch?: SearchQuery;
  defaultFilters?: FilterValues;
  debounceMs?: number;
}

export interface SearchAndFilterState {
  search: SearchQuery;
  filters: FilterValues;
  recentSearches: string[];
  savedFilters: { name: string; values: FilterValues }[];
}

export interface SearchAndFilterActions {
  setSearch: (search: SearchQuery) => void;
  setFilters: (filters: FilterValues) => void;
  clearSearch: () => void;
  clearFilters: () => void;
  clearAll: () => void;
  addRecentSearch: (query: string) => void;
  saveFilter: (name: string, values: FilterValues) => void;
  loadFilter: (name: string) => void;
  removeFilter: (name: string) => void;
}

export interface UseSearchAndFilterReturn<T> {
  // Data
  filteredData: T[];
  
  // State
  state: SearchAndFilterState;
  
  // Actions
  actions: SearchAndFilterActions;
  
  // Computed
  hasActiveFilters: boolean;
  hasActiveSearch: boolean;
  totalResults: number;
  
  // Utilities
  getSearchSuggestions: () => string[];
}

export function useSearchAndFilter<T extends Record<string, any>>({
  data,
  searchFields = ["title", "name"],
  filterFunctions = {},
  defaultSearch = { global: "" },
  defaultFilters = {},
  debounceMs = 300,
}: SearchAndFilterOptions<T>): UseSearchAndFilterReturn<T> {
  
  // State
  const [search, setSearch] = useState<SearchQuery>(defaultSearch);
  const [filters, setFilters] = useState<FilterValues>(defaultFilters);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [savedFilters, setSavedFilters] = useState<{ name: string; values: FilterValues }[]>([]);

  // Load saved state from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem("searchAndFilter");
      if (saved) {
        const parsed = JSON.parse(saved);
        setRecentSearches(parsed.recentSearches || []);
        setSavedFilters(parsed.savedFilters || []);
      }
    } catch (error) {
      console.warn("Failed to load search and filter state:", error);
    }
  }, []);

  // Save state to localStorage
  useEffect(() => {
    try {
      localStorage.setItem("searchAndFilter", JSON.stringify({
        recentSearches,
        savedFilters,
      }));
    } catch (error) {
      console.warn("Failed to save search and filter state:", error);
    }
  }, [recentSearches, savedFilters]);

  // Search function
  const searchData = useCallback((items: T[], query: SearchQuery): T[] => {
    if (!query.global && (!query.fields || Object.keys(query.fields).length === 0)) {
      return items;
    }

    return items.filter(item => {
      let matches = true;

      // Global search
      if (query.global) {
        const globalMatch = searchFields.some(field => {
          const value = item[field];
          if (typeof value === "string") {
            return value.toLowerCase().includes(query.global!.toLowerCase());
          }
          return false;
        });
        
        if (query.operator === "OR") {
          matches = globalMatch;
        } else {
          matches = matches && globalMatch;
        }
      }

      // Field-specific search
      if (query.fields && Object.keys(query.fields).length > 0) {
        const fieldMatches = Object.entries(query.fields).map(([field, value]) => {
          const itemValue = item[field];
          if (typeof itemValue === "string" && value) {
            return itemValue.toLowerCase().includes(value.toLowerCase());
          }
          return !value; // Empty field search matches all
        });

        const fieldMatch = query.operator === "OR" 
          ? fieldMatches.some(Boolean)
          : fieldMatches.every(Boolean);

        if (query.operator === "OR") {
          matches = matches || fieldMatch;
        } else {
          matches = matches && fieldMatch;
        }
      }

      return matches;
    });
  }, [searchFields]);

  // Filter function
  const filterData = useCallback((items: T[], filterValues: FilterValues): T[] => {
    if (Object.keys(filterValues).length === 0) {
      return items;
    }

    return items.filter(item => {
      return Object.entries(filterValues).every(([key, value]) => {
        // Skip empty/null values
        if (value === null || value === undefined || value === "") {
          return true;
        }

        // Array values (empty arrays are considered inactive)
        if (Array.isArray(value) && value.length === 0) {
          return true;
        }

        // Use custom filter function if available
        if (filterFunctions[key]) {
          return filterFunctions[key](item, value);
        }

        // Default filtering logic
        const itemValue = item[key];

        // Array filter (multiselect)
        if (Array.isArray(value)) {
          if (Array.isArray(itemValue)) {
            return value.some(v => itemValue.includes(v));
          }
          return value.includes(itemValue);
        }

        // Date range filter
        if (value && typeof value === "object" && (value.from || value.to)) {
          const itemDate = new Date(itemValue);
          const fromMatch = !value.from || itemDate >= value.from;
          const toMatch = !value.to || itemDate <= value.to;
          return fromMatch && toMatch;
        }

        // Boolean filter
        if (typeof value === "boolean") {
          return value ? !!itemValue : true;
        }

        // String filter
        if (typeof value === "string") {
          return String(itemValue).toLowerCase().includes(value.toLowerCase());
        }

        // Exact match
        return itemValue === value;
      });
    });
  }, [filterFunctions]);

  // Filtered data
  const filteredData = useMemo(() => {
    let result = data;
    
    // Apply search
    result = searchData(result, search);
    
    // Apply filters
    result = filterData(result, filters);
    
    return result;
  }, [data, search, filters, searchData, filterData]);

  // Actions
  const actions: SearchAndFilterActions = {
    setSearch: useCallback((newSearch: SearchQuery) => {
      setSearch(newSearch);
      
      // Add to recent searches if it's a global search
      if (newSearch.global && newSearch.global.trim()) {
        addRecentSearch(newSearch.global.trim());
      }
    }, []),

    setFilters: useCallback((newFilters: FilterValues) => {
      setFilters(newFilters);
    }, []),

    clearSearch: useCallback(() => {
      setSearch({ global: "", fields: {}, operator: search.operator });
    }, [search.operator]),

    clearFilters: useCallback(() => {
      setFilters({});
    }, []),

    clearAll: useCallback(() => {
      setSearch({ global: "", fields: {}, operator: search.operator });
      setFilters({});
    }, [search.operator]),

    addRecentSearch: useCallback((query: string) => {
      setRecentSearches(prev => {
        const filtered = prev.filter(s => s !== query);
        return [query, ...filtered].slice(0, 10); // Keep last 10
      });
    }, []),

    saveFilter: useCallback((name: string, values: FilterValues) => {
      setSavedFilters(prev => {
        const filtered = prev.filter(f => f.name !== name);
        return [...filtered, { name, values }];
      });
    }, []),

    loadFilter: useCallback((name: string) => {
      const filter = savedFilters.find(f => f.name === name);
      if (filter) {
        setFilters(filter.values);
      }
    }, [savedFilters]),

    removeFilter: useCallback((name: string) => {
      setSavedFilters(prev => prev.filter(f => f.name !== name));
    }, []),
  };

  // Add recent search function
  const addRecentSearch = useCallback((query: string) => {
    setRecentSearches(prev => {
      const filtered = prev.filter(s => s !== query);
      return [query, ...filtered].slice(0, 10);
    });
  }, []);

  // Computed values
  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key];
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === "object" && value !== null) {
      return Object.keys(value).some(k => value[k]);
    }
    return value !== undefined && value !== null && value !== "";
  });

  const hasActiveSearch = !!(search.global || (search.fields && Object.keys(search.fields).length > 0));

  // Get search suggestions based on data
  const getSearchSuggestions = useCallback((): string[] => {
    const suggestions = new Set<string>();
    
    data.forEach(item => {
      searchFields.forEach(field => {
        const value = item[field];
        if (typeof value === "string" && value.length > 2) {
          // Add words from the field
          const words = value.split(/\s+/).filter(word => word.length > 2);
          words.forEach(word => suggestions.add(word));
        }
      });
    });

    return Array.from(suggestions).slice(0, 10);
  }, [data, searchFields]);

  return {
    filteredData,
    state: {
      search,
      filters,
      recentSearches,
      savedFilters,
    },
    actions,
    hasActiveFilters,
    hasActiveSearch,
    totalResults: filteredData.length,
    getSearchSuggestions,
  };
}
