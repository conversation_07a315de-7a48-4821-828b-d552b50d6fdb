// Analytics data interfaces and API functions
import { AnalyticsService } from '@/services/api-services';

export interface ContractActivityData {
  month: string;
  created: number;
  completed: number;
  workspaceId: string;
}

export interface ContractTypeData {
  type: string;
  count: number;
  color: string;
  workspaceId: string;
}

export interface AnalyticsSummary {
  totalContracts: number;
  activeContracts: number;
  pendingApprovals: number;
  complianceRate: number;
  changeFromLastMonth: number;
  changeFromLastWeek: number;
  workspaceId: string;
}

export interface PerformanceMetric {
  name: string;
  value: string | number;
  change: string;
  workspaceId: string;
}

// API functions to fetch analytics data

/**
 * Fetch contract activity data for a workspace
 */
export async function getContractActivityData(workspaceId: string, token?: string): Promise<ContractActivityData[]> {
  try {
    const response = await AnalyticsService.getContractActivity(workspaceId, '6months', token);
    return response.data.map(activity => ({
      month: activity.month,
      created: activity.created,
      completed: activity.completed,
      workspaceId: workspaceId
    }));
  } catch (error) {
    console.error("Error fetching contract activity data:", error);
    return [];
  }
}

/**
 * Fetch contract type distribution data for a workspace
 */
export async function getContractTypesData(workspaceId: string, token?: string): Promise<ContractTypeData[]> {
  try {
    const response = await AnalyticsService.getContractTypeDistribution(workspaceId, token);
    return response.data.map(typeData => ({
      type: typeData.type,
      count: typeData.count,
      color: typeData.color || "#3b82f6", // Default color if not provided
      workspaceId: workspaceId
    }));
  } catch (error) {
    console.error("Error fetching contract types data:", error);
    return [];
  }
}

/**
 * Fetch analytics summary for a workspace
 */
export async function getAnalyticsSummary(workspaceId: string, token?: string): Promise<AnalyticsSummary | null> {
  try {
    const response = await AnalyticsService.getAnalyticsSummary(workspaceId, token);
    return {
      totalContracts: response.data.total_contracts,
      activeContracts: response.data.active_contracts,
      pendingApprovals: response.data.pending_approvals,
      complianceRate: response.data.compliance_rate,
      changeFromLastMonth: response.data.change_from_last_month || 0,
      changeFromLastWeek: response.data.change_from_last_week || 0,
      workspaceId: workspaceId
    };
  } catch (error) {
    console.error("Error fetching analytics summary:", error);
    return null;
  }
}

/**
 * Fetch performance metrics for a workspace
 */
export async function getPerformanceMetrics(workspaceId: string, token?: string): Promise<PerformanceMetric[]> {
  try {
    const response = await AnalyticsService.getPerformanceMetrics(workspaceId, token);
    return response.data.map(metric => ({
      name: metric.name,
      value: metric.value,
      change: metric.change,
      workspaceId: workspaceId
    }));
  } catch (error) {
    console.error("Error fetching performance metrics:", error);
    return [];
  }
}
