import React, { useState, useEffect } from "react";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useApi } from "@/lib/api";
import { AnalyticsService } from "@/services/api-services";
import type { ContractTypeDistribution } from "@/services/api-types";
import { Loader2 } from "lucide-react";

// Define colors for chart segments
const COLORS = [
  "#3b82f6", // blue
  "#10b981", // green
  "#f59e0b", // amber
  "#8b5cf6", // purple
  "#ec4899", // pink
  "#06b6d4", // cyan
  "#ef4444", // red
  "#84cc16", // lime
  "#6366f1", // indigo
  "#f97316"  // orange
];

interface ContractTypesChartProps {
  data?: ContractTypeDistribution[];
}

const ContractTypesChart: React.FC<ContractTypesChartProps> = ({ data }) => {
  const { currentWorkspace } = useClerkWorkspace();
  const { fetch } = useApi();

  const [chartData, setChartData] = useState<Array<ContractTypeDistribution & { color: string }>>([]);
  const [loading, setLoading] = useState(!data);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // If data is provided via props, use it
    if (data) {
      // Add colors to the data
      const dataWithColors = data.map((item, index) => ({
        ...item,
        color: COLORS[index % COLORS.length]
      }));
      setChartData(dataWithColors);
      setLoading(false);
      return;
    }

    // Otherwise fetch from API
    const fetchTypeData = async () => {
      if (!currentWorkspace?.id) return;

      setLoading(true);
      setError(null);

      try {
        const result = await fetch(
          () => AnalyticsService.getContractTypeDistribution(currentWorkspace.id),
          "Loading contract types...",
          "Failed to load contract types"
        );

        if (result) {
          // Add colors to the data
          const dataWithColors = result.map((item, index) => ({
            ...item,
            color: COLORS[index % COLORS.length]
          }));
          setChartData(dataWithColors);
        }
      } catch (err) {
        console.error("Error fetching contract types:", err);
        setError("Failed to load contract types");
      } finally {
        setLoading(false);
      }
    };

    fetchTypeData();
  }, [currentWorkspace?.id, fetch, data]);

  // Show loading state
  if (loading) {
    return (
      <div className="w-full h-[300px] flex flex-col items-center justify-center">
        <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
        <p className="text-muted-foreground">Loading contract types...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full h-[300px] flex items-center justify-center">
        <p className="text-destructive">Error: {error}</p>
      </div>
    );
  }

  // If no data is available, show empty chart
  if (chartData.length === 0) {
    return (
      <div className="w-full h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">No data available for the current workspace</p>
      </div>
    );
  }

  const total = chartData.reduce((sum, item) => sum + item.count, 0);
  const radius = 80;

  // Calculate the segments
  let currentAngle = 0;
  const segments = chartData.map((item) => {
    const angle = (item.count / total) * 360;
    const startAngle = currentAngle;
    currentAngle += angle;

    return {
      ...item,
      percentage: (item.count / total) * 100,
      startAngle,
      angle,
    };
  });

  // Convert angle to SVG arc parameters
  const getArc = (startAngle: number, angle: number) => {
    const startRad = (startAngle - 90) * (Math.PI / 180);
    const endRad = (startAngle + angle - 90) * (Math.PI / 180);

    const x1 = radius * Math.cos(startRad) + 100;
    const y1 = radius * Math.sin(startRad) + 100;
    const x2 = radius * Math.cos(endRad) + 100;
    const y2 = radius * Math.sin(endRad) + 100;

    const largeArcFlag = angle > 180 ? 1 : 0;

    return `M 100 100 L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  return (
    <div className="w-full h-[300px] flex items-center justify-center">
      <div className="flex flex-col items-center">
        <div className="relative">
          <svg width="200" height="200" viewBox="0 0 200 200">
            {segments.map((segment) => (
              <path
                key={segment.type}
                d={getArc(segment.startAngle, segment.angle)}
                fill={segment.color}
                stroke="white"
                strokeWidth="1"
              />
            ))}
            <circle cx="100" cy="100" r="40" fill="white" />
          </svg>
        </div>

        <div className="mt-6 grid grid-cols-2 gap-x-8 gap-y-2">
          {segments.map((segment) => (
            <div key={segment.type} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-sm"
                style={{ backgroundColor: segment.color }}
              ></div>
              <span className="text-sm">{segment.type}</span>
              <span className="text-sm text-muted-foreground">
                ({segment.count}, {segment.percentage.toFixed(1)}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContractTypesChart;
