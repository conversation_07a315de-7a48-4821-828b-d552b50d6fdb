import React, { useState, useEffect } from "react";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useApi } from "@/lib/api";
import { AnalyticsService } from "@/services/api-services";
import type { ContractActivity } from "@/services/api-types";
import { Loader2 } from "lucide-react";

interface ContractActivityChartProps {
  data?: ContractActivity[];
  timeRange?: string;
}

const ContractActivityChart: React.FC<ContractActivityChartProps> = ({
  data,
  timeRange = '6months'
}) => {
  const { currentWorkspace } = useClerkWorkspace();
  const { fetch } = useApi();

  const [chartData, setChartData] = useState<ContractActivity[]>(data || []);
  const [loading, setLoading] = useState(!data);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // If data is provided via props, use it
    if (data) {
      setChartData(data);
      setLoading(false);
      return;
    }

    // Otherwise fetch from API
    const fetchActivityData = async () => {
      if (!currentWorkspace?.id) return;

      setLoading(true);
      setError(null);

      try {
        const result = await fetch(
          () => AnalyticsService.getContractActivity(currentWorkspace.id, timeRange),
          "Loading activity data...",
          "Failed to load activity data"
        );

        if (result) {
          setChartData(result);
        }
      } catch (err) {
        console.error("Error fetching activity data:", err);
        setError("Failed to load activity data");
      } finally {
        setLoading(false);
      }
    };

    fetchActivityData();
  }, [currentWorkspace?.id, fetch, data, timeRange]);

  // Show loading state
  if (loading) {
    return (
      <div className="w-full h-[300px] flex flex-col items-center justify-center">
        <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
        <p className="text-muted-foreground">Loading activity data...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full h-[300px] flex items-center justify-center">
        <p className="text-destructive">Error: {error}</p>
      </div>
    );
  }

  // If no data is available, show empty chart
  if (chartData.length === 0) {
    return (
      <div className="w-full h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">No data available for the current workspace</p>
      </div>
    );
  }

  const maxValue = Math.max(...chartData.flatMap(d => [d.created, d.completed]));
  const chartHeight = 250;
  const barWidth = 24;
  const barGap = 8;
  const groupWidth = (barWidth * 2) + barGap;
  const chartWidth = groupWidth * chartData.length;

  return (
    <div className="w-full h-[300px] flex items-center justify-center">
      <div className="relative h-full w-full flex flex-col">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 bottom-0 w-10 flex flex-col justify-between text-xs text-muted-foreground">
          <div>{maxValue}</div>
          <div>{Math.round(maxValue * 0.75)}</div>
          <div>{Math.round(maxValue * 0.5)}</div>
          <div>{Math.round(maxValue * 0.25)}</div>
          <div>0</div>
        </div>

        {/* Chart area */}
        <div className="ml-10 flex-1 flex items-end">
          <svg width={chartWidth} height={chartHeight} className="overflow-visible">
            {/* Horizontal grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
              <line
                key={ratio}
                x1={0}
                y1={chartHeight - (chartHeight * ratio)}
                x2={chartWidth}
                y2={chartHeight - (chartHeight * ratio)}
                stroke="#e5e7eb"
                strokeDasharray="4 4"
              />
            ))}

            {/* Bars */}
            {chartData.map((item, index) => {
              const createdHeight = (item.created / maxValue) * chartHeight;
              const completedHeight = (item.completed / maxValue) * chartHeight;
              const groupX = index * groupWidth;

              return (
                <g key={item.month} transform={`translate(${groupX}, 0)`}>
                  {/* Created bar */}
                  <rect
                    x={0}
                    y={chartHeight - createdHeight}
                    width={barWidth}
                    height={createdHeight}
                    rx={4}
                    fill="#3b82f6"
                    opacity={0.8}
                  />

                  {/* Completed bar */}
                  <rect
                    x={barWidth + barGap}
                    y={chartHeight - completedHeight}
                    width={barWidth}
                    height={completedHeight}
                    rx={4}
                    fill="#10b981"
                    opacity={0.8}
                  />

                  {/* Month label */}
                  <text
                    x={barWidth + (barGap / 2)}
                    y={chartHeight + 20}
                    textAnchor="middle"
                    fontSize={12}
                    fill="currentColor"
                    className="text-muted-foreground"
                  >
                    {item.month}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>

        {/* Legend */}
        <div className="mt-8 flex items-center justify-center gap-6">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-blue-500 opacity-80"></div>
            <span className="text-sm">Created</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-green-500 opacity-80"></div>
            <span className="text-sm">Completed</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractActivityChart;
