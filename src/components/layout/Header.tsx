import React from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Bell, ChevronDown, Menu, Search, LogOut, Settings } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ThemeToggle from "../theme/ThemeToggle";
import NotificationsDropdown from "../notifications/NotificationsDropdown";
import { useUser, useClerk } from "@clerk/clerk-react";

interface Workspace {
  id: string;
  name: string;
  description?: string;
  members?: number;
  isActive?: boolean;
}

interface HeaderProps {
  currentWorkspace: Workspace;
  onToggleSidebar: () => void;
  onWorkspaceChange: (workspace: Workspace) => void;
  pageTitle?: string;
}

const Header = ({
  currentWorkspace,
  onToggleSidebar,
  onWorkspaceChange,
  pageTitle,
}: HeaderProps) => {
  const navigate = useNavigate();
  const { user } = useUser();
  const { signOut } = useClerk();

  return (
    <header
      className="sticky top-0 z-30 flex h-16 items-center gap-2 sm:gap-4 border-b bg-background/95 backdrop-blur-sm px-3 sm:px-4 md:px-6 shadow-sm"
      style={{ paddingTop: 'env(safe-area-inset-top)' }}
    >
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden"
        onClick={onToggleSidebar}
        aria-label="Toggle sidebar menu"
      >
        <Menu className="h-5 w-5" />
      </Button>

      {/* Page title - visible on all screen sizes */}
      {pageTitle && (
        <h1 className="text-base font-medium truncate max-w-[200px] sm:max-w-xs">
          {pageTitle}
        </h1>
      )}

      {/* Mobile search button */}
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden"
        onClick={() => console.log("Mobile search")}
        aria-label="Search"
      >
        <Search className="h-5 w-5" />
      </Button>

      <div className="ml-auto flex items-center gap-2 sm:gap-4">
        {/* Desktop search */}
        <div className="relative hidden md:flex">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search contracts, clauses..."
            className="w-[300px] pl-8 h-9 bg-muted/50 border-muted focus:bg-background transition-colors"
          />
        </div>

        <NotificationsDropdown
          notifications={[
            {
              id: "notif-1",
              title: "Contract Approval Required",
              message: "Service Agreement with Acme Corp requires your approval",
              type: "approval",
              status: "unread",
              timestamp: "2023-07-15T10:30:00Z",
              actionUrl: "/contracts/123",
            },
            {
              id: "notif-2",
              title: "Contract Updated",
              message: "Jane Doe updated the NDA with XYZ Inc",
              type: "contract",
              status: "unread",
              timestamp: "2023-07-14T15:45:00Z",
              sender: {
                name: "Jane Doe",
                initials: "JD",
              },
              actionUrl: "/contracts/456",
            },
            {
              id: "notif-3",
              title: "You were mentioned",
              message: "Alice Johnson mentioned you in a comment on the Lease Agreement",
              type: "mention",
              status: "read",
              timestamp: "2023-07-13T09:15:00Z",
              sender: {
                name: "Alice Johnson",
                initials: "AJ",
              },
              actionUrl: "/app/contracts/789",
            },
          ]}
          unreadCount={2}
          onMarkAsRead={(id) => console.log("Mark as read:", id)}
          onMarkAllAsRead={() => console.log("Mark all as read")}
          onDeleteNotification={(id) => console.log("Delete notification:", id)}
          onDeleteAllNotifications={() => console.log("Delete all notifications")}
          onSettingsClick={() => console.log("Settings clicked")}
        />

        <div className="hidden md:block">
          <ThemeToggle variant="ghost" />
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-8 w-8 sm:h-9 sm:w-9 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-sm font-medium text-primary hover:bg-primary/20 transition-colors"
            >
              {user?.firstName?.[0]}{user?.lastName?.[0] || ''}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="flex items-center gap-2 p-2">
              <div className="h-10 w-10 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-sm font-medium text-primary">
                {user?.firstName?.[0]}{user?.lastName?.[0] || ''}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{user?.firstName} {user?.lastName}</span>
                <span className="text-xs text-muted-foreground">{user?.primaryEmailAddress?.emailAddress}</span>
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => navigate("/app/profile")}
              className="cursor-pointer flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigate("/app/settings")}
              className="cursor-pointer flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => signOut()}
              className="cursor-pointer text-destructive focus:text-destructive flex items-center gap-2"
            >
              <LogOut className="h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
