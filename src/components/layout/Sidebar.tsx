import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useApi } from "@/lib/api";
import { WorkspaceService } from "@/services/api-services";
import type { Workspace as ApiWorkspace } from "@/services/api-types";
import { useAuth } from "@clerk/clerk-react";
import {
  BarChart3,
  FileText,
  FolderOpen,
  Home,
  Settings,
  ThumbsUp,
  Users,
  BookText,
  LayoutDashboard,
  UserCircle,
  ChevronDown,
  PenTool,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useOptimizedWorkspaceSwitching } from "@/hooks/useOptimizedWorkspaceSwitching";

interface Workspace {
  id: string;
  name: string;
  description?: string;
  members?: number;
  isActive?: boolean;
}

interface SidebarProps {
  activePath: string;
  userName: string;
  userRole: string;
  currentWorkspace?: Workspace;
  onWorkspaceChange?: (workspace: Workspace) => void;
}

const Sidebar = ({
  activePath,
  userName,
  userRole,
  currentWorkspace,
  onWorkspaceChange = () => {},
}: SidebarProps) => {
  // Get workspaces from ClerkWorkspaceProvider instead of fetching directly
  const { getUserWorkspaces, isLoading: workspacesLoading } = useClerkWorkspace();
  const workspaces = getUserWorkspaces();
  const loading = workspacesLoading;
  const error = null; // ClerkWorkspaceProvider handles errors internally

  // Optimized workspace switching
  const {
    switchWorkspace,
    switchWorkspaceImmediate,
    preloadWorkspace,
    isLoading: isSwitching,
    isTransitioning,
    error: switchError,
    clearError,
  } = useOptimizedWorkspaceSwitching({
    onWorkspaceChanged: (workspaceId) => {
      // Find the workspace and call the original handler
      const workspace = workspaces.find(ws => ws.id === workspaceId);
      if (workspace) {
        onWorkspaceChange(workspace);
      }
    },
    onError: (error, workspaceId) => {
      console.error(`Failed to switch to workspace ${workspaceId}:`, error);
    },
    enablePreloading: true,
    enableOptimisticUpdates: true,
    debounceMs: 200,
  });

  // Handle workspace click with optimized switching
  const handleWorkspaceClick = (workspace: Workspace) => {
    if (workspace.id === currentWorkspace?.id) return; // Already active

    // Use immediate switch for better UX
    switchWorkspaceImmediate(workspace.id, workspace.name);
  };

  // Preload workspace data on hover
  const handleWorkspaceHover = (workspace: Workspace) => {
    if (workspace.id !== currentWorkspace?.id) {
      preloadWorkspace(workspace.id);
    }
  };
  return (
    <div className="w-64 h-full bg-background border-r flex flex-col shadow-sm">
      <div className="p-4">
        <div className="flex items-center gap-2.5">
          <div className="h-9 w-9 rounded-md bg-primary flex items-center justify-center shadow-sm">
            <span className="text-lg font-bold text-primary-foreground">L</span>
          </div>
          <span className="text-xl font-bold tracking-tight">LegalAI</span>
        </div>
      </div>

      <Separator className="opacity-70" />

      <div className="px-4 pt-3 pb-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full gap-2 justify-between transition-all duration-200",
                isTransitioning && "scale-[0.98] opacity-80",
                isSwitching && "cursor-wait"
              )}
              disabled={isSwitching}
            >
              <div className="flex items-center gap-2">
                <div className={cn(
                  "h-5 w-5 rounded-full bg-primary flex items-center justify-center transition-all duration-200",
                  isTransitioning && "animate-pulse"
                )}>
                  {isSwitching ? (
                    <Loader2 className="h-3 w-3 animate-spin text-primary-foreground" />
                  ) : (
                    <span className="text-[10px] font-medium text-primary-foreground">
                      {currentWorkspace?.name
                        ? currentWorkspace.name.charAt(0)
                        : "L"}
                    </span>
                  )}
                </div>
                <span className={cn(
                  "font-medium text-sm transition-all duration-200",
                  isTransitioning && "opacity-70"
                )}>
                  {currentWorkspace?.name || "Workspace"}
                </span>
              </div>
              <ChevronDown className={cn(
                "h-4 w-4 opacity-50 transition-transform duration-200",
                isSwitching && "animate-spin"
              )} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[220px] p-0">
            {/* Fixed Header */}
            <div className="px-2 py-1.5 border-b border-border/50">
              <DropdownMenuLabel className="px-0 py-1">Workspaces</DropdownMenuLabel>
            </div>

            {/* Scrollable Workspace List */}
            <div className="max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border/50 hover:scrollbar-thumb-border/80">
              {switchError && (
                <div className="px-2 py-2 mx-1 mb-1 bg-destructive/10 border border-destructive/20 rounded-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-destructive">{switchError}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 text-destructive hover:text-destructive"
                      onClick={clearError}
                    >
                      ×
                    </Button>
                  </div>
                </div>
              )}
              {loading ? (
                <div className="px-2 py-4 text-center">
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Loading workspaces...</span>
                  </div>
                </div>
              ) : error ? (
                <div className="px-2 py-4 text-center">
                  <span className="text-sm text-destructive">{error}</span>
                </div>
              ) : workspaces.length === 0 ? (
                <div className="px-2 py-4 text-center space-y-2">
                  <span className="text-sm text-muted-foreground block">No workspaces available</span>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => (window.location.href = "/app/workspaces")}
                    className="w-full"
                  >
                    Create Workspace
                  </Button>
                </div>
              ) : (
                <div className="py-1">
                  {workspaces.map((workspace) => (
                    <DropdownMenuItem
                      key={workspace.id}
                      className={cn(
                        "flex items-center gap-2 py-2 mx-1 rounded-sm focus:bg-accent/50 cursor-pointer transition-all duration-150",
                        workspace.isActive && "bg-accent/30",
                        isSwitching && workspace.id === currentWorkspace?.id && "opacity-50"
                      )}
                      onClick={() => handleWorkspaceClick(workspace)}
                      onMouseEnter={() => handleWorkspaceHover(workspace)}
                      disabled={isSwitching}
                    >
                      <div className={cn(
                        "h-5 w-5 rounded-full bg-primary flex items-center justify-center flex-shrink-0 transition-all duration-150",
                        workspace.isActive && "ring-2 ring-primary/20"
                      )}>
                        <span className="text-[10px] font-medium text-primary-foreground">
                          {workspace?.name ? workspace.name.charAt(0) : "W"}
                        </span>
                      </div>
                      <div className="flex flex-col min-w-0 flex-1">
                        <span className={cn(
                          "text-sm font-medium truncate transition-all duration-150",
                          workspace.isActive && "text-primary"
                        )}>
                          {workspace.name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {workspace.members} member{workspace.members !== 1 ? 's' : ''}
                        </span>
                      </div>
                      {workspace.isActive && (
                        <Badge variant="outline" className="ml-auto text-xs flex-shrink-0 border-primary/50 text-primary">
                          Active
                        </Badge>
                      )}
                      {isSwitching && workspace.id === currentWorkspace?.id && (
                        <Loader2 className="ml-auto h-3 w-3 animate-spin text-muted-foreground" />
                      )}
                    </DropdownMenuItem>
                  ))}
                </div>
              )}
            </div>

            {/* Fixed Footer */}
            {workspaces.length > 0 && (
              <div className="border-t border-border/50 p-1">
                <DropdownMenuItem
                  onClick={() => (window.location.href = "/app/workspaces")}
                  className="mx-0 rounded-sm focus:bg-accent/50 cursor-pointer"
                >
                  <span className="text-sm">Manage Workspaces</span>
                </DropdownMenuItem>
              </div>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="flex-1 py-4 overflow-auto scrollbar-thin">
        <div className="px-3 mb-2">
          <h3 className="text-xs font-medium text-muted-foreground px-2 mb-1.5">MAIN NAVIGATION</h3>
        </div>
        <nav className="space-y-1 px-2">
          <Link to="/app/dashboard" className="block">
            <Button
              variant={activePath === "/app/dashboard" ? "secondary" : "ghost"}
              className={`w-full justify-start h-9 px-2.5 ${
                activePath === "/app/dashboard"
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <LayoutDashboard className={`mr-2.5 h-4 w-4 ${activePath === "/app/dashboard" ? "text-primary" : "opacity-70"}`} />
              Dashboard
            </Button>
          </Link>

          <Link to="/app/contracts" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/contracts")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/contracts")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <FileText className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/contracts") ? "text-primary" : "opacity-70"}`} />
              Contracts
            </Button>
          </Link>

          <Link to="/app/repository" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/repository")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/repository")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <FolderOpen className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/repository") ? "text-primary" : "opacity-70"}`} />
              Repository
            </Button>
          </Link>

          <Link to="/app/approvals" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/approvals")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/approvals")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <ThumbsUp className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/approvals") ? "text-primary" : "opacity-70"}`} />
              Approvals
              <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium text-primary">3</span>
            </Button>
          </Link>

          <div className="px-3 pt-4 pb-1">
            <h3 className="text-xs font-medium text-muted-foreground px-2 mb-1.5">TOOLS & RESOURCES</h3>
          </div>

          <Link to="/app/analytics" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/analytics")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/analytics")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <BarChart3 className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/analytics") ? "text-primary" : "opacity-70"}`} />
              Analytics
            </Button>
          </Link>

          <Link to="/app/workspaces" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/workspaces")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/workspaces")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <Users className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/workspaces") ? "text-primary" : "opacity-70"}`} />
              Workspaces
            </Button>
          </Link>

          <Link to="/app/clause-library" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/clause-library")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/clause-library")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <BookText className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/clause-library") ? "text-primary" : "opacity-70"}`} />
              Clause Library
            </Button>
          </Link>

          <div className="px-3 pt-4 pb-1">
            <h3 className="text-xs font-medium text-muted-foreground px-2 mb-1.5">EXAMPLES</h3>
          </div>

          <Link to="/app/examples/form" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/examples/form")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/examples/form")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <FileText className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/examples/form") ? "text-primary" : "opacity-70"}`} />
              Form Example
            </Button>
          </Link>

          <Link to="/app/examples/signature" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/examples/signature")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/examples/signature")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <PenTool className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/examples/signature") ? "text-primary" : "opacity-70"}`} />
              Signature Flow
            </Button>
          </Link>

          <Link to="/app/examples/permissions" className="block">
            <Button
              variant={
                activePath && activePath.startsWith("/app/examples/permissions")
                  ? "secondary"
                  : "ghost"
              }
              className={`w-full justify-start h-9 px-2.5 ${
                activePath && activePath.startsWith("/app/examples/permissions")
                  ? "bg-secondary/70 font-medium shadow-sm"
                  : "hover:bg-muted/50"
              }`}
            >
              <Users className={`mr-2.5 h-4 w-4 ${activePath && activePath.startsWith("/app/examples/permissions") ? "text-primary" : "opacity-70"}`} />
              Permissions
            </Button>
          </Link>

        </nav>
      </div>

      <Separator className="opacity-70" />

      <div className="p-3 space-y-1">
        <Link to="/app/settings" className="block">
          <Button
            variant={activePath === "/app/settings" ? "secondary" : "ghost"}
            className={`w-full justify-start h-9 px-2.5 ${
              activePath === "/app/settings"
                ? "bg-secondary/70 font-medium shadow-sm"
                : "hover:bg-muted/50"
            }`}
          >
            <Settings className={`mr-2.5 h-4 w-4 ${activePath === "/app/settings" ? "text-primary" : "opacity-70"}`} />
            Settings
          </Button>
        </Link>

        <div className="mt-3 pt-3 border-t border-border/50">
          <div className="flex items-center gap-2.5 p-2 rounded-md">
            <div className="h-8 w-8 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center">
              <UserCircle className="h-5 w-5 text-primary/80" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium">{userName}</span>
              <span className="text-xs text-muted-foreground">{userRole}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
