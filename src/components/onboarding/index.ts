// Onboarding Components Export Index
export { OnboardingFlow } from './OnboardingFlow';
export { OnboardingProvider, useOnboarding, useIsNewUser, useOnboardingAnalytics } from './OnboardingContext';
export {
  GuidedTour,
  useGuidedTour,
  contractsTour,
  templatesTour,
  dashboardTour
} from './GuidedTour';
export {
  OnboardingWrapper,
  TourTarget,
  useTourAttributes,
  tourStyles,
  OnboardingExample
} from './OnboardingWrapper';

// Re-export types (interfaces are not exported by default, so we create type aliases)
export type OnboardingContextType = {
  isOnboardingComplete: boolean;
  showOnboarding: boolean;
  completeOnboarding: () => void;
  startOnboarding: () => void;
  skipOnboarding: () => void;
  resetOnboarding: () => void;
};

export type TourStep = {
  id: string;
  title: string;
  content: string;
  target: string;
  position: 'top' | 'bottom' | 'left' | 'right';
  action?: {
    text: string;
    onClick: () => void;
  };
};
