import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft, 
  Users, 
  FileText, 
  Shield, 
  Briefcase,
  Building,
  Sparkles,
  BookOpen,
  Target
} from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
}

interface OnboardingFlowProps {
  onComplete: () => void;
  userRole?: string;
  workspaceName?: string;
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  onComplete,
  userRole = 'user',
  workspaceName = 'Your Workspace'
}) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to LegalAI',
      description: 'Your intelligent contract management platform',
      icon: <Sparkles className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'workspace',
      title: 'Explore Your Workspace',
      description: `Get familiar with ${workspaceName} and its features`,
      icon: <Building className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'contracts',
      title: 'Contract Management',
      description: 'Learn how to create, manage, and track contracts',
      icon: <FileText className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'templates',
      title: 'Template Library',
      description: 'Discover pre-built templates for faster contract creation',
      icon: <BookOpen className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'collaboration',
      title: 'Team Collaboration',
      description: 'Work together with approval workflows and comments',
      icon: <Users className="h-6 w-6" />,
      completed: false
    },
    {
      id: 'security',
      title: 'Security & Compliance',
      description: 'Understand how we protect your sensitive data',
      icon: <Shield className="h-6 w-6" />,
      completed: false
    }
  ];

  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    const currentStepId = steps[currentStep].id;
    setCompletedSteps(prev => new Set([...prev, currentStepId]));
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  const renderStepContent = () => {
    const step = steps[currentStep];
    
    switch (step.id) {
      case 'welcome':
        return (
          <div className="text-center space-y-6">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Sparkles className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-2">Welcome to LegalAI</h2>
              <p className="text-gray-600 mb-4">
                Your intelligent contract management platform that streamlines legal workflows
                and enhances collaboration across your organization.
              </p>
              <div className="grid grid-cols-2 gap-4 mt-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">50%</div>
                  <div className="text-sm text-gray-500">Faster Contract Creation</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">99%</div>
                  <div className="text-sm text-gray-500">Compliance Rate</div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'workspace':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Building className="mx-auto h-12 w-12 text-blue-600 mb-4" />
              <h2 className="text-xl font-bold mb-2">Your Workspace: {workspaceName}</h2>
              <p className="text-gray-600">
                Workspaces help organize your contracts, templates, and team members.
                You can be part of multiple workspaces for different projects or departments.
              </p>
            </div>
            
            <div className="grid gap-4">
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="font-medium">Contracts</div>
                  <div className="text-sm text-gray-500">Manage all your legal agreements</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <BookOpen className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Templates</div>
                  <div className="text-sm text-gray-500">Pre-built contract templates</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Users className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="font-medium">Team Members</div>
                  <div className="text-sm text-gray-500">Collaborate with your team</div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'contracts':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <FileText className="mx-auto h-12 w-12 text-blue-600 mb-4" />
              <h2 className="text-xl font-bold mb-2">Contract Management Made Easy</h2>
              <p className="text-gray-600">
                Create, review, and manage contracts with powerful tools and AI assistance.
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-blue-600">1</span>
                </div>
                <div>
                  <div className="font-medium">Create Contracts</div>
                  <div className="text-sm text-gray-500">Use our wizard or start from templates</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-blue-600">2</span>
                </div>
                <div>
                  <div className="font-medium">AI Analysis</div>
                  <div className="text-sm text-gray-500">Get intelligent insights and risk assessment</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-blue-600">3</span>
                </div>
                <div>
                  <div className="font-medium">Approval Workflow</div>
                  <div className="text-sm text-gray-500">Route contracts for review and approval</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-bold text-blue-600">4</span>
                </div>
                <div>
                  <div className="font-medium">Electronic Signature</div>
                  <div className="text-sm text-gray-500">Collect signatures digitally</div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'templates':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <BookOpen className="mx-auto h-12 w-12 text-green-600 mb-4" />
              <h2 className="text-xl font-bold mb-2">Template Library</h2>
              <p className="text-gray-600">
                Speed up contract creation with our comprehensive template library.
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="p-3 border rounded-lg text-center">
                <Shield className="mx-auto h-6 w-6 text-blue-600 mb-2" />
                <div className="text-sm font-medium">NDAs</div>
                <div className="text-xs text-gray-500">Non-disclosure agreements</div>
              </div>
              <div className="p-3 border rounded-lg text-center">
                <Briefcase className="mx-auto h-6 w-6 text-green-600 mb-2" />
                <div className="text-sm font-medium">Service Agreements</div>
                <div className="text-xs text-gray-500">Professional services</div>
              </div>
              <div className="p-3 border rounded-lg text-center">
                <Users className="mx-auto h-6 w-6 text-purple-600 mb-2" />
                <div className="text-sm font-medium">Employment</div>
                <div className="text-xs text-gray-500">Job contracts</div>
              </div>
              <div className="p-3 border rounded-lg text-center">
                <Building className="mx-auto h-6 w-6 text-orange-600 mb-2" />
                <div className="text-sm font-medium">Real Estate</div>
                <div className="text-xs text-gray-500">Property agreements</div>
              </div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-blue-900">Pro Tip</div>
              <div className="text-sm text-blue-700">
                You can customize any template or create your own for future use.
              </div>
            </div>
          </div>
        );

      case 'collaboration':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Users className="mx-auto h-12 w-12 text-purple-600 mb-4" />
              <h2 className="text-xl font-bold mb-2">Team Collaboration</h2>
              <p className="text-gray-600">
                Work together seamlessly with built-in collaboration tools.
              </p>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                <Target className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="font-medium">Approval Workflows</div>
                  <div className="text-sm text-gray-500">Set up custom approval processes</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="font-medium">Comments & Reviews</div>
                  <div className="text-sm text-gray-500">Add comments and track changes</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="font-medium">Real-time Updates</div>
                  <div className="text-sm text-gray-500">Stay informed with notifications</div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Shield className="mx-auto h-12 w-12 text-green-600 mb-4" />
              <h2 className="text-xl font-bold mb-2">Security & Compliance</h2>
              <p className="text-gray-600">
                Your data is protected with enterprise-grade security measures.
              </p>
            </div>
            
            <div className="grid gap-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm">End-to-end encryption</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm">SOC 2 Type II compliance</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm">GDPR compliant</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm">Regular security audits</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm">Role-based access control</span>
              </div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-green-900">Ready to Get Started?</div>
              <div className="text-sm text-green-700">
                You're all set! Click "Complete Setup" to start using LegalAI.
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-between mb-4">
            <Badge variant="outline">
              Step {currentStep + 1} of {steps.length}
            </Badge>
            <Button variant="ghost" size="sm" onClick={handleSkip}>
              Skip Tour
            </Button>
          </div>
          <Progress value={progress} className="mb-4" />
        </CardHeader>
        
        <CardContent className="space-y-6">
          {renderStepContent()}
          
          <Separator />
          
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <Button onClick={handleNext}>
              {currentStep === steps.length - 1 ? 'Complete Setup' : 'Next'}
              {currentStep < steps.length - 1 && <ArrowRight className="h-4 w-4 ml-2" />}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
