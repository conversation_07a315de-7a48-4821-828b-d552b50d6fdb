import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  Edit,
  Lock,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  Trash,
  User,
  UserCog,
  Users,
} from "lucide-react";

// Types
interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isSystem?: boolean;
  userCount: number;
}

interface UserWithRole {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roleId: string;
  status: "active" | "inactive" | "pending";
  lastActive?: string;
}

interface UserRolesPermissionsProps {
  onRoleCreate?: (role: Omit<Role, "id" | "userCount">) => void;
  onRoleUpdate?: (role: Role) => void;
  onRoleDelete?: (roleId: string) => void;
  onUserRoleUpdate?: (userId: string, roleId: string) => void;
}

const UserRolesPermissions: React.FC<UserRolesPermissionsProps> = ({
  onRoleCreate,
  onRoleUpdate,
  onRoleDelete,
  onUserRoleUpdate,
}) => {
  // State
  const [activeTab, setActiveTab] = useState<string>("roles");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState<boolean>(false);
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState<boolean>(false);
  const [selectedRoleId, setSelectedRoleId] = useState<string | null>(null);
  const [newRole, setNewRole] = useState<Omit<Role, "id" | "userCount">>({
    name: "",
    description: "",
    permissions: [],
  });

  // Mock data for permissions
  const permissions: Permission[] = [
    { id: "perm-1", name: "contracts.view", description: "View contracts", category: "contracts" },
    { id: "perm-2", name: "contracts.create", description: "Create contracts", category: "contracts" },
    { id: "perm-3", name: "contracts.edit", description: "Edit contracts", category: "contracts" },
    { id: "perm-4", name: "contracts.delete", description: "Delete contracts", category: "contracts" },
    { id: "perm-5", name: "contracts.approve", description: "Approve contracts", category: "contracts" },
    { id: "perm-6", name: "templates.view", description: "View templates", category: "templates" },
    { id: "perm-7", name: "templates.create", description: "Create templates", category: "templates" },
    { id: "perm-8", name: "templates.edit", description: "Edit templates", category: "templates" },
    { id: "perm-9", name: "clauses.view", description: "View clause library", category: "clauses" },
    { id: "perm-10", name: "clauses.manage", description: "Manage clause library", category: "clauses" },
    { id: "perm-11", name: "users.view", description: "View users", category: "users" },
    { id: "perm-12", name: "users.manage", description: "Manage users", category: "users" },
    { id: "perm-13", name: "roles.view", description: "View roles", category: "roles" },
    { id: "perm-14", name: "roles.manage", description: "Manage roles", category: "roles" },
    { id: "perm-15", name: "analytics.view", description: "View analytics", category: "analytics" },
    { id: "perm-16", name: "settings.view", description: "View settings", category: "settings" },
    { id: "perm-17", name: "settings.manage", description: "Manage settings", category: "settings" },
  ];

  // Mock data for roles
  const [roles, setRoles] = useState<Role[]>([
    {
      id: "role-1",
      name: "Administrator",
      description: "Full system access",
      permissions: permissions.map(p => p.id),
      isSystem: true,
      userCount: 2,
    },
    {
      id: "role-2",
      name: "Contract Manager",
      description: "Manage contracts and templates",
      permissions: [
        "perm-1", "perm-2", "perm-3", "perm-4", "perm-5",
        "perm-6", "perm-7", "perm-8", "perm-9", "perm-10",
        "perm-15",
      ],
      userCount: 5,
    },
    {
      id: "role-3",
      name: "Legal Reviewer",
      description: "Review and approve contracts",
      permissions: ["perm-1", "perm-3", "perm-5", "perm-6", "perm-9"],
      userCount: 8,
    },
    {
      id: "role-4",
      name: "Read Only",
      description: "View-only access",
      permissions: ["perm-1", "perm-6", "perm-9", "perm-15"],
      userCount: 12,
    },
  ]);

  // Mock data for users
  const users: UserWithRole[] = [
    {
      id: "user-1",
      name: "John Smith",
      email: "<EMAIL>",
      roleId: "role-1",
      status: "active",
      lastActive: "2023-07-15T10:30:00Z",
    },
    {
      id: "user-2",
      name: "Jane Doe",
      email: "<EMAIL>",
      roleId: "role-1",
      status: "active",
      lastActive: "2023-07-15T14:45:00Z",
    },
    {
      id: "user-3",
      name: "Alice Johnson",
      email: "<EMAIL>",
      roleId: "role-2",
      status: "active",
      lastActive: "2023-07-14T09:15:00Z",
    },
    {
      id: "user-4",
      name: "Bob Williams",
      email: "<EMAIL>",
      roleId: "role-3",
      status: "inactive",
      lastActive: "2023-06-30T16:20:00Z",
    },
    {
      id: "user-5",
      name: "Carol Brown",
      email: "<EMAIL>",
      roleId: "role-4",
      status: "active",
      lastActive: "2023-07-15T11:10:00Z",
    },
  ];

  // Filter roles based on search term
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter users based on search term
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    roles.find(r => r.id === user.roleId)?.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group permissions by category
  const permissionsByCategory = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  // Handle role creation
  const handleCreateRole = () => {
    if (!newRole.name.trim()) return;

    const newRoleWithId: Role = {
      id: `role-${Date.now()}`,
      ...newRole,
      userCount: 0,
    };

    setRoles([...roles, newRoleWithId]);
    if (onRoleCreate) onRoleCreate(newRole);

    setNewRole({
      name: "",
      description: "",
      permissions: [],
    });

    setIsAddRoleDialogOpen(false);
  };

  // Handle role deletion
  const handleDeleteRole = () => {
    if (!selectedRoleId) return;

    setRoles(roles.filter(role => role.id !== selectedRoleId));
    if (onRoleDelete) onRoleDelete(selectedRoleId);

    setSelectedRoleId(null);
    setIsDeleteRoleDialogOpen(false);
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string) => {
    setNewRole(prev => {
      const permissions = prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId];

      return { ...prev, permissions };
    });
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Never";

    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get role by ID
  const getRoleById = (roleId: string) => roles.find(role => role.id === roleId);

  // Get status badge
  const getStatusBadge = (status: UserWithRole["status"]) => {
    switch (status) {
      case "active":
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case "inactive":
        return <Badge variant="secondary" className="bg-slate-500">Inactive</Badge>;
      case "pending":
        return <Badge variant="outline" className="border-amber-500 text-amber-600">Pending</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">User Roles & Permissions</h2>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8 h-9 w-[200px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          {activeTab === "roles" && (
            <Button
              onClick={() => setIsAddRoleDialogOpen(true)}
              size="sm"
              className="h-9"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Role
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="roles" className="flex items-center">
            <Shield className="h-4 w-4 mr-2" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Users
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Roles</CardTitle>
              <CardDescription>Manage user roles and their permissions</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-4">
                  {filteredRoles.length > 0 ? (
                    filteredRoles.map((role) => (
                      <Card key={role.id} className="overflow-hidden">
                        <CardHeader className="pb-3 bg-muted/30">
                          <div className="flex items-center justify-between">
                            <div>
                              <CardTitle className="text-base flex items-center">
                                {role.name}
                                {role.isSystem && (
                                  <Badge variant="outline" className="ml-2 text-xs">System</Badge>
                                )}
                              </CardTitle>
                              <CardDescription>{role.description}</CardDescription>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{role.userCount} users</Badge>
                              {!role.isSystem && (
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon" className="h-8 w-8">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() => {
                                        setNewRole({
                                          name: role.name,
                                          description: role.description,
                                          permissions: role.permissions,
                                        });
                                        setSelectedRoleId(role.id);
                                        setIsAddRoleDialogOpen(true);
                                      }}
                                    >
                                      <Edit className="h-4 w-4 mr-2" />
                                      Edit Role
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => {
                                        setSelectedRoleId(role.id);
                                        setIsDeleteRoleDialogOpen(true);
                                      }}
                                      className="text-destructive"
                                    >
                                      <Trash className="h-4 w-4 mr-2" />
                                      Delete Role
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-4">
                          <div className="space-y-4">
                            <div>
                              <h4 className="text-sm font-medium mb-2">Permissions</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                                  <div key={category} className="space-y-2">
                                    <h5 className="text-xs font-medium uppercase text-muted-foreground">
                                      {category}
                                    </h5>
                                    <div className="space-y-1">
                                      {categoryPermissions.map((permission) => (
                                        <div key={permission.id} className="flex items-center gap-2">
                                          <Checkbox
                                            id={`${role.id}-${permission.id}`}
                                            checked={role.permissions.includes(permission.id)}
                                            disabled
                                          />
                                          <label
                                            htmlFor={`${role.id}-${permission.id}`}
                                            className="text-sm"
                                          >
                                            {permission.description}
                                          </label>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Shield className="h-12 w-12 text-muted-foreground mb-3" />
                      <h3 className="text-lg font-medium mb-1">No roles found</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {searchTerm ? "Try a different search term" : "Create your first role"}
                      </p>
                      {!searchTerm && (
                        <Button
                          variant="outline"
                          onClick={() => setIsAddRoleDialogOpen(true)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Role
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Users</CardTitle>
              <CardDescription>Manage user role assignments</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length > 0 ? (
                    filteredUsers.map((user) => {
                      const userRole = getRoleById(user.roleId);

                      return (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                {user.avatar ? (
                                  <AvatarImage src={user.avatar} alt={user.name} />
                                ) : (
                                  <AvatarFallback>
                                    {user.name.split(" ").map(n => n[0]).join("").toUpperCase()}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                              <div>
                                <div className="font-medium">{user.name}</div>
                                <div className="text-xs text-muted-foreground">{user.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="font-normal">
                              {userRole?.name || "Unknown"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(user.status)}
                          </TableCell>
                          <TableCell>
                            {formatDate(user.lastActive)}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>
                                  <User className="h-4 w-4 mr-2" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <UserCog className="h-4 w-4 mr-2" />
                                  Change Role
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Lock className="h-4 w-4 mr-2" />
                                  Reset Password
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center">
                          <Users className="h-8 w-8 text-muted-foreground mb-2" />
                          <p className="text-muted-foreground">
                            {searchTerm ? "No users found" : "No users available"}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add/Edit Role Dialog */}
      <Dialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedRoleId ? "Edit Role" : "Create New Role"}</DialogTitle>
            <DialogDescription>
              {selectedRoleId ? "Update role details and permissions" : "Define a new role with specific permissions"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Role Name</label>
                <Input
                  placeholder="e.g., Contract Manager"
                  value={newRole.name}
                  onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Input
                  placeholder="Brief description of the role"
                  value={newRole.description}
                  onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">Permissions</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setNewRole({ ...newRole, permissions: permissions.map(p => p.id) })}
                >
                  Select All
                </Button>
              </div>

              <ScrollArea className="h-[300px] pr-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                    <div key={category} className="space-y-2">
                      <h4 className="text-xs font-medium uppercase text-muted-foreground">
                        {category}
                      </h4>
                      <div className="space-y-1">
                        {categoryPermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center gap-2">
                            <Checkbox
                              id={`new-${permission.id}`}
                              checked={newRole.permissions.includes(permission.id)}
                              onCheckedChange={() => handlePermissionToggle(permission.id)}
                            />
                            <label
                              htmlFor={`new-${permission.id}`}
                              className="text-sm"
                            >
                              {permission.description}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddRoleDialogOpen(false);
              setSelectedRoleId(null);
              setNewRole({
                name: "",
                description: "",
                permissions: [],
              });
            }}>
              Cancel
            </Button>
            <Button onClick={() => {
              if (selectedRoleId) {
                // Update existing role
                const updatedRole = {
                  id: selectedRoleId,
                  ...newRole,
                  userCount: roles.find(r => r.id === selectedRoleId)?.userCount || 0,
                  isSystem: roles.find(r => r.id === selectedRoleId)?.isSystem,
                };
                setRoles(roles.map(role => role.id === selectedRoleId ? updatedRole : role));
                if (onRoleUpdate) onRoleUpdate(updatedRole);
                setSelectedRoleId(null);
              } else {
                // Create new role
                handleCreateRole();
              }
            }}>
              {selectedRoleId ? "Update Role" : "Create Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Role Confirmation Dialog */}
      <AlertDialog open={isDeleteRoleDialogOpen} onOpenChange={setIsDeleteRoleDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Role</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this role? This action cannot be undone.
              {selectedRoleId && roles.find(r => r.id === selectedRoleId)?.userCount > 0 && (
                <div className="mt-2 text-destructive">
                  Warning: This role is currently assigned to {roles.find(r => r.id === selectedRoleId)?.userCount} users.
                  Deleting it will remove the role from these users.
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsDeleteRoleDialogOpen(false);
              setSelectedRoleId(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole} className="bg-destructive text-destructive-foreground">
              <Trash className="h-4 w-4 mr-2" />
              Delete Role
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default UserRolesPermissions;
