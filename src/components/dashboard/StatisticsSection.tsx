import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { FileText, AlertTriangle, Clock, CheckCircle, Loader2 } from "lucide-react";
import { useApi } from "@/lib/api";
import { AnalyticsService } from "@/services/api-services";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Skeleton, SkeletonCard } from "@/components/ui/skeleton";

interface StatisticsSectionProps {
  totalContracts?: number;
  pendingApprovals?: number;
  expiringSoon?: number;
  complianceRate?: number;
  isLoading?: boolean;
}

const StatisticsSection = ({
  totalContracts,
  pendingApprovals,
  expiringSoon,
  complianceRate,
  isLoading = false,
}: StatisticsSectionProps) => {
  const { fetch } = useApi();
  const { currentWorkspace } = useClerkWorkspace();

  const [stats, setStats] = useState({
    totalContracts: totalContracts || 0,
    pendingApprovals: pendingApprovals || 0,
    expiringSoon: expiringSoon || 0,
    complianceRate: complianceRate || 0
  });
  const [loading, setLoading] = useState(isLoading || !totalContracts);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!currentWorkspace?.id) return;

      setLoading(true);

      try {
        const result = await fetch(
          () => AnalyticsService.getDashboardSummary(currentWorkspace.id),
          "Loading dashboard data...",
          "Failed to load dashboard data"
        );

        if (result) {
          setStats({
            totalContracts: result.total_contracts,
            pendingApprovals: result.pending_approvals,
            expiringSoon: result.expiring_soon,
            complianceRate: result.compliance_rate
          });
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
      } finally {
        setLoading(false);
      }
    };

    // If props are provided, use them instead of fetching
    if (totalContracts !== undefined &&
        pendingApprovals !== undefined &&
        expiringSoon !== undefined &&
        complianceRate !== undefined) {
      setStats({
        totalContracts,
        pendingApprovals,
        expiringSoon,
        complianceRate
      });
      setLoading(isLoading);
    } else {
      fetchDashboardData();
    }
  }, [currentWorkspace?.id, fetch, totalContracts, pendingApprovals, expiringSoon, complianceRate, isLoading]);
  return (
    <div className="grid-comfortable grid-cols-1 md:grid-cols-2 lg:grid-cols-4 stagger-children">
      <Card variant="enhanced" interactive>
        <CardHeader className="pb-3">
          <CardTitle className="heading-label">
            Total Contracts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-16 rounded-md" />
              <Skeleton className="h-3 w-32 rounded-sm" />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-2xl font-semibold tracking-tight">{stats.totalContracts}</div>
              <p className="helper-text">
                {currentWorkspace ? `In ${currentWorkspace.name}` : 'All workspaces'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card variant="enhanced" interactive>
        <CardHeader className="pb-3">
          <CardTitle className="heading-label">
            Pending Approvals
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-16 rounded-md" />
              <Skeleton className="h-3 w-24 rounded-sm" />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-2xl font-semibold tracking-tight">{stats.pendingApprovals}</div>
              <p className="helper-text">
                Awaiting review
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card variant="enhanced" interactive>
        <CardHeader className="pb-3">
          <CardTitle className="heading-label">
            Expiring Soon
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-16 rounded-md" />
              <Skeleton className="h-3 w-20 rounded-sm" />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-2xl font-semibold tracking-tight">{stats.expiringSoon}</div>
              <p className="helper-text">
                Next 30 days
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card variant="enhanced" interactive>
        <CardHeader className="pb-3">
          <CardTitle className="heading-label">
            Compliance Rate
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              <Skeleton className="h-8 w-16 rounded-md" />
              <Skeleton className="h-1.5 w-full rounded-full" />
            </div>
          ) : (
            <div className="space-y-3">
              <div className="text-2xl font-semibold tracking-tight">{stats.complianceRate}%</div>
              <Progress value={stats.complianceRate} className="h-1.5" />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default StatisticsSection;
