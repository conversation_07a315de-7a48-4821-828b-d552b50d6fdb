import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Edit, CheckCircle, XCircle, ArrowRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface Activity {
  id: string;
  type: "created" | "edited" | "status_change" | "approval" | "rejection";
  contractName: string;
  user: string;
  timestamp: string;
  details?: string;
}

interface RecentActivitiesProps {
  activities?: Activity[];
  maxItems?: number;
  isLoading?: boolean;
}

const RecentActivities = ({
  activities = [],
  maxItems = 5,
  isLoading = false,
}: RecentActivitiesProps) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60),
    );

    if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? "hour" : "hours"} ago`;
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    }
  };

  const getActivityIcon = (type: Activity["type"]) => {
    switch (type) {
      case "created":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "edited":
        return <Edit className="h-4 w-4 text-amber-500" />;
      case "status_change":
        return <ArrowRight className="h-4 w-4 text-purple-500" />;
      case "approval":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "rejection":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getActivityText = (activity: Activity) => {
    switch (activity.type) {
      case "created":
        return `created ${activity.contractName}`;
      case "edited":
        return `edited ${activity.contractName}`;
      case "status_change":
        return `changed status of ${activity.contractName}`;
      case "approval":
        return `approved ${activity.contractName}`;
      case "rejection":
        return `rejected ${activity.contractName}`;
      default:
        return `interacted with ${activity.contractName}`;
    }
  };

  const displayedActivities = activities.slice(0, maxItems);

  const renderSkeletonItem = () => (
    <div className="flex items-start space-x-2 p-2 border rounded-lg">
      <Skeleton className="h-8 w-8 rounded-full" />
      <div className="flex-1 min-w-0">
        <Skeleton className="h-4 w-3/4 mb-1" />
        <Skeleton className="h-3 w-1/2 mb-1" />
        <div className="flex items-center mt-1">
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-2">
      {isLoading ? (
        <>
          {[1, 2, 3, 4].map((i) => (
            <div key={i}>{renderSkeletonItem()}</div>
          ))}
        </>
      ) : displayedActivities.length > 0 ? (
        <>
          {displayedActivities.map((activity) => (
            <div
              key={activity.id}
              className="flex items-start space-x-2 p-2 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
            >
              <div className={`rounded-full p-1.5 ${
                activity.type === 'created' ? 'bg-blue-50 dark:bg-blue-900/20' :
                activity.type === 'edited' ? 'bg-amber-50 dark:bg-amber-900/20' :
                activity.type === 'status_change' ? 'bg-purple-50 dark:bg-purple-900/20' :
                activity.type === 'approval' ? 'bg-green-50 dark:bg-green-900/20' :
                activity.type === 'rejection' ? 'bg-red-50 dark:bg-red-900/20' :
                'bg-muted'
              }`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium truncate">
                  <span className="font-semibold">{activity.user}</span>{" "}
                  {getActivityText(activity)}
                </p>
                {activity.details && (
                  <p className="text-xs text-muted-foreground mt-0.5 truncate">
                    {activity.details}
                  </p>
                )}
                <div className="flex items-center mt-1">
                  <div className="h-1 w-1 rounded-full bg-muted-foreground/30 mr-1"></div>
                  <p className="text-xs text-muted-foreground">
                    {formatDate(activity.timestamp)}
                  </p>
                </div>
              </div>
            </div>
          ))}

          {activities.length > maxItems && (
            <div className="pt-1 text-center">
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs h-7"
                onClick={() => (window.location.href = "/contracts")}
              >
                View All Activities
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="rounded-full bg-muted p-2 mb-2">
            <FileText className="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 className="text-sm font-medium mb-1">No recent activities</h3>
          <p className="text-xs text-muted-foreground text-center">
            Activities will appear here as you work with contracts.
          </p>
        </div>
      )}
    </div>
  );
};

export default RecentActivities;
