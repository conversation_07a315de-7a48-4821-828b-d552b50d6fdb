import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ThumbsDown, ThumbsUp, FileText } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface Contract {
  id: string;
  title: string;
  submittedBy: {
    name: string;
    avatar?: string;
    initials: string;
  };
  submittedDate: string;
  priority: "high" | "medium" | "low";
  status: "pending" | "approved" | "rejected" | "reviewing";
}

interface PendingApprovalsProps {
  contracts?: Contract[];
  onApprove?: (id: string) => void;
  onReject?: (id: string) => void;
  onReview?: (id: string) => void;
  isLoading?: boolean;
}

const PendingApprovals = ({
  contracts = [],
  onApprove = () => {},
  onReject = () => {},
  onReview = () => {},
  isLoading = false,
}: PendingApprovalsProps) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge variant="destructive">High</Badge>;
      case "medium":
        return <Badge variant="secondary">Medium</Badge>;
      case "low":
        return <Badge variant="outline">Low</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline">Pending</Badge>;
      case "reviewing":
        return <Badge variant="secondary">Reviewing</Badge>;
      case "approved":
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  const renderSkeletonItem = () => (
    <div className="p-3 border rounded-lg">
      <div className="flex items-start justify-between mb-1.5">
        <div className="flex items-start space-x-2">
          <Skeleton className="h-6 w-6 rounded-full" />
          <div>
            <Skeleton className="h-4 w-32 mb-1" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
        <div className="flex items-center space-x-1.5">
          <Skeleton className="h-5 w-16" />
        </div>
      </div>
      <div className="flex justify-end space-x-1.5 mt-2">
        <Skeleton className="h-7 w-16" />
        <Skeleton className="h-7 w-16" />
        <Skeleton className="h-7 w-20" />
      </div>
    </div>
  );

  return (
    <div className="space-y-3">
      {isLoading ? (
        <>
          {[1, 2, 3].map((i) => (
            <div key={i}>{renderSkeletonItem()}</div>
          ))}
        </>
      ) : contracts.length > 0 ? (
        contracts.map((contract) => (
          <div
            key={contract.id}
            className="p-3 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-start justify-between mb-1.5">
              <div className="flex items-start space-x-2">
                <Avatar className="h-6 w-6">
                  {contract.submittedBy.avatar ? (
                    <AvatarImage
                      src={contract.submittedBy.avatar}
                      alt={contract.submittedBy.name}
                    />
                  ) : (
                    <AvatarFallback className="text-xs">
                      {contract.submittedBy.initials}
                    </AvatarFallback>
                  )}
                </Avatar>
                <div>
                  <h3 className="text-sm font-medium">{contract.title}</h3>
                  <p className="text-xs text-muted-foreground">
                    {contract.submittedBy.name} • {formatDate(contract.submittedDate)}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-1.5">
                {getPriorityBadge(contract.priority)}
                {getStatusBadge(contract.status)}
              </div>
            </div>
            <div className="flex justify-end space-x-1.5 mt-2">
              <Button
                variant="outline"
                size="sm"
                className="h-7 text-xs px-2"
                onClick={() => onReview(contract.id)}
              >
                <FileText className="h-3 w-3 mr-1" />
                Review
              </Button>
              <Button
                variant="destructive"
                size="sm"
                className="h-7 text-xs px-2"
                onClick={() => onReject(contract.id)}
              >
                <ThumbsDown className="h-3 w-3 mr-1" />
                Reject
              </Button>
              <Button
                variant="default"
                size="sm"
                className="h-7 text-xs px-2"
                onClick={() => onApprove(contract.id)}
              >
                <ThumbsUp className="h-3 w-3 mr-1" />
                Approve
              </Button>
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-4">
          <p className="text-xs text-muted-foreground">
            No contracts pending your approval.
          </p>
        </div>
      )}
    </div>
  );
};

export default PendingApprovals;
