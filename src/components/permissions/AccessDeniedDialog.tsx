import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ShieldAlert } from 'lucide-react';

interface AccessDeniedDialogProps {
  /**
   * Whether the dialog is open
   */
  open: boolean;
  
  /**
   * Function to call when the dialog is closed
   */
  onClose: () => void;
  
  /**
   * The title of the feature being accessed
   */
  featureTitle?: string;
  
  /**
   * The permission required to access the feature
   */
  requiredPermission?: string;
  
  /**
   * Custom message to display
   */
  customMessage?: string;
}

/**
 * A dialog that is shown when a user tries to access a feature they don't have permission for
 */
const AccessDeniedDialog: React.FC<AccessDeniedDialogProps> = ({
  open,
  onClose,
  featureTitle = 'this feature',
  requiredPermission,
  customMessage,
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader className="flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center mb-4">
            <ShieldAlert className="h-6 w-6 text-destructive" />
          </div>
          <DialogTitle>Access Denied</DialogTitle>
          <DialogDescription className="pt-2">
            {customMessage || (
              <>
                You don't have permission to access {featureTitle}.
                {requiredPermission && (
                  <> The required permission is <strong>{requiredPermission}</strong>.</>
                )}
              </>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-center pt-4">
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AccessDeniedDialog;
