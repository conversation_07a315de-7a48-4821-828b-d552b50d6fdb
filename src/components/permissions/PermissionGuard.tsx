import React, { useState, useEffect } from 'react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import { useUser } from '@clerk/clerk-react';

interface PermissionGuardProps {
  /**
   * The permission ID required to render the children
   */
  permissionId?: string;
  
  /**
   * Array of permission IDs - any of these permissions will allow access
   */
  anyPermissions?: string[];
  
  /**
   * Array of permission IDs - all of these permissions are required for access
   */
  allPermissions?: string[];
  
  /**
   * The workspace ID to check permissions against (defaults to current workspace)
   */
  workspaceId?: string;
  
  /**
   * Content to render when the user has the required permissions
   */
  children: React.ReactNode;
  
  /**
   * Optional content to render when the user doesn't have the required permissions
   */
  fallback?: React.ReactNode;
  
  /**
   * Whether to show a loading state while checking permissions
   */
  showLoading?: boolean;
}

/**
 * A component that conditionally renders its children based on user permissions
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissionId,
  anyPermissions,
  allPermissions,
  workspaceId,
  children,
  fallback = null,
  showLoading = false,
}) => {
  const { user } = useUser();
  const { 
    currentWorkspace, 
    hasPermission, 
    hasAnyPermission
  } = useClerkWorkspace();
  
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  
  useEffect(() => {
    const checkPermissions = async () => {
      if (!user) {
        setHasAccess(false);
        return;
      }
      
      // Use the current workspace ID if none is provided
      const wsId = workspaceId || currentWorkspace?.id;
      
      if (!wsId) {
        setHasAccess(false);
        return;
      }
      
      try {
        // Check for a single permission
        if (permissionId) {
          const result = await hasPermission(wsId, user.id, permissionId);
          setHasAccess(result);
          return;
        }
        
        // Check for any of the provided permissions
        if (anyPermissions && anyPermissions.length > 0) {
          const result = await hasAnyPermission(wsId, user.id, anyPermissions);
          setHasAccess(result);
          return;
        }
        
        // Check for all of the provided permissions
        if (allPermissions && allPermissions.length > 0) {
          // We need to check each permission individually
          const results = await Promise.all(
            allPermissions.map(perm => hasPermission(wsId, user.id, perm))
          );
          
          // User needs to have all permissions
          setHasAccess(results.every(result => result === true));
          return;
        }
        
        // If no permissions are specified, default to allowing access
        setHasAccess(true);
      } catch (error) {
        console.error('Error checking permissions:', error);
        setHasAccess(false);
      }
    };
    
    checkPermissions();
  }, [
    user, 
    currentWorkspace, 
    workspaceId, 
    permissionId, 
    anyPermissions, 
    allPermissions, 
    hasPermission,
    hasAnyPermission
  ]);
  
  // Show loading state if permissions are still being checked
  if (hasAccess === null) {
    if (showLoading) {
      return (
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
        </div>
      );
    }
    return null;
  }
  
  // Render children if user has permission, otherwise render fallback
  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;
