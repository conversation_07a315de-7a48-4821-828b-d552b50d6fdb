import React, { useState, useRef, useEffect } from "react";
import { Search, X, Clock, Filter, ChevronDown } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";

export interface SearchField {
  key: string;
  label: string;
  placeholder?: string;
  type?: "text" | "select" | "date";
  options?: { value: string; label: string }[];
}

export interface SearchQuery {
  global?: string;
  fields?: Record<string, string>;
  operator?: "AND" | "OR";
}

export interface EnhancedSearchProps {
  value: SearchQuery;
  onChange: (query: SearchQuery) => void;
  placeholder?: string;
  fields?: SearchField[];
  recentSearches?: string[];
  suggestions?: string[];
  showAdvanced?: boolean;
  showRecentSearches?: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  debounceMs?: number;
  onSaveSearch?: (query: SearchQuery, name: string) => void;
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  value,
  onChange,
  placeholder = "Search...",
  fields = [],
  recentSearches = [],
  suggestions = [],
  showAdvanced = true,
  showRecentSearches = true,
  className,
  size = "md",
  debounceMs = 300,
  onSaveSearch,
}) => {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [isSuggestionsOpen, setIsSuggestionsOpen] = useState(false);
  const [localValue, setLocalValue] = useState(value.global || "");
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Debounced search
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      onChange({
        ...value,
        global: localValue,
      });
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [localValue, debounceMs]);

  // Size classes
  const sizeClasses = {
    sm: "h-8 text-sm",
    md: "h-9",
    lg: "h-10 text-base",
  };

  const iconSizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  // Handle field search
  const handleFieldChange = (fieldKey: string, fieldValue: string) => {
    const newFields = { ...value.fields };
    if (fieldValue) {
      newFields[fieldKey] = fieldValue;
    } else {
      delete newFields[fieldKey];
    }

    onChange({
      ...value,
      fields: newFields,
    });
  };

  // Handle recent search selection
  const handleRecentSearchSelect = (searchTerm: string) => {
    setLocalValue(searchTerm);
    setIsSuggestionsOpen(false);
    inputRef.current?.focus();
  };

  // Clear search
  const handleClear = () => {
    setLocalValue("");
    onChange({
      global: "",
      fields: {},
      operator: value.operator,
    });
  };

  // Get active field filters count
  const activeFieldsCount = Object.keys(value.fields || {}).length;

  // Check if search has content
  const hasContent = localValue || activeFieldsCount > 0;

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className={cn(
          "absolute left-2.5 top-1/2 transform -translate-y-1/2 text-muted-foreground",
          iconSizeClasses[size]
        )} />
        
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={localValue}
          onChange={(e) => setLocalValue(e.target.value)}
          onFocus={() => setIsSuggestionsOpen(true)}
          className={cn(
            "pl-8 pr-20",
            sizeClasses[size],
            hasContent && "ring-1 ring-primary/20"
          )}
        />

        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {/* Advanced search toggle */}
          {showAdvanced && fields.length > 0 && (
            <Popover open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-6 w-6",
                    activeFieldsCount > 0 && "text-primary bg-primary/10"
                  )}
                >
                  <Filter className="h-3 w-3" />
                  {activeFieldsCount > 0 && (
                    <Badge
                      variant="secondary"
                      className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs"
                    >
                      {activeFieldsCount}
                    </Badge>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Advanced Search</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onChange({ ...value, fields: {} })}
                      className="h-6 text-xs"
                    >
                      Clear All
                    </Button>
                  </div>
                  
                  <div className="space-y-3">
                    {fields.map((field) => (
                      <div key={field.key} className="space-y-1">
                        <label className="text-sm font-medium">
                          {field.label}
                        </label>
                        <Input
                          placeholder={field.placeholder || `Search ${field.label.toLowerCase()}...`}
                          value={value.fields?.[field.key] || ""}
                          onChange={(e) => handleFieldChange(field.key, e.target.value)}
                          className="h-8"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}

          {/* Clear button */}
          {hasContent && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClear}
              className="h-6 w-6"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Search suggestions */}
      {isSuggestionsOpen && (showRecentSearches || suggestions.length > 0) && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1">
          <Command className="rounded-md border shadow-md bg-background">
            <CommandList className="max-h-60">
              {showRecentSearches && recentSearches.length > 0 && (
                <CommandGroup heading="Recent Searches">
                  {recentSearches.slice(0, 5).map((search, index) => (
                    <CommandItem
                      key={index}
                      onSelect={() => handleRecentSearchSelect(search)}
                      className="cursor-pointer"
                    >
                      <Clock className="mr-2 h-3 w-3 text-muted-foreground" />
                      {search}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
              
              {suggestions.length > 0 && (
                <CommandGroup heading="Suggestions">
                  {suggestions.slice(0, 5).map((suggestion, index) => (
                    <CommandItem
                      key={index}
                      onSelect={() => handleRecentSearchSelect(suggestion)}
                      className="cursor-pointer"
                    >
                      <Search className="mr-2 h-3 w-3 text-muted-foreground" />
                      {suggestion}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
              
              {recentSearches.length === 0 && suggestions.length === 0 && (
                <CommandEmpty>No suggestions available</CommandEmpty>
              )}
            </CommandList>
          </Command>
        </div>
      )}

      {/* Click outside to close suggestions */}
      {isSuggestionsOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsSuggestionsOpen(false)}
        />
      )}
    </div>
  );
};

export default EnhancedSearch;
