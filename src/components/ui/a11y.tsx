import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

// VisuallyHidden component - hides content visually but keeps it accessible to screen readers
interface VisuallyHiddenProps {
  children: ReactNode;
  as?: React.ElementType;
}

export const VisuallyHidden: React.FC<VisuallyHiddenProps> = ({
  children,
  as: Component = "span",
}) => {
  return (
    <Component
      className="absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0"
      style={{ clip: "rect(0, 0, 0, 0)" }}
    >
      {children}
    </Component>
  );
};

// SkipLink component - allows keyboard users to skip to main content
interface SkipLinkProps {
  targetId: string;
  className?: string;
  children?: ReactNode;
}

export const SkipLink: React.FC<SkipLinkProps> = ({
  targetId,
  className,
  children = "Skip to main content",
}) => {
  return (
    <a
      href={`#${targetId}`}
      className={cn(
        "fixed top-0 left-0 z-50 p-2 bg-background text-foreground transform -translate-y-full focus:translate-y-0 transition-transform",
        className
      )}
    >
      {children}
    </a>
  );
};

// FocusTrap component - traps focus within a component (useful for modals)
interface FocusTrapProps {
  children: ReactNode;
  active?: boolean;
  className?: string;
}

export const FocusTrap: React.FC<FocusTrapProps> = ({
  children,
  active = true,
  className,
}) => {
  const startRef = React.useRef<HTMLDivElement>(null);
  const endRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!active) return;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== "Tab") return;

      // Get all focusable elements
      const container = startRef.current?.parentElement;
      if (!container) return;

      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      // Handle tab and shift+tab to create a focus loop
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    };

    document.addEventListener("keydown", handleTabKey);
    return () => {
      document.removeEventListener("keydown", handleTabKey);
    };
  }, [active]);

  return (
    <div className={className}>
      {active && (
        <div
          ref={startRef}
          tabIndex={0}
          onFocus={() => endRef.current?.focus()}
          aria-hidden="true"
        />
      )}
      {children}
      {active && (
        <div
          ref={endRef}
          tabIndex={0}
          onFocus={() => startRef.current?.focus()}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

// LiveRegion component - announces dynamic content changes to screen readers
interface LiveRegionProps {
  children: ReactNode;
  "aria-live"?: "polite" | "assertive" | "off";
  "aria-atomic"?: boolean;
  "aria-relevant"?: "additions" | "removals" | "text" | "all";
  className?: string;
}

export const LiveRegion: React.FC<LiveRegionProps> = ({
  children,
  "aria-live": ariaLive = "polite",
  "aria-atomic": ariaAtomic = true,
  "aria-relevant": ariaRelevant = "additions text" as const,
  className,
}) => {
  return (
    <div
      aria-live={ariaLive}
      aria-atomic={ariaAtomic}
      aria-relevant={ariaRelevant}
      className={cn("sr-only", className)}
    >
      {children}
    </div>
  );
};
