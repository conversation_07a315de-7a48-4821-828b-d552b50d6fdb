import React, { useState, useCallback, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { ChevronUp, ChevronDown, ChevronsUpDown } from "lucide-react";

export type SortDirection = "asc" | "desc" | null;

export interface SortConfig {
  key: string;
  direction: SortDirection;
}

export interface ColumnDef<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  className?: string;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  accessor?: (row: T) => any;
}

interface EnhancedTableProps<T = any> {
  data: T[];
  columns: ColumnDef<T>[];
  sortConfig?: SortConfig;
  onSort?: (config: SortConfig) => void;
  selectedRows?: string[];
  onRowSelect?: (id: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  getRowId?: (row: T) => string;
  onRowClick?: (row: T, index: number) => void;
  onRowDoubleClick?: (row: T, index: number) => void;
  className?: string;
  stickyHeader?: boolean;
  hoverable?: boolean;
  striped?: boolean;
  compact?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  keyboardNavigation?: boolean;
}

const EnhancedTable = <T extends Record<string, any>>({
  data,
  columns,
  sortConfig,
  onSort,
  selectedRows = [],
  onRowSelect,
  onSelectAll,
  getRowId = (row: T) => row.id,
  onRowClick,
  onRowDoubleClick,
  className,
  stickyHeader = false,
  hoverable = true,
  striped = false,
  compact = false,
  loading = false,
  emptyMessage = "No data available",
  keyboardNavigation = true,
}: EnhancedTableProps<T>) => {
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1);
  const tableRef = useRef<HTMLTableElement>(null);
  const rowRefs = useRef<(HTMLTableRowElement | null)[]>([]);

  // Handle sorting
  const handleSort = useCallback((columnKey: string) => {
    if (!onSort) return;

    let newDirection: SortDirection = "asc";
    if (sortConfig?.key === columnKey) {
      if (sortConfig.direction === "asc") {
        newDirection = "desc";
      } else if (sortConfig.direction === "desc") {
        newDirection = null;
      }
    }

    onSort({ key: columnKey, direction: newDirection });
  }, [sortConfig, onSort]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!keyboardNavigation) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!tableRef.current?.contains(document.activeElement)) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setFocusedRowIndex(prev => Math.min(prev + 1, data.length - 1));
          break;
        case "ArrowUp":
          e.preventDefault();
          setFocusedRowIndex(prev => Math.max(prev - 1, 0));
          break;
        case "Enter":
        case " ":
          e.preventDefault();
          if (focusedRowIndex >= 0 && onRowClick) {
            onRowClick(data[focusedRowIndex], focusedRowIndex);
          }
          break;
        case "Escape":
          setFocusedRowIndex(-1);
          tableRef.current?.blur();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [keyboardNavigation, focusedRowIndex, data, onRowClick]);

  // Focus management
  useEffect(() => {
    if (focusedRowIndex >= 0 && rowRefs.current[focusedRowIndex]) {
      rowRefs.current[focusedRowIndex]?.focus();
    }
  }, [focusedRowIndex]);

  // Render sort icon
  const renderSortIcon = (column: ColumnDef<T>) => {
    if (!column.sortable) return null;

    const isActive = sortConfig?.key === column.key;
    const direction = isActive ? sortConfig?.direction : null;

    if (direction === "asc") {
      return <ChevronUp className="h-4 w-4" />;
    } else if (direction === "desc") {
      return <ChevronDown className="h-4 w-4" />;
    } else {
      return <ChevronsUpDown className="h-4 w-4 opacity-50" />;
    }
  };

  // Handle row selection
  const handleRowSelect = (row: T, selected: boolean) => {
    if (onRowSelect) {
      onRowSelect(getRowId(row), selected);
    }
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (onSelectAll) {
      onSelectAll(selected);
    }
  };

  // Check if all rows are selected
  const allSelected = data.length > 0 && selectedRows.length === data.length;
  const someSelected = selectedRows.length > 0 && selectedRows.length < data.length;

  return (
    <div className={cn("relative w-full overflow-auto", className)}>
      <table
        ref={tableRef}
        className="enhanced-table"
        tabIndex={keyboardNavigation ? 0 : -1}
      >
        <thead
          className={cn(
            "[&_tr]:border-b",
            stickyHeader && "sticky top-0 z-10 bg-background"
          )}
        >
          <tr>
            {/* Select all checkbox */}
            {onRowSelect && (
              <th className="h-10 px-2 text-left align-middle font-medium text-muted-foreground w-12">
                <input
                  type="checkbox"
                  checked={allSelected}
                  ref={(el) => {
                    if (el) el.indeterminate = someSelected;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-border"
                />
              </th>
            )}
            
            {/* Column headers */}
            {columns.map((column) => (
              <th
                key={column.key}
                className={cn(
                  "h-10 px-2 text-left align-middle font-medium text-muted-foreground",
                  column.sortable && "cursor-pointer select-none hover:text-foreground transition-colors",
                  column.width && `w-[${column.width}]`,
                  column.className
                )}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className="flex items-center gap-2">
                  <span>{column.label}</span>
                  {renderSortIcon(column)}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        
        <tbody className="[&_tr:last-child]:border-0">
          {loading ? (
            <tr>
              <td colSpan={columns.length + (onRowSelect ? 1 : 0)} className="h-24 text-center">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2 text-muted-foreground">Loading...</span>
                </div>
              </td>
            </tr>
          ) : data.length === 0 ? (
            <tr>
              <td colSpan={columns.length + (onRowSelect ? 1 : 0)} className="h-24 text-center text-muted-foreground">
                {emptyMessage}
              </td>
            </tr>
          ) : (
            data.map((row, index) => {
              const rowId = getRowId(row);
              const isSelected = selectedRows.includes(rowId);
              const isFocused = focusedRowIndex === index;

              return (
                <tr
                  key={rowId}
                  ref={(el) => (rowRefs.current[index] = el)}
                  className={cn(
                    "border-b transition-colors",
                    hoverable && "hover:bg-muted/50",
                    striped && index % 2 === 1 && "bg-muted/25",
                    isSelected && "bg-muted/50",
                    isFocused && "ring-2 ring-primary ring-inset",
                    compact ? "h-10" : "h-12",
                    (onRowClick || keyboardNavigation) && "cursor-pointer",
                    "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-inset"
                  )}
                  onClick={() => {
                    onRowClick?.(row, index);
                    setFocusedRowIndex(index);
                  }}
                  onDoubleClick={() => onRowDoubleClick?.(row, index)}
                  tabIndex={keyboardNavigation ? 0 : -1}
                  onFocus={() => setFocusedRowIndex(index)}
                >
                  {/* Row selection checkbox */}
                  {onRowSelect && (
                    <td className="p-2 align-middle">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleRowSelect(row, e.target.checked);
                        }}
                        className="rounded border-border"
                      />
                    </td>
                  )}
                  
                  {/* Row cells */}
                  {columns.map((column) => {
                    const value = column.accessor ? column.accessor(row) : row[column.key];
                    const content = column.render ? column.render(value, row, index) : value;

                    return (
                      <td
                        key={column.key}
                        className={cn(
                          "p-2 align-middle",
                          column.className
                        )}
                      >
                        {content}
                      </td>
                    );
                  })}
                </tr>
              );
            })
          )}
        </tbody>
      </table>
    </div>
  );
};

export default EnhancedTable;
