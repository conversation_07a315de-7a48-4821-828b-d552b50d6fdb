import React from 'react';
import { But<PERSON> } from './button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ApiErrorFallbackProps {
  error?: {
    message?: string;
    code?: string;
    details?: string;
  };
  resetError?: () => void;
  retry?: () => void;
  title?: string;
  description?: string;
  showDetails?: boolean;
}

/**
 * A component to display when an API call fails
 */
export const ApiErrorFallback: React.FC<ApiErrorFallbackProps> = ({
  error,
  resetError,
  retry,
  title = 'Something went wrong',
  description = 'There was an error loading the data. Please try again later.',
  showDetails = false,
}) => {
  const errorMessage = error?.message || 'An unexpected error occurred';
  const errorCode = error?.code || 'UNKNOWN';
  const errorDetails = error?.details;

  return (
    <Card className="w-full border border-destructive/20 bg-destructive/5">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-destructive" />
          <CardTitle className="text-base font-medium text-destructive">{title}</CardTitle>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="text-sm text-muted-foreground">
          <p className="font-medium text-destructive">{errorMessage}</p>
          {errorCode && <p className="mt-1 text-xs">Error code: {errorCode}</p>}
        </div>
        {showDetails && errorDetails && (
          <details className="mt-2">
            <summary className="cursor-pointer text-xs text-muted-foreground hover:text-foreground">
              Show technical details
            </summary>
            <pre className="mt-2 max-h-40 overflow-auto rounded bg-muted p-2 text-xs">
              {errorDetails}
            </pre>
          </details>
        )}
      </CardContent>
      <CardFooter className="flex gap-2 pt-1">
        {retry && (
          <Button
            variant="outline"
            size="sm"
            onClick={retry}
            className="gap-1.5"
          >
            <RefreshCw className="h-3.5 w-3.5" />
            Try Again
          </Button>
        )}
        {resetError && (
          <Button
            variant="ghost"
            size="sm"
            onClick={resetError}
          >
            Dismiss
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

/**
 * A simpler version of the error fallback for inline use
 */
export const InlineApiError: React.FC<ApiErrorFallbackProps> = ({
  error,
  retry,
  title,
  description,
}) => {
  const errorMessage = error?.message || 'An unexpected error occurred';
  
  return (
    <div className="flex flex-col gap-2 rounded-md border border-destructive/20 bg-destructive/5 p-3">
      <div className="flex items-center gap-2">
        <AlertCircle className="h-4 w-4 text-destructive" />
        <p className="text-sm font-medium text-destructive">{title || errorMessage}</p>
      </div>
      {description && <p className="text-xs text-muted-foreground">{description}</p>}
      {retry && (
        <Button
          variant="outline"
          size="sm"
          onClick={retry}
          className="mt-1 gap-1.5 self-start"
        >
          <RefreshCw className="h-3 w-3" />
          Retry
        </Button>
      )}
    </div>
  );
};
