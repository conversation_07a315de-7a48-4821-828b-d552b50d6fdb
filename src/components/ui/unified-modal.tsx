import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Loader2, X } from 'lucide-react';
import { cn } from '@/lib/utils';

// Unified modal types
export type ModalType = 'dialog' | 'alert' | 'drawer' | 'fullscreen';
export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full';
export type ModalVariant = 'default' | 'destructive' | 'success' | 'warning';

export interface ModalAction {
  label: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  onClick: () => void | Promise<void>;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
}

export interface UnifiedModalProps {
  // Core props
  open: boolean;
  onOpenChange: (open: boolean) => void;
  
  // Content props
  title?: string;
  description?: string;
  children?: React.ReactNode;
  
  // Behavior props
  type?: ModalType;
  size?: ModalSize;
  variant?: ModalVariant;
  closable?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  
  // Actions
  actions?: ModalAction[];
  primaryAction?: ModalAction;
  secondaryAction?: ModalAction;
  
  // Loading state
  loading?: boolean;
  loadingText?: string;
  
  // Custom styling
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  
  // Responsive behavior
  mobileAsDrawer?: boolean;
  
  // Callbacks
  onClose?: () => void;
  onOpen?: () => void;
}

const sizeClasses: Record<ModalSize, string> = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '3xl': 'max-w-3xl',
  '4xl': 'max-w-4xl',
  '5xl': 'max-w-5xl',
  full: 'max-w-[95vw] h-[95vh]',
};

const variantClasses: Record<ModalVariant, string> = {
  default: '',
  destructive: 'border-destructive/50',
  success: 'border-green-500/50',
  warning: 'border-yellow-500/50',
};

const UnifiedModal: React.FC<UnifiedModalProps> = ({
  open,
  onOpenChange,
  title,
  description,
  children,
  type = 'dialog',
  size = 'md',
  variant = 'default',
  closable = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  actions = [],
  primaryAction,
  secondaryAction,
  loading = false,
  loadingText = 'Loading...',
  className,
  contentClassName,
  headerClassName,
  footerClassName,
  mobileAsDrawer = false,
  onClose,
  onOpen,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle open/close callbacks
  useEffect(() => {
    if (open && onOpen) {
      onOpen();
    }
  }, [open, onOpen]);

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && onClose) {
      onClose();
    }
    onOpenChange(newOpen);
  };

  const handleActionClick = async (action: ModalAction, actionId: string) => {
    if (action.disabled || actionLoading) return;

    try {
      setActionLoading(actionId);
      await action.onClick();
    } catch (error) {
      console.error('Modal action error:', error);
    } finally {
      setActionLoading(null);
    }
  };

  // Render action buttons
  const renderActions = () => {
    const allActions = [
      ...(secondaryAction ? [{ ...secondaryAction, id: 'secondary' }] : []),
      ...(primaryAction ? [{ ...primaryAction, id: 'primary' }] : []),
      ...actions.map((action, index) => ({ ...action, id: `action-${index}` })),
    ];

    if (allActions.length === 0) return null;

    return (
      <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
        {allActions.map((action) => (
          <Button
            key={action.id}
            variant={action.variant || 'default'}
            onClick={() => handleActionClick(action, action.id)}
            disabled={action.disabled || actionLoading !== null}
            className="w-full sm:w-auto"
          >
            {actionLoading === action.id ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </>
            ) : (
              <>
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label}
              </>
            )}
          </Button>
        ))}
      </div>
    );
  };

  // Render loading state
  const renderLoadingState = () => (
    <div className="flex flex-col items-center justify-center py-8">
      <Loader2 className="h-8 w-8 animate-spin mb-4" />
      <p className="text-muted-foreground">{loadingText}</p>
    </div>
  );

  // Render content
  const renderContent = () => (
    <>
      {(title || description) && (
        <div className={cn("space-y-2", headerClassName)}>
          {title && <h2 className="text-lg font-semibold">{title}</h2>}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      
      <div className="flex-1">
        {loading ? renderLoadingState() : children}
      </div>
      
      {(actions.length > 0 || primaryAction || secondaryAction) && (
        <div className={cn("pt-4", footerClassName)}>
          {renderActions()}
        </div>
      )}
    </>
  );

  // Alert Dialog
  if (type === 'alert') {
    return (
      <AlertDialog open={open} onOpenChange={handleOpenChange}>
        <AlertDialogContent className={cn(sizeClasses[size], variantClasses[variant], contentClassName)}>
          <AlertDialogHeader>
            {title && <AlertDialogTitle>{title}</AlertDialogTitle>}
            {description && <AlertDialogDescription>{description}</AlertDialogDescription>}
          </AlertDialogHeader>
          
          {children && !loading && (
            <div className="py-4">
              {children}
            </div>
          )}
          
          {loading && renderLoadingState()}
          
          <AlertDialogFooter>
            {secondaryAction && (
              <AlertDialogCancel
                onClick={() => handleActionClick(secondaryAction, 'secondary')}
                disabled={secondaryAction.disabled || actionLoading !== null}
              >
                {actionLoading === 'secondary' ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </>
                ) : (
                  secondaryAction.label
                )}
              </AlertDialogCancel>
            )}
            {primaryAction && (
              <AlertDialogAction
                onClick={() => handleActionClick(primaryAction, 'primary')}
                disabled={primaryAction.disabled || actionLoading !== null}
                className={primaryAction.variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
              >
                {actionLoading === 'primary' ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading...
                  </>
                ) : (
                  <>
                    {primaryAction.icon && <span className="mr-2">{primaryAction.icon}</span>}
                    {primaryAction.label}
                  </>
                )}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  // Drawer (mobile-first or explicit)
  if (type === 'drawer' || (mobileAsDrawer && isMobile)) {
    return (
      <Drawer open={open} onOpenChange={handleOpenChange}>
        <DrawerContent className={cn(variantClasses[variant], contentClassName)}>
          <div className="mx-auto w-full max-w-sm">
            <DrawerHeader className={headerClassName}>
              {title && <DrawerTitle>{title}</DrawerTitle>}
              {description && <DrawerDescription>{description}</DrawerDescription>}
            </DrawerHeader>
            
            <div className="p-4 pb-0">
              {loading ? renderLoadingState() : children}
            </div>
            
            {(actions.length > 0 || primaryAction || secondaryAction) && (
              <DrawerFooter className={footerClassName}>
                {renderActions()}
              </DrawerFooter>
            )}
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Regular Dialog (default)
  const dialogSizeClass = size === 'full' ? 'max-w-[95vw] h-[95vh]' : sizeClasses[size];
  
  return (
    <Dialog 
      open={open} 
      onOpenChange={closeOnOverlayClick ? handleOpenChange : undefined}
    >
      <DialogContent 
        className={cn(
          dialogSizeClass,
          variantClasses[variant],
          size === 'full' && 'p-0',
          contentClassName
        )}
        onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
      >
        {!closable && (
          <style dangerouslySetInnerHTML={{
            __html: `
              [data-radix-dialog-content] button[aria-label="Close"] {
                display: none !important;
              }
            `
          }} />
        )}
        
        {size === 'full' ? (
          <div className="flex flex-col h-full">
            {(title || description) && (
              <DialogHeader className={cn("px-6 py-4 border-b", headerClassName)}>
                {title && <DialogTitle>{title}</DialogTitle>}
                {description && <DialogDescription>{description}</DialogDescription>}
              </DialogHeader>
            )}
            
            <div className="flex-1 overflow-auto p-6">
              {loading ? renderLoadingState() : children}
            </div>
            
            {(actions.length > 0 || primaryAction || secondaryAction) && (
              <DialogFooter className={cn("px-6 py-4 border-t", footerClassName)}>
                {renderActions()}
              </DialogFooter>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {(title || description) && (
              <DialogHeader className={headerClassName}>
                {title && <DialogTitle>{title}</DialogTitle>}
                {description && <DialogDescription>{description}</DialogDescription>}
              </DialogHeader>
            )}
            
            <div>
              {loading ? renderLoadingState() : children}
            </div>
            
            {(actions.length > 0 || primaryAction || secondaryAction) && (
              <DialogFooter className={footerClassName}>
                {renderActions()}
              </DialogFooter>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default UnifiedModal;
