import React, { useState } from "react";
import { Search, Filter, SlidersHorizontal, Bookmark, Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import EnhancedSearch, { SearchQuery, SearchField } from "./enhanced-search";
import EnhancedFilterPanel, { FilterGroup, FilterValues, FilterChips } from "./enhanced-filter-panel";
import { cn } from "@/lib/utils";

export interface SearchFilterBarProps {
  // Search props
  searchValue: SearchQuery;
  onSearchChange: (query: SearchQuery) => void;
  searchFields?: SearchField[];
  searchPlaceholder?: string;
  recentSearches?: string[];
  suggestions?: string[];

  // Filter props
  filterValues: FilterValues;
  onFilterChange: (values: FilterValues) => void;
  filterGroups?: FilterGroup[];

  // Saved searches/filters
  savedFilters?: { name: string; values: FilterValues }[];
  onSaveFilter?: (name: string, values: FilterValues) => void;
  onLoadFilter?: (name: string) => void;

  // UI props
  className?: string;
  showFilterPanel?: boolean;
  showFilterChips?: boolean;
  showSavedFilters?: boolean;
  compact?: boolean;
  
  // Results info
  totalResults?: number;
  showResultsCount?: boolean;
}

const SearchFilterBar: React.FC<SearchFilterBarProps> = ({
  searchValue,
  onSearchChange,
  searchFields = [],
  searchPlaceholder = "Search...",
  recentSearches = [],
  suggestions = [],
  filterValues,
  onFilterChange,
  filterGroups = [],
  savedFilters = [],
  onSaveFilter,
  onLoadFilter,
  className,
  showFilterPanel = true,
  showFilterChips = true,
  showSavedFilters = true,
  compact = false,
  totalResults,
  showResultsCount = true,
}) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [saveFilterName, setSaveFilterName] = useState("");

  // Count active filters
  const activeFilterCount = Object.keys(filterValues).filter(key => {
    const value = filterValues[key];
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === "object" && value !== null) {
      return Object.keys(value).some(k => value[k]);
    }
    return value !== undefined && value !== null && value !== "";
  }).length;

  // Check if search is active
  const hasActiveSearch = !!(searchValue.global || (searchValue.fields && Object.keys(searchValue.fields).length > 0));

  // Clear all filters and search
  const handleClearAll = () => {
    onSearchChange({ global: "", fields: {}, operator: searchValue.operator });
    onFilterChange({});
  };

  // Save current filter configuration
  const handleSaveFilter = () => {
    if (saveFilterName.trim() && onSaveFilter) {
      onSaveFilter(saveFilterName.trim(), filterValues);
      setSaveFilterName("");
      setIsSaveDialogOpen(false);
    }
  };

  return (
    <div className={cn("space-y-3", className)}>
      {/* Main search and filter bar */}
      <div className={cn(
        "flex items-center gap-3",
        compact ? "flex-col sm:flex-row" : "flex-row"
      )}>
        {/* Search */}
        <div className="flex-1 min-w-0">
          <EnhancedSearch
            value={searchValue}
            onChange={onSearchChange}
            placeholder={searchPlaceholder}
            fields={searchFields}
            recentSearches={recentSearches}
            suggestions={suggestions}
            showAdvanced={searchFields.length > 0}
            showRecentSearches={recentSearches.length > 0}
            size={compact ? "sm" : "md"}
          />
        </div>

        {/* Filter toggle */}
        {showFilterPanel && filterGroups.length > 0 && (
          <Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size={compact ? "sm" : "default"}
                className={cn(
                  "flex items-center gap-2",
                  activeFilterCount > 0 && "border-primary text-primary"
                )}
              >
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filters</span>
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[400px] sm:w-[540px]">
              <SheetHeader>
                <SheetTitle>Filters</SheetTitle>
                <SheetDescription>
                  Refine your search results with advanced filters
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6">
                <EnhancedFilterPanel
                  filters={filterGroups}
                  values={filterValues}
                  onChange={onFilterChange}
                  onClear={() => onFilterChange({})}
                  savedFilters={savedFilters}
                  collapsible={false}
                />
              </div>
              
              {/* Save filter button */}
              {onSaveFilter && activeFilterCount > 0 && (
                <div className="mt-6 pt-6 border-t">
                  <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full">
                        <Save className="mr-2 h-4 w-4" />
                        Save Filter Configuration
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Save Filter Configuration</DialogTitle>
                        <DialogDescription>
                          Give your filter configuration a name to save it for later use.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="filter-name">Filter Name</Label>
                          <Input
                            id="filter-name"
                            placeholder="e.g., Active Contracts This Month"
                            value={saveFilterName}
                            onChange={(e) => setSaveFilterName(e.target.value)}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setIsSaveDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleSaveFilter}
                          disabled={!saveFilterName.trim()}
                        >
                          Save Filter
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}
            </SheetContent>
          </Sheet>
        )}

        {/* Saved filters dropdown */}
        {showSavedFilters && savedFilters.length > 0 && onLoadFilter && (
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size={compact ? "sm" : "default"}>
                <Bookmark className="h-4 w-4" />
                <span className="hidden sm:inline ml-2">Saved</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px]">
              <SheetHeader>
                <SheetTitle>Saved Filters</SheetTitle>
                <SheetDescription>
                  Quick access to your saved filter configurations
                </SheetDescription>
              </SheetHeader>
              <div className="mt-6 space-y-2">
                {savedFilters.map((filter, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    className="w-full justify-start"
                    onClick={() => {
                      onLoadFilter(filter.name);
                      setIsFilterOpen(false);
                    }}
                  >
                    <Bookmark className="mr-2 h-4 w-4" />
                    {filter.name}
                  </Button>
                ))}
              </div>
            </SheetContent>
          </Sheet>
        )}

        {/* Clear all button */}
        {(hasActiveSearch || activeFilterCount > 0) && (
          <Button
            variant="ghost"
            size={compact ? "sm" : "default"}
            onClick={handleClearAll}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Filter chips */}
      {showFilterChips && filterGroups.length > 0 && (
        <FilterChips
          filters={filterGroups}
          values={filterValues}
          onChange={onFilterChange}
          onClear={() => onFilterChange({})}
        />
      )}

      {/* Results count */}
      {showResultsCount && totalResults !== undefined && (
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            {totalResults} result{totalResults !== 1 ? "s" : ""} found
            {(hasActiveSearch || activeFilterCount > 0) && " with current filters"}
          </span>
        </div>
      )}
    </div>
  );
};

export default SearchFilterBar;
