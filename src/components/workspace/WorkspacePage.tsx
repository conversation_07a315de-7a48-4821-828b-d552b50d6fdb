import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import WorkspaceManager from "./WorkspaceManager";

const WorkspacePage = () => {
  const { userWorkspaces, currentWorkspace } = useClerkWorkspace();

  return (
    <div className="container mx-auto py-4 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Workspaces</h1>
          <p className="text-xs text-muted-foreground">
            Manage your workspaces and user access
          </p>
        </div>
        <div className="flex items-center gap-2">
          {currentWorkspace && (
            <div className="text-xs">
              Current workspace: <span className="font-medium">{currentWorkspace.name}</span>
            </div>
          )}
        </div>
      </div>

      <div className="bg-card rounded-lg border shadow-sm">
        <WorkspaceManager />
      </div>
    </div>
  );
};

export default WorkspacePage;
