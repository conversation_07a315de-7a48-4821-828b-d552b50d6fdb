import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, MoreHorizontal, Plus, Users, UserCog, Shield, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useApi } from "@/lib/api";
import { useWorkspaces } from "@/hooks/useWorkspaces";
import { WorkspaceService } from "@/services/api-services";
import type { Workspace as ApiWorkspace } from "@/services/api-types";

interface Workspace {
  id: string;
  name: string;
  description?: string;
  members?: number;
  contracts?: number;
  createdBy?: string;
  createdDate?: string;
  isActive?: boolean;
}

interface WorkspaceListProps {
  workspaces?: Workspace[];
  onSelectWorkspace?: (workspace: Workspace) => void;
  onCreateWorkspace?: () => void;
  onManageWorkspace?: (workspace: Workspace) => void;
  onDeleteWorkspace?: (workspace: Workspace) => void;
  onManageUsersRoles?: (workspace: Workspace) => void;
}

const WorkspaceList = ({
  workspaces: propWorkspaces = [],
  onSelectWorkspace = () => {},
  onCreateWorkspace = () => {},
  onManageWorkspace = () => {},
  onDeleteWorkspace = () => {},
  onManageUsersRoles = () => {},
}: WorkspaceListProps) => {
  const { fetch } = useApi();
  const [workspaces, setWorkspaces] = useState<Workspace[]>(propWorkspaces);
  const [loading, setLoading] = useState(propWorkspaces.length === 0);
  const [error, setError] = useState<string | null>(null);

  // Use the centralized workspace hook
  const {
    workspaces: fetchedWorkspaces,
    isLoading: workspaceLoading,
    error: workspaceError,
    refreshWorkspaces,
  } = useWorkspaces({
    autoFetch: propWorkspaces.length === 0, // Only auto-fetch if no props provided
    onSuccess: (workspaces) => {
      console.log("✅ WorkspaceList: Workspaces loaded successfully:", workspaces.length);
    },
    onError: (error) => {
      console.error("❌ WorkspaceList: Error loading workspaces:", error);
    },
  });

  // Update local state based on props or fetched data
  useEffect(() => {
    if (propWorkspaces && propWorkspaces.length > 0) {
      // Use props workspaces if provided
      console.log("✅ WorkspaceList using props workspaces:", propWorkspaces.length);
      setWorkspaces(propWorkspaces);
      setLoading(false);
      setError(null);
    } else if (fetchedWorkspaces.length > 0) {
      // Use fetched workspaces if no props provided
      console.log("✅ WorkspaceList using fetched workspaces:", fetchedWorkspaces.length);
      setWorkspaces(fetchedWorkspaces);
      setLoading(workspaceLoading);
      setError(workspaceError);
    } else {
      // Update loading and error states
      setLoading(workspaceLoading);
      setError(workspaceError);
    }
  }, [propWorkspaces, fetchedWorkspaces, workspaceLoading, workspaceError]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Your Workspaces</h2>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading workspaces...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch your workspaces
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Shield className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading workspaces</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : workspaces.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No workspaces found</h3>
          <p className="text-muted-foreground mt-2">
            Create your first workspace to get started
          </p>
          <Button onClick={onCreateWorkspace} className="mt-4">
            <Plus className="mr-2 h-4 w-4" />
            Create Workspace
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {workspaces.map((workspace) => (
          <Card key={workspace.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center mr-2">
                    <span className="text-xs font-medium text-primary-foreground">
                      {workspace.name.charAt(0)}
                    </span>
                  </div>
                  <CardTitle className="text-lg">{workspace.name}</CardTitle>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onSelectWorkspace(workspace)}
                    >
                      Select Workspace
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onManageWorkspace(workspace)}
                    >
                      Edit Workspace
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => onDeleteWorkspace(workspace)}
                    >
                      Delete Workspace
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <CardDescription className="mt-2">
                {workspace.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-muted-foreground mr-1" />
                  <span className="text-muted-foreground">
                    {workspace.members} members
                  </span>
                </div>
                {workspace.contracts && (
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-muted-foreground mr-1" />
                    <span className="text-muted-foreground">
                      {workspace.contracts} contracts
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="pt-2 flex-col gap-3">
              <div className="w-full flex items-center justify-between">
                {workspace.createdBy && (
                  <span className="text-xs text-muted-foreground">
                    Created by {workspace.createdBy}
                  </span>
                )}
                {workspace.isActive && (
                  <Badge variant="outline" className="text-xs">
                    Active
                  </Badge>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => onManageUsersRoles(workspace)}
              >
                <UserCog className="h-4 w-4 mr-2" />
                Users & Roles
              </Button>
            </CardFooter>
          </Card>
          ))}

          {/* Create New Workspace Card */}
          <Card className="overflow-hidden border-dashed border-2 flex flex-col items-center justify-center p-6 h-[250px]">
            <div className="rounded-full bg-muted h-12 w-12 flex items-center justify-center mb-4">
              <Plus className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">Create New Workspace</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Set up a new workspace for your team or project
            </p>
            <Button onClick={onCreateWorkspace}>Create Workspace</Button>
          </Card>
        </div>
      )}
    </div>
  );
};

export default WorkspaceList;
