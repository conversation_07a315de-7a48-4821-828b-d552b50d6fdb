import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Save, Loader2 } from "lucide-react";
import { useApi } from "@/lib/api";
import { WorkspaceService } from "@/services/api-services";
import { useToast } from "@/components/ui/use-toast";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";

interface Workspace {
  id: string;
  name: string;
  description: string;
  members: number;
  contracts?: number;
  createdBy?: string;
  createdDate?: string;
  isActive?: boolean;
}

interface WorkspaceFormProps {
  workspace?: Workspace;
  onClose: () => void;
  onWorkspaceCreated?: (workspace: Omit<Workspace, "id">) => void;
  onWorkspaceUpdated?: (id: string, workspace: Partial<Workspace>) => void;
}

const WorkspaceForm = ({
  workspace,
  onClose,
  onWorkspaceCreated,
  onWorkspaceUpdated,
}: WorkspaceFormProps) => {
  const { fetch } = useApi();
  const { toast } = useToast();
  const { createWorkspace, updateWorkspace } = useClerkWorkspace();
  const isEditing = !!workspace;
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: workspace?.name || "",
    description: workspace?.description || "",
    visibility: "private",
    isActive: workspace?.isActive || false,
    template: "blank",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({ ...formData, [name]: checked });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isEditing && workspace) {
        // Update existing workspace using ClerkWorkspaceProvider
        const result = await updateWorkspace(workspace.id, {
          name: formData.name,
          description: formData.description
        });

        if (result && onWorkspaceUpdated) {
          toast({
            title: "Workspace updated",
            description: "The workspace has been updated successfully.",
          });
          onWorkspaceUpdated(workspace.id, {
            name: formData.name,
            description: formData.description,
            isActive: formData.isActive,
          });
        }
      } else {
        // Create new workspace using ClerkWorkspaceProvider
        const newWorkspace = await createWorkspace({
          name: formData.name,
          description: formData.description,
          members: 1, // Default to 1 member (the creator)
          contracts: 0,
          createdDate: new Date().toISOString().split('T')[0],
        });

        if (newWorkspace) {
          toast({
            title: "Workspace created",
            description: "The new workspace has been created successfully.",
          });

          // Call the callback with the workspace data (without ID since it's handled by ClerkWorkspaceProvider)
          if (onWorkspaceCreated) {
            onWorkspaceCreated({
              name: newWorkspace.name,
              description: newWorkspace.description || '',
              members: newWorkspace.members,
              contracts: 0,
              createdDate: newWorkspace.createdDate || new Date().toISOString().split('T')[0],
            });
          }

          // Close the form after successful creation
          if (onClose) {
            onClose();
          }
        }
      }
    } catch (error) {
      console.error("Error submitting workspace form:", error);
      toast({
        title: "Error",
        description: isEditing
          ? "Failed to update workspace. Please try again."
          : "Failed to create workspace. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" onClick={onClose} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h2 className="text-2xl font-bold">
            {isEditing ? "Edit Workspace" : "Create Workspace"}
          </h2>
          <p className="text-muted-foreground">
            {isEditing
              ? "Update workspace details and settings"
              : "Set up a new workspace for your team"}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Enter the basic details for your workspace
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Workspace Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter workspace name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter workspace description"
                rows={3}
              />
            </div>

            {!isEditing && (
              <div className="space-y-2">
                <Label htmlFor="template">Workspace Template</Label>
                <Select
                  value={formData.template}
                  onValueChange={(value) =>
                    handleSelectChange("template", value)
                  }
                >
                  <SelectTrigger id="template">
                    <SelectValue placeholder="Select template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="blank">Blank Workspace</SelectItem>
                    <SelectItem value="legal">Legal Team</SelectItem>
                    <SelectItem value="finance">Finance Team</SelectItem>
                    <SelectItem value="hr">HR Team</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Templates include predefined roles, permissions, and folder
                  structures
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Workspace Settings</CardTitle>
            <CardDescription>
              Configure additional settings for your workspace
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Visibility</p>
                <p className="text-sm text-muted-foreground">
                  Control who can see this workspace
                </p>
              </div>
              <Select
                value={formData.visibility}
                onValueChange={(value) =>
                  handleSelectChange("visibility", value)
                }
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="private">Private</SelectItem>
                  <SelectItem value="public">Public</SelectItem>
                  <SelectItem value="restricted">Restricted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            {isEditing && (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Active Status</p>
                    <p className="text-sm text-muted-foreground">
                      Set whether this workspace is active
                    </p>
                  </div>
                  <Switch
                    checked={formData.isActive}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("isActive", checked)
                    }
                  />
                </div>

                <Separator />
              </>
            )}

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Auto-Add New Users</p>
                <p className="text-sm text-muted-foreground">
                  Automatically add new users to this workspace
                </p>
              </div>
              <Switch defaultChecked={false} />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-4">
            <Button variant="outline" type="button" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? "Update Workspace" : "Create Workspace"}
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
};

export default WorkspaceForm;
