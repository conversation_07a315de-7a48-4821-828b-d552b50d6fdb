import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Users, Building, ArrowRight } from 'lucide-react';

interface NoWorkspacesScreenProps {
  onCreateWorkspace?: () => void;
  autoRedirect?: boolean;
}

const NoWorkspacesScreen: React.FC<NoWorkspacesScreenProps> = ({ 
  onCreateWorkspace,
  autoRedirect = false 
}) => {
  const navigate = useNavigate();

  const handleCreateWorkspace = () => {
    if (onCreateWorkspace) {
      onCreateWorkspace();
    } else {
      navigate('/app/workspaces');
    }
  };

  // Auto-redirect to workspace creation if enabled
  React.useEffect(() => {
    if (autoRedirect) {
      const timer = setTimeout(() => {
        handleCreateWorkspace();
      }, 3000); // Redirect after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [autoRedirect]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="max-w-2xl w-full space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <Building className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight">Welcome to LegalAI</h1>
          <p className="text-lg text-muted-foreground max-w-md mx-auto">
            You don't have access to any workspaces yet. Create your first workspace to get started with contract management.
          </p>
        </div>

        {/* Main Card */}
        <Card className="border-2 border-dashed border-muted-foreground/25">
          <CardHeader className="text-center pb-4">
            <CardTitle className="flex items-center justify-center gap-2">
              <Users className="w-5 h-5" />
              No Workspaces Available
            </CardTitle>
            <CardDescription>
              Workspaces help you organize contracts, collaborate with team members, and manage access permissions.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Organize Contracts</h4>
                <p className="text-sm text-muted-foreground">
                  Keep all your contracts organized by team, department, or project.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Team Collaboration</h4>
                <p className="text-sm text-muted-foreground">
                  Invite team members and assign roles for secure collaboration.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Access Control</h4>
                <p className="text-sm text-muted-foreground">
                  Control who can view, edit, and approve contracts in your workspace.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Analytics & Insights</h4>
                <p className="text-sm text-muted-foreground">
                  Track contract performance and get insights into your legal processes.
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button 
                onClick={handleCreateWorkspace}
                className="flex-1 h-12"
                size="lg"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Workspace
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/app/dashboard')}
                className="flex-1 h-12"
                size="lg"
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                Continue to Dashboard
              </Button>
            </div>

            {/* Auto-redirect notice */}
            {autoRedirect && (
              <div className="text-center pt-4 border-t">
                <p className="text-sm text-muted-foreground">
                  You'll be redirected to create a workspace in a few seconds...
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Help Text */}
        <div className="text-center space-y-2">
          <p className="text-sm text-muted-foreground">
            Need help getting started? 
            <Button variant="link" className="p-0 h-auto ml-1" onClick={() => window.open('/help', '_blank')}>
              Check our documentation
            </Button>
          </p>
          <p className="text-xs text-muted-foreground">
            If you believe you should have access to a workspace, contact your administrator.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NoWorkspacesScreen;
