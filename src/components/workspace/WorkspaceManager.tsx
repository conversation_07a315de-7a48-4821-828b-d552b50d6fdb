import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Search, Plus } from "lucide-react";
import WorkspaceList from "./WorkspaceList";
import WorkspaceForm from "./WorkspaceForm";
import UserAccessManager from "./UserAccessManager";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Workspace } from "@/lib/workspace-provider";

interface WorkspaceManagerProps {
  isOpen?: boolean;
}

const WorkspaceManager = ({ isOpen = false }: WorkspaceManagerProps) => {
  const [activeTab, setActiveTab] = useState("workspaces");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedWorkspace, setSelectedWorkspace] = useState<string | null>(
    null,
  );
  const [isCreatingWorkspace, setIsCreatingWorkspace] = useState(false);
  const [isEditingWorkspace, setIsEditingWorkspace] = useState(false);
  const [isManagingUsers, setIsManagingUsers] = useState(false);
  const [filteredWorkspaces, setFilteredWorkspaces] = useState<Workspace[]>([]);

  // Get workspaces from context
  const {
    userWorkspaces,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    getWorkspaceById,
    currentWorkspace,
    setCurrentWorkspace,
    getUserWorkspaces,
    canAccessWorkspace,
    isLoading
  } = useClerkWorkspace();

  // Get only the workspaces the user has access to
  const accessibleWorkspaces = getUserWorkspaces();

  // Filter workspaces based on search query and user access
  useEffect(() => {
    // Only update filtered workspaces if we're not loading
    if (!isLoading) {
      if (searchQuery.trim() === "") {
        setFilteredWorkspaces(accessibleWorkspaces);
      } else {
        const filtered = accessibleWorkspaces.filter(
          workspace =>
            workspace.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (workspace.description && workspace.description.toLowerCase().includes(searchQuery.toLowerCase()))
        );
        setFilteredWorkspaces(filtered);
      }
    }
  }, [searchQuery, accessibleWorkspaces, isLoading]);

  const handleCreateWorkspace = () => {
    setIsCreatingWorkspace(true);
    setSelectedWorkspace(null);
  };

  const handleEditWorkspace = (workspace: Workspace) => {
    setSelectedWorkspace(workspace.id);
    setIsEditingWorkspace(true);
  };

  const handleManageUsers = (workspace: Workspace) => {
    setSelectedWorkspace(workspace.id);
    setIsManagingUsers(true);
  };

  const handleCloseForm = () => {
    setIsCreatingWorkspace(false);
    setIsEditingWorkspace(false);
    setIsManagingUsers(false);
  };

  const handleWorkspaceCreated = async (newWorkspace: Omit<Workspace, "id">) => {
    try {
      // Workspace creation is now handled directly by WorkspaceForm using ClerkWorkspaceProvider
      // Just close the form since the workspace has already been created
      console.log("Workspace created:", newWorkspace);
      setIsCreatingWorkspace(false);
    } catch (error) {
      console.error("Error creating workspace:", error);
    }
  };

  const handleWorkspaceUpdated = async (id: string, updatedData: Partial<Workspace>) => {
    try {
      // Update the workspace using the context function
      const updatedWorkspace = await updateWorkspace(id, updatedData);
      console.log("Workspace updated:", updatedWorkspace);
      setIsEditingWorkspace(false);
    } catch (error) {
      console.error("Error updating workspace:", error);
    }
  };

  const handleDeleteWorkspace = async (workspace: Workspace) => {
    try {
      // Delete the workspace using the context function
      const success = await deleteWorkspace(workspace.id);
      console.log(`Workspace ${workspace.id} deleted:`, success);
    } catch (error) {
      console.error("Error deleting workspace:", error);
    }
  };

  const handleUsersUpdated = () => {
    setIsManagingUsers(false);
  };

  const handleSelectWorkspace = (workspace: Workspace) => {
    // Set the current workspace in the context
    setCurrentWorkspace(workspace);
  };

  const getSelectedWorkspace = () => {
    if (!selectedWorkspace) return undefined;

    const workspace = getWorkspaceById(selectedWorkspace);
    if (!workspace) return undefined;

    // Convert to the expected Workspace type with required description field
    return {
      ...workspace,
      description: workspace.description || '',
      members: workspace.members || 0
    };
  };

  // Show loading state while data is being fetched
  if (isLoading) {
    return (
      <div className="w-full h-full bg-background p-6 overflow-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium">Workspaces</h3>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-3 text-muted-foreground">Loading workspaces...</span>
        </div>
      </div>
    );
  }

  // Show empty state if no workspaces are available
  if (!isLoading && accessibleWorkspaces.length === 0 && !isCreatingWorkspace) {
    return (
      <div className="w-full h-full bg-background p-6 overflow-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium">Workspaces</h3>
          <Button onClick={handleCreateWorkspace}>
            <Plus className="mr-2 h-4 w-4" />
            New Workspace
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center py-12 space-y-4">
          <div className="text-center">
            <h4 className="text-lg font-medium">No workspaces found</h4>
            <p className="text-muted-foreground mt-2">
              Create your first workspace to get started with contract management
            </p>
          </div>
          <Button onClick={handleCreateWorkspace} size="lg">
            <Plus className="mr-2 h-4 w-4" />
            Create Your First Workspace
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium">Workspaces</h3>
        <Button onClick={handleCreateWorkspace}>
          <Plus className="mr-2 h-4 w-4" />
          New Workspace
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full max-w-md mb-6">
          <TabsTrigger value="workspaces">Workspaces</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="workspaces" className="mt-0">
          {!isCreatingWorkspace && !isEditingWorkspace && !isManagingUsers ? (
            <>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search workspaces..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <WorkspaceList
                workspaces={filteredWorkspaces}
                onCreateWorkspace={handleCreateWorkspace}
                onManageWorkspace={handleEditWorkspace}
                onDeleteWorkspace={handleDeleteWorkspace}
                onSelectWorkspace={handleSelectWorkspace}
                onManageUsersRoles={handleManageUsers}
              />
            </>
          ) : isCreatingWorkspace ? (
            <WorkspaceForm
              onClose={handleCloseForm}
              onWorkspaceCreated={handleWorkspaceCreated}
            />
          ) : isEditingWorkspace ? (
            <WorkspaceForm
              workspace={getSelectedWorkspace()}
              onClose={handleCloseForm}
              onWorkspaceUpdated={(id, data) => handleWorkspaceUpdated(id, data)}
            />
          ) : isManagingUsers ? (
            <UserAccessManager
              workspace={getSelectedWorkspace()}
              onClose={handleCloseForm}
              onUsersUpdated={handleUsersUpdated}
            />
          ) : null}
        </TabsContent>

        <TabsContent value="settings" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Workspace Settings</CardTitle>
              <CardDescription>
                Configure global settings for all workspaces
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="defaultWorkspace">Default Workspace</Label>
                <Select
                  defaultValue={currentWorkspace?.id || ""}
                  onValueChange={(value) => {
                    const workspace = getWorkspaceById(value);
                    if (workspace) {
                      setCurrentWorkspace(workspace);
                    }
                  }}
                >
                  <SelectTrigger id="defaultWorkspace">
                    <SelectValue placeholder="Select default workspace" />
                  </SelectTrigger>
                  <SelectContent>
                    {accessibleWorkspaces.length > 0 ? (
                      accessibleWorkspaces.map((workspace) => (
                        <SelectItem key={workspace.id} value={workspace.id}>
                          {workspace.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>
                        No accessible workspaces
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  This workspace will be selected by default when users log in
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                {userWorkspaces.length} workspaces available
              </div>
              <Button onClick={() => setActiveTab("workspaces")}>
                Back to Workspaces
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WorkspaceManager;
