/**
 * Workspace Transition Component
 * Provides smooth transitions when switching between workspaces
 */

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface WorkspaceTransitionProps {
  children: React.ReactNode;
  workspaceId: string | null;
  isLoading?: boolean;
  className?: string;
  loadingMessage?: string;
  showLoadingOverlay?: boolean;
}

export const WorkspaceTransition: React.FC<WorkspaceTransitionProps> = ({
  children,
  workspaceId,
  isLoading = false,
  className,
  loadingMessage = "Switching workspace...",
  showLoadingOverlay = true,
}) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState(workspaceId);
  const [contentKey, setContentKey] = useState(0);

  useEffect(() => {
    if (workspaceId !== currentWorkspaceId) {
      // Start transition
      setIsTransitioning(true);
      
      // Update workspace ID after a brief delay for smooth transition
      const timer = setTimeout(() => {
        setCurrentWorkspaceId(workspaceId);
        setContentKey(prev => prev + 1);
        
        // End transition
        setTimeout(() => {
          setIsTransitioning(false);
        }, 150);
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [workspaceId, currentWorkspaceId]);

  return (
    <div className={cn("relative", className)}>
      {/* Loading Overlay */}
      {(isLoading || isTransitioning) && showLoadingOverlay && (
        <div className={cn(
          "absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center transition-opacity duration-200",
          isLoading || isTransitioning ? "opacity-100" : "opacity-0 pointer-events-none"
        )}>
          <div className="flex items-center gap-3 px-4 py-2 bg-background border rounded-lg shadow-lg">
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
            <span className="text-sm font-medium text-foreground">
              {loadingMessage}
            </span>
          </div>
        </div>
      )}

      {/* Content with transition */}
      <div
        key={contentKey}
        className={cn(
          "transition-all duration-300 ease-in-out",
          isTransitioning ? "opacity-50 scale-[0.98]" : "opacity-100 scale-100"
        )}
      >
        {children}
      </div>
    </div>
  );
};

// Hook for workspace transition state
export const useWorkspaceTransition = (workspaceId: string | null) => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousWorkspaceId, setPreviousWorkspaceId] = useState(workspaceId);

  useEffect(() => {
    if (workspaceId !== previousWorkspaceId) {
      setIsTransitioning(true);
      setPreviousWorkspaceId(workspaceId);
      
      const timer = setTimeout(() => {
        setIsTransitioning(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [workspaceId, previousWorkspaceId]);

  return {
    isTransitioning,
    previousWorkspaceId,
  };
};

// Workspace content wrapper with fade transition
interface WorkspaceContentProps {
  children: React.ReactNode;
  workspaceId: string | null;
  className?: string;
  fadeOnChange?: boolean;
}

export const WorkspaceContent: React.FC<WorkspaceContentProps> = ({
  children,
  workspaceId,
  className,
  fadeOnChange = true,
}) => {
  const { isTransitioning } = useWorkspaceTransition(workspaceId);

  return (
    <div
      className={cn(
        "transition-opacity duration-200",
        fadeOnChange && isTransitioning ? "opacity-60" : "opacity-100",
        className
      )}
    >
      {children}
    </div>
  );
};

// Staggered animation for lists
interface WorkspaceListTransitionProps {
  children: React.ReactNode[];
  workspaceId: string | null;
  className?: string;
  staggerDelay?: number;
}

export const WorkspaceListTransition: React.FC<WorkspaceListTransitionProps> = ({
  children,
  workspaceId,
  className,
  staggerDelay = 50,
}) => {
  const { isTransitioning } = useWorkspaceTransition(workspaceId);
  const [animationKey, setAnimationKey] = useState(0);

  useEffect(() => {
    if (!isTransitioning) {
      setAnimationKey(prev => prev + 1);
    }
  }, [isTransitioning]);

  return (
    <div className={className}>
      {children.map((child, index) => (
        <div
          key={`${animationKey}-${index}`}
          className={cn(
            "transition-all duration-300 ease-out",
            isTransitioning 
              ? "opacity-0 translate-y-2" 
              : "opacity-100 translate-y-0"
          )}
          style={{
            transitionDelay: isTransitioning ? '0ms' : `${index * staggerDelay}ms`,
          }}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

// Skeleton loader for workspace content
interface WorkspaceSkeletonProps {
  className?: string;
  rows?: number;
  showHeader?: boolean;
}

export const WorkspaceSkeleton: React.FC<WorkspaceSkeletonProps> = ({
  className,
  rows = 3,
  showHeader = true,
}) => {
  return (
    <div className={cn("space-y-4 animate-pulse", className)}>
      {showHeader && (
        <div className="space-y-2">
          <div className="h-6 bg-muted rounded w-1/3"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
        </div>
      )}
      
      <div className="space-y-3">
        {Array.from({ length: rows }).map((_, index) => (
          <div key={index} className="space-y-2">
            <div className="h-4 bg-muted rounded w-full"></div>
            <div className="h-4 bg-muted rounded w-3/4"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WorkspaceTransition;
