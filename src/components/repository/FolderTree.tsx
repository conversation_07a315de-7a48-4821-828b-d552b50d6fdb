import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FolderPlus,
  MoreHorizontal,
  Edit,
  Trash2,
  FileText,
} from "lucide-react";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";

// Define the Folder interface
export interface Folder {
  id: string;
  name: string;
  parentId: string | null;
  workspaceId: string;
  createdBy: string;
  createdDate: string;
  documentCount?: number;
}

interface FolderTreeProps {
  onFolderSelect: (folderId: string | null) => void;
  selectedFolderId: string | null;
  folders?: Folder[];
  setFolders?: React.Dispatch<React.SetStateAction<Folder[]>>;
}

const FolderTree: React.FC<FolderTreeProps> = ({
  onFolderSelect,
  selectedFolderId,
  folders: propFolders,
  setFolders: propSetFolders,
}) => {
  const { currentWorkspace } = useClerkWorkspace();
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(["root"]));
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [parentFolderId, setParentFolderId] = useState<string | null>("root");
  const [folderToEdit, setFolderToEdit] = useState<Folder | null>(null);

  // Use folders from props if provided, otherwise use local state
  const [internalFolders, setInternalFolders] = useState<Folder[]>([
    {
      id: "folder-1",
      name: "Contracts",
      parentId: "root",
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "John Doe",
      createdDate: "2023-04-15",
      documentCount: 12,
    },
    {
      id: "folder-2",
      name: "NDAs",
      parentId: "folder-1",
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "John Doe",
      createdDate: "2023-04-16",
      documentCount: 5,
    },
    {
      id: "folder-3",
      name: "Service Agreements",
      parentId: "folder-1",
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "Jane Smith",
      createdDate: "2023-04-17",
      documentCount: 7,
    },
    {
      id: "folder-4",
      name: "Templates",
      parentId: "root",
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "John Doe",
      createdDate: "2023-04-18",
      documentCount: 8,
    },
    {
      id: "folder-5",
      name: "Employment",
      parentId: "folder-4",
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "Jane Smith",
      createdDate: "2023-04-19",
      documentCount: 3,
    },
    {
      id: "folder-6",
      name: "Archived",
      parentId: "root",
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "John Doe",
      createdDate: "2023-04-20",
      documentCount: 15,
    },
  ]);

  // Use folders from props if provided, otherwise use internal state
  const folders = propFolders || internalFolders;
  const setFolders = propSetFolders || setInternalFolders;

  // Toggle folder expansion
  const toggleFolder = (folderId: string) => {
    const newExpandedFolders = new Set(expandedFolders);
    if (newExpandedFolders.has(folderId)) {
      newExpandedFolders.delete(folderId);
    } else {
      newExpandedFolders.add(folderId);
    }
    setExpandedFolders(newExpandedFolders);
  };

  // Create a new folder
  const handleCreateFolder = () => {
    if (!newFolderName.trim()) return;

    const newFolder: Folder = {
      id: `folder-${Date.now()}`,
      name: newFolderName,
      parentId: parentFolderId,
      workspaceId: currentWorkspace?.id || "default",
      createdBy: "Current User",
      createdDate: new Date().toISOString().split("T")[0],
      documentCount: 0,
    };

    setFolders([...folders, newFolder]);
    setNewFolderName("");
    setIsCreateDialogOpen(false);

    // Expand the parent folder to show the new folder
    if (parentFolderId) {
      setExpandedFolders(new Set([...expandedFolders, parentFolderId]));
    }
  };

  // Rename a folder
  const handleRenameFolder = () => {
    if (!folderToEdit || !newFolderName.trim()) return;

    const updatedFolders = folders.map((folder) =>
      folder.id === folderToEdit.id
        ? { ...folder, name: newFolderName }
        : folder
    );

    setFolders(updatedFolders);
    setNewFolderName("");
    setFolderToEdit(null);
    setIsRenameDialogOpen(false);
  };

  // Delete a folder
  const handleDeleteFolder = () => {
    if (!folderToEdit) return;

    // Get all descendant folder IDs (for cascading delete)
    const getDescendantIds = (folderId: string): string[] => {
      const directChildren = folders.filter((f) => f.parentId === folderId);
      const descendantIds = directChildren.map((child) => child.id);

      directChildren.forEach((child) => {
        descendantIds.push(...getDescendantIds(child.id));
      });

      return descendantIds;
    };

    const idsToDelete = [folderToEdit.id, ...getDescendantIds(folderToEdit.id)];
    const updatedFolders = folders.filter((folder) => !idsToDelete.includes(folder.id));

    setFolders(updatedFolders);
    setFolderToEdit(null);
    setIsDeleteDialogOpen(false);

    // If the deleted folder was selected, select the root
    if (selectedFolderId && idsToDelete.includes(selectedFolderId)) {
      onFolderSelect(null);
    }
  };

  // Open create folder dialog
  const openCreateFolderDialog = (parentId: string | null) => {
    setParentFolderId(parentId);
    setNewFolderName("");
    setIsCreateDialogOpen(true);
  };

  // Open rename folder dialog
  const openRenameFolderDialog = (folder: Folder) => {
    setFolderToEdit(folder);
    setNewFolderName(folder.name);
    setIsRenameDialogOpen(true);
  };

  // Open delete folder dialog
  const openDeleteFolderDialog = (folder: Folder) => {
    setFolderToEdit(folder);
    setIsDeleteDialogOpen(true);
  };

  // Recursive function to render the folder tree
  const renderFolderTree = (parentId: string | null) => {
    const childFolders = folders.filter((folder) => folder.parentId === parentId);

    if (childFolders.length === 0) {
      return null;
    }

    return (
      <ul className={`pl-4 ${parentId === "root" ? "pl-0" : ""}`}>
        {childFolders.map((folder) => {
          const hasChildren = folders.some((f) => f.parentId === folder.id);
          const isExpanded = expandedFolders.has(folder.id);
          const isSelected = selectedFolderId === folder.id;

          return (
            <li key={folder.id} className="py-1">
              <div className="flex items-center group">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => toggleFolder(folder.id)}
                >
                  {hasChildren ? (
                    isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )
                  ) : (
                    <div className="w-4" />
                  )}
                </Button>

                <div
                  className={`flex items-center flex-1 px-2 py-1 rounded-md cursor-pointer ${
                    isSelected ? "bg-muted" : "hover:bg-muted/50"
                  }`}
                  onClick={() => onFolderSelect(folder.id)}
                >
                  <Folder
                    className={`h-4 w-4 mr-2 ${
                      isSelected ? "text-primary" : "text-muted-foreground"
                    }`}
                  />
                  <span className="text-sm">{folder.name}</span>
                  {folder.documentCount !== undefined && folder.documentCount > 0 && (
                    <span className="ml-2 text-xs text-muted-foreground">
                      ({folder.documentCount})
                    </span>
                  )}
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-100"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40">
                    <DropdownMenuItem
                      onClick={() => openCreateFolderDialog(folder.id)}
                    >
                      <FolderPlus className="h-4 w-4 mr-2" />
                      New Subfolder
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => openRenameFolderDialog(folder)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Rename
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => openDeleteFolderDialog(folder)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {isExpanded && renderFolderTree(folder.id)}
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Folders</h3>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2"
          onClick={() => openCreateFolderDialog("root")}
        >
          <FolderPlus className="h-4 w-4 mr-2" />
          New Folder
        </Button>
      </div>

      <div className="space-y-1">
        <div
          className={`flex items-center px-2 py-1 rounded-md cursor-pointer ${
            selectedFolderId === null ? "bg-muted" : "hover:bg-muted/50"
          }`}
          onClick={() => onFolderSelect(null)}
        >
          <FileText
            className={`h-4 w-4 mr-2 ${
              selectedFolderId === null ? "text-primary" : "text-muted-foreground"
            }`}
          />
          <span className="text-sm">All Documents</span>
        </div>

        {renderFolderTree("root")}
      </div>

      {/* Create Folder Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
            <DialogDescription>
              Enter a name for your new folder.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Folder name"
              className="w-full"
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rename Folder Dialog */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Rename Folder</DialogTitle>
            <DialogDescription>
              Enter a new name for this folder.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Folder name"
              className="w-full"
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRenameDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleRenameFolder}>Rename</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Folder Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Folder</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this folder? This action cannot be
              undone and all subfolders will also be deleted.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteFolder}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FolderTree;
