import React, { useState, useEffect } from 'react';
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useApi } from "@/lib/api";
import { Template, templateStore } from "@/types/template";
import { ContractService, TemplateService } from "@/services/api-services";
import type { Contract as ApiContract, Template as ApiTemplate } from "@/services/api-types";
import RepositoryGridView from './RepositoryGridView';
import RepositoryListView from './RepositoryListView';

interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "active" | "expired" | "terminated" | "pending_approval" | "rejected";
  createdBy: { name: string; id?: string; };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  tags: string[];
  starred: boolean;
  workspaceId?: string;
  folderId?: string;
}

interface UnifiedRepositoryViewProps {
  viewMode: 'grid' | 'list';
  searchQuery: string;
  selectedFolderId: string | null;
  dateRange: { from: Date | undefined; to: Date | undefined; };
  showStarredOnly: boolean;
  selectedUsers: string[];
  onDocumentSelect?: (documentId: string) => void;
  onPreviewDocument?: (documentId: string) => void;
  selectedDocuments?: string[];
  onSelectDocument?: (documentId: string, isChecked: boolean) => void;
  onSelectAll?: (isChecked: boolean) => void;
}

const UnifiedRepositoryView: React.FC<UnifiedRepositoryViewProps> = ({
  viewMode,
  searchQuery,
  selectedFolderId,
  dateRange,
  showStarredOnly,
  selectedUsers,
  onDocumentSelect,
  onPreviewDocument,
  selectedDocuments = [],
  onSelectDocument,
  onSelectAll,
}) => {
  const { currentWorkspace, canAccessContent } = useClerkWorkspace();
  const { fetch, fetchArray } = useApi();

  // Unified state for both views
  const [templates, setTemplates] = useState<Template[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Unified data fetching logic
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Load templates from local storage
        const localTemplates = templateStore.getTemplates();
        setTemplates(localTemplates);

        // Fetch data from API if workspace is available
        if (currentWorkspace && canAccessContent) {
          try {
            // Only fetch templates if we have a workspace
            if (!currentWorkspace) {
              setLoading(false);
              return;
            }

            // Fetch templates from API
            const apiTemplates = await fetchArray(
              () => TemplateService.getTemplates({ workspace_id: currentWorkspace.id }),
              "Loading templates...",
              "Failed to load templates"
            );

            if (apiTemplates) {
              const mappedTemplates: Template[] = apiTemplates.map((template: ApiTemplate) => ({
                id: template.id,
                title: template.title,
                description: template.description || '',
                type: template.type,
                complexity: template.complexity,
                lastUpdated: template.updated_at || new Date().toISOString().split('T')[0],
                usageCount: template.usage_count || 0,
                industry: template.industry,
                rating: template.rating,
                tags: template.tags || [],
                icon: template.icon,
                isUserCreated: template.is_user_created,
                createdBy: template.created_by,
                folderId: template.folder_id
              }));

              setTemplates([...localTemplates, ...mappedTemplates]);
            }
          } catch (templateError) {
            console.error("Error fetching templates:", templateError);
          }

          try {
            // Fetch contracts from API (only if we have a workspace)
            if (!currentWorkspace) {
              setLoading(false);
              return;
            }

            const apiContracts = await fetchArray(
              () => ContractService.getContracts({ workspace_id: currentWorkspace.id }),
              "Loading contracts...",
              "Failed to load contracts"
            );

            if (apiContracts) {
              const mappedContracts: Contract[] = apiContracts.map((contract: ApiContract) => ({
                id: contract.id,
                title: contract.title,
                type: contract.type || 'General',
                status: contract.status as Contract['status'],
                createdBy: {
                  name: contract.created_by?.name || 'Unknown',
                  id: contract.created_by?.id
                },
                createdDate: contract.created_at,
                expiryDate: contract.expiry_date,
                counterparty: contract.counterparty || 'Unknown',
                tags: contract.tags || [],
                starred: false,
                workspaceId: contract.workspace_id,
                folderId: contract.folder_id
              }));

              setContracts(mappedContracts);
            }
          } catch (contractError) {
            console.error("Error fetching contracts:", contractError);
          }
        }
      } catch (err) {
        console.error("Error fetching repository data:", err);
        setError("Failed to load repository data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentWorkspace, fetch, fetchArray, canAccessContent]);

  // Render appropriate view based on viewMode
  if (viewMode === 'grid') {
    return (
      <RepositoryGridView
        searchQuery={searchQuery}
        selectedFolderId={selectedFolderId}
        dateRange={dateRange}
        showStarredOnly={showStarredOnly}
        selectedUsers={selectedUsers}
      />
    );
  }

  return (
    <RepositoryListView
      searchQuery={searchQuery}
      selectedFolderId={selectedFolderId}
      dateRange={dateRange}
      showStarredOnly={showStarredOnly}
      selectedUsers={selectedUsers}
      onDocumentSelect={onDocumentSelect}
      onPreviewDocument={onPreviewDocument}
      selectedDocuments={selectedDocuments}
      onSelectDocument={onSelectDocument}
      onSelectAll={onSelectAll}
    />
  );
};

export default UnifiedRepositoryView;
