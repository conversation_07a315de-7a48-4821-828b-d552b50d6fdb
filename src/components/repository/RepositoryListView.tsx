import React, { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import EnhancedTable, { type ColumnDef } from "@/components/ui/enhanced-table";
import { useTableState } from "@/hooks/useTableState";

import { Download, Eye, FileText, MoreHorizontal, Star, Copy, ArrowRight, Loader2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Template, templateStore } from "@/types/template";
import { useApi } from "@/lib/api";
import { ContractService, TemplateService } from "@/services/api-services";
import type { Contract as ApiContract, Template as ApiTemplate } from "@/services/api-types";

interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "active" | "expired" | "terminated" | "pending_approval" | "rejected";
  createdBy: {
    name: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  tags: string[];
  starred: boolean;
  workspaceId?: string; // Add workspace ID to associate contracts with workspaces
  folderId?: string; // Add folder ID to associate contracts with folders
}

interface RepositoryListViewProps {
  searchQuery: string;
  selectedFolderId: string | null;
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  showStarredOnly: boolean;
  selectedUsers: string[];
  onDocumentSelect?: (documentId: string) => void;
  onPreviewDocument?: (documentId: string) => void;
  selectedDocuments?: string[];
  onSelectDocument?: (documentId: string, isChecked: boolean) => void;
  onSelectAll?: (isChecked: boolean) => void;
}

const RepositoryListView = ({
  searchQuery,
  selectedFolderId,
  dateRange,
  showStarredOnly,
  selectedUsers,
  onDocumentSelect,
  onPreviewDocument,
  selectedDocuments = [],
  onSelectDocument,
  onSelectAll,
}: RepositoryListViewProps) => {
  // Get workspace context
  const { currentWorkspace, canAccessContent } = useClerkWorkspace();
  const { fetch, fetchArray } = useApi();

  // State for data
  const [templates, setTemplates] = useState<Template[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load templates from local storage and API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Load templates from local storage first
        templateStore.loadFromLocalStorage();
        setTemplates(templateStore.getTemplates());

        // Then fetch templates from API
        if (currentWorkspace) {
          try {
            // Fetch templates from API using fetchArray to ensure we get an array
            const templatesResult = await fetchArray(
              () => TemplateService.getTemplates({ workspace_id: currentWorkspace.id }),
              "Loading templates...",
              "Failed to load templates"
            );

          // Debug the templates result
          console.log("Templates result (list view):", templatesResult);
          console.log("Templates result type (list view):", typeof templatesResult);
          console.log("Is array (list view):", Array.isArray(templatesResult));

          if (templatesResult && templatesResult.length > 0) {
            // Map API templates to UI templates
            const apiTemplates = templatesResult.map((template: ApiTemplate) => ({
              id: template.id,
              title: template.title,
              description: template.description,
              type: template.type,
              complexity: template.complexity,
              industry: template.industry || '',
              tags: template.tags || [],
              icon: template.icon || '',
              lastUpdated: template.updated_at || template.created_at,
              usageCount: template.usage_count,
              isUserCreated: template.is_user_created,
              folderId: template.folder_id || 'folder-4', // Default to templates folder
            }));

            // Merge with local templates
            const localTemplates = templateStore.getTemplates();
            const mergedTemplates = [...localTemplates];

            // Add API templates that don't exist locally
            apiTemplates.forEach(apiTemplate => {
              if (!localTemplates.some(localTemplate => localTemplate.id === apiTemplate.id)) {
                mergedTemplates.push(apiTemplate);
              }
            });

            setTemplates(mergedTemplates);

            // Update local storage with merged templates
            templateStore.setTemplates(mergedTemplates);
            templateStore.saveToLocalStorage();
            }
          } catch (templateError) {
            console.error("Error fetching templates:", templateError);
            // Continue with contracts even if templates fail
          }
        }

        if (currentWorkspace) {
          try {
            // Fetch contracts from API using fetchArray to ensure we get an array
            const contractsResult = await fetchArray(
              () => ContractService.getContracts({ workspace_id: currentWorkspace.id }),
              "Loading contracts...",
              "Failed to load contracts"
            );

          // Debug the contracts result
          console.log("Contracts result (list view):", contractsResult);
          console.log("Contracts result type (list view):", typeof contractsResult);
          console.log("Is array (contracts list view):", Array.isArray(contractsResult));

          if (contractsResult && contractsResult.length > 0) {
            // Map API contracts to UI contracts
            const mappedContracts: Contract[] = contractsResult.map((contract: ApiContract) => ({
              id: contract.id,
              title: contract.title,
              type: contract.type,
              status: contract.status,
              createdBy: {
                name: contract.created_by?.name || 'Unknown',
                id: contract.created_by?.id || 'unknown-id'
              },
              createdDate: contract.created_at,
              expiryDate: contract.expiry_date,
              counterparty: contract.counterparty || 'N/A',
              tags: contract.tags || [],
              starred: contract.starred || false,
              workspaceId: contract.workspace_id,
              folderId: contract.folder_id || 'folder-1' // Default to Contracts folder
            }));

            setContracts(mappedContracts);
            }
          } catch (contractError) {
            console.error("Error fetching contracts:", contractError);
            // Continue with local data even if contracts API fails
          }
        }
      } catch (err) {
        console.error("Error fetching repository data:", err);
        setError("Failed to load repository data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentWorkspace, fetch]);

  // Handle template selection
  const handleUseTemplate = (templateId: string) => {
    // Increment usage count
    templateStore.incrementUsageCount(templateId);
    // Navigate to contract wizard with template
    window.location.href = `/app/contracts/wizard?template=${templateId}`;
  };

  // Filter contracts based on filters
  const filteredContracts = contracts.filter((contract) => {
    // If templates folder is selected, don't show contracts
    if (selectedFolderId === "folder-4") {
      return false;
    }

    // Filter by workspace - only show contracts from the current workspace
    // AND ensure user has access to the workspace the contract belongs to
    const matchesWorkspace = currentWorkspace ?
      contract.workspaceId === currentWorkspace.id && canAccessContent(contract.workspaceId) :
      false;

    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      contract.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.counterparty.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.type.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by folder
    const matchesFolder =
      selectedFolderId === null ||
      contract.folderId === selectedFolderId;

    // Filter by date range
    const contractDate = new Date(contract.createdDate);
    const matchesDateRange =
      !dateRange.from ||
      ((!dateRange.from || contractDate >= dateRange.from) &&
       (!dateRange.to || contractDate <= dateRange.to));

    // Filter by starred status
    const matchesStarred =
      !showStarredOnly ||
      contract.starred;

    // Filter by creator
    const matchesCreator =
      selectedUsers.length === 0 ||
      (contract.createdBy.id && selectedUsers.includes(contract.createdBy.id));

    return matchesWorkspace && matchesSearch && matchesFolder && matchesDateRange && matchesStarred && matchesCreator;
  });

  // Filter templates based on filters
  const filteredTemplates = templates.filter((template) => {
    // Only show templates when templates folder is selected or no folder is selected
    const matchesFolder =
      selectedFolderId === "folder-4" ||
      (selectedFolderId === null && template.folderId === "folder-4");

    // Filter by search query
    const matchesSearch =
      searchQuery === "" ||
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.tags && template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));

    return matchesFolder && matchesSearch;
  });

  // Combine templates and contracts into a single data structure for the table
  type DocumentItem = {
    id: string;
    title: string;
    type: string;
    createdBy: { name: string; id?: string };
    createdDate: string;
    status?: Contract["status"];
    starred?: boolean;
    itemType: "template" | "contract";
    counterparty?: string;
    expiryDate?: string;
    tags?: string[];
  };

  const combinedData: DocumentItem[] = [
    ...filteredTemplates.map((template): DocumentItem => ({
      id: template.id,
      title: template.title,
      type: template.type,
      createdBy: template.createdBy || { name: "Unknown", id: undefined },
      createdDate: template.lastUpdated || new Date().toISOString().split('T')[0],
      starred: template.rating ? template.rating > 4.5 : false,
      itemType: "template",
      tags: template.tags,
    })),
    ...filteredContracts.map((contract): DocumentItem => ({
      id: contract.id,
      title: contract.title,
      type: contract.type,
      createdBy: contract.createdBy,
      createdDate: contract.createdDate,
      status: contract.status,
      starred: contract.starred,
      itemType: "contract",
      counterparty: contract.counterparty,
      expiryDate: contract.expiryDate,
      tags: contract.tags,
    })),
  ];

  // Enhanced table state management
  const tableState = useTableState({
    data: combinedData,
    getRowId: (item: DocumentItem) => item.id,
    defaultSort: { key: "createdDate", direction: "desc" },
  });

  // Selection handlers
  const handleRowSelect = (documentId: string, selected: boolean) => {
    tableState.handleRowSelect(documentId, selected);
    if (onSelectDocument) {
      onSelectDocument(documentId, selected);
    }
  };

  const handleSelectAll = (selected: boolean) => {
    tableState.handleSelectAll(selected);
    if (onSelectAll) {
      onSelectAll(selected);
    }
  };

  const getStatusBadge = (status: Contract["status"]) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "pending_approval":
        return <Badge variant="secondary">In Review</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "expired":
        return <Badge variant="destructive">Expired</Badge>;
      case "terminated":
        return <Badge variant="destructive">Terminated</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to simplify contract titles
  const simplifyTitle = (title: string) => {
    // Remove "with Company Name" and other common phrases
    let simplified = title
      .replace(/\s+with\s+[^,]+/i, '')
      .replace(/\s+-\s+[^,]+/i, '')
      .trim();

    // If still too long, truncate
    if (simplified.length > 20) {
      simplified = simplified.substring(0, 20).trim();
    }

    return simplified;
  };

  // Column definitions for enhanced table
  const columns: ColumnDef<DocumentItem>[] = [
    {
      key: "title",
      label: "Document",
      sortable: true,
      render: (_: any, item: DocumentItem) => (
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs">
            {item.itemType === "template" ? "T" : "C"}
          </div>
          <div>
            <div
              className="font-medium cursor-pointer hover:underline transition-colors"
              onClick={() => onDocumentSelect?.(item.id)}
            >
              {item.title}
            </div>
            <div className="text-xs text-muted-foreground">
              {item.itemType === "template" ? "Template" : "Contract"}
              {item.counterparty && ` • ${item.counterparty}`}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "type",
      label: "Type",
      sortable: true,
    },
    {
      key: "createdBy",
      label: "Created By",
      sortable: true,
      render: (value: any) => value.name,
    },
    {
      key: "createdDate",
      label: "Created",
      sortable: true,
      render: (value: any) => formatDate(value),
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (_: any, item: DocumentItem) => {
        if (item.itemType === "template") {
          return <Badge variant="outline">Template</Badge>;
        }
        return item.status ? getStatusBadge(item.status) : null;
      },
    },
    {
      key: "actions",
      label: "Actions",
      sortable: false,
      width: "120px",
      render: (_: any, item: DocumentItem) => (
        <div className="flex items-center gap-2 table-row-actions">
          {onPreviewDocument && (
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                onPreviewDocument(item.id);
              }}
              title="Quick Preview"
              className="h-8 w-8 hover:bg-muted"
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onDocumentSelect?.(item.id)}>
                <ArrowRight className="mr-2 h-4 w-4" /> Open
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" /> Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" /> Download
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Star className="mr-2 h-4 w-4" />
                {item.starred ? "Unstar" : "Star"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  // Get template complexity badge
  const getComplexityBadge = (complexity: Template["complexity"]) => {
    switch (complexity) {
      case "simple":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Simple</Badge>;
      case "medium":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Medium</Badge>;
      case "complex":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Complex</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading repository data...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch your documents
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading repository</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : combinedData.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No documents found</h3>
          <p className="text-muted-foreground mt-2">
            {searchQuery || selectedFolderId || dateRange.from || showStarredOnly || selectedUsers.length > 0
              ? "Try adjusting your filters"
              : "Add documents to your repository to get started"}
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <EnhancedTable
            data={tableState.sortedData}
            columns={columns}
            sortConfig={tableState.sortConfig}
            onSort={tableState.handleSort}
            selectedRows={onSelectDocument ? tableState.selectedRows : []}
            onRowSelect={onSelectDocument ? handleRowSelect : undefined}
            onSelectAll={onSelectAll ? handleSelectAll : undefined}
            onRowClick={(item: DocumentItem) => onDocumentSelect?.(item.id)}
            hoverable={true}
            stickyHeader={false}
            keyboardNavigation={true}
            loading={loading}
            emptyMessage="No documents found"
            className="rounded-md"
          />
        </div>
      )}
    </div>
  );
};

export default RepositoryListView;
