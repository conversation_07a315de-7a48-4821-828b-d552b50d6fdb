import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Calendar,
  FileText,
  FolderOpen,
  Grid3X3,
  List,
  Search,
  X,
  Filter,
  ArrowUpDown,
  Star,
  Upload,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";
import RepositoryGridView from "./RepositoryGridView";
import RepositoryListView from "./RepositoryListView";
import FolderTree, { Folder } from "./FolderTree";
import DocumentUploadDialog from "./DocumentUploadDialog";

interface ContractRepositoryProps {}

const ContractRepository = ({}: ContractRepositoryProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string>("newest");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState<boolean>(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [showStarredOnly, setShowStarredOnly] = useState<boolean>(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState<boolean>(false);
  const [documents, setDocuments] = useState<any[]>([]);

  // Folders state
  const [folders, setFolders] = useState<Folder[]>([
    {
      id: "folder-1",
      name: "Contracts",
      parentId: "root",
      workspaceId: "1",
      createdBy: "John Doe",
      createdDate: "2023-04-15",
      documentCount: 12,
    },
    {
      id: "folder-2",
      name: "NDAs",
      parentId: "folder-1",
      workspaceId: "1",
      createdBy: "John Doe",
      createdDate: "2023-04-16",
      documentCount: 5,
    },
    {
      id: "folder-3",
      name: "Service Agreements",
      parentId: "folder-1",
      workspaceId: "1",
      createdBy: "Jane Smith",
      createdDate: "2023-04-17",
      documentCount: 7,
    },
    {
      id: "folder-4",
      name: "Templates",
      parentId: "root",
      workspaceId: "1",
      createdBy: "John Doe",
      createdDate: "2023-04-18",
      documentCount: 8,
    },
    {
      id: "folder-5",
      name: "Employment",
      parentId: "folder-4",
      workspaceId: "1",
      createdBy: "Jane Smith",
      createdDate: "2023-04-19",
      documentCount: 3,
    },
    {
      id: "folder-6",
      name: "Archived",
      parentId: "root",
      workspaceId: "1",
      createdBy: "John Doe",
      createdDate: "2023-04-20",
      documentCount: 15,
    },
  ]);

  // Mock data for filters
  const tags = [
    { id: "t1", name: "Important" },
    { id: "t2", name: "Reviewed" },
    { id: "t3", name: "Needs Attention" },
    { id: "t4", name: "Approved" },
    { id: "t5", name: "Rejected" },
  ];

  const contractTypes = [
    { id: "ct1", name: "Service Agreement" },
    { id: "ct2", name: "NDA" },
    { id: "ct3", name: "Employment" },
    { id: "ct4", name: "Lease" },
    { id: "ct5", name: "License" },
  ];

  const statuses = [
    { id: "s1", name: "Draft" },
    { id: "s2", name: "In Review" },
    { id: "s3", name: "Active" },
    { id: "s4", name: "Expired" },
    { id: "s5", name: "Terminated" },
  ];

  const users = [
    { id: "u1", name: "Jane Smith", initials: "JS" },
    { id: "u2", name: "John Doe", initials: "JD" },
    { id: "u3", name: "Alice Johnson", initials: "AJ" },
    { id: "u4", name: "Bob Williams", initials: "BW" },
    { id: "u5", name: "Carol Brown", initials: "CB" },
  ];

  const sortOptions = [
    { id: "newest", name: "Newest First" },
    { id: "oldest", name: "Oldest First" },
    { id: "a-z", name: "A-Z" },
    { id: "z-a", name: "Z-A" },
    { id: "expiry", name: "Expiry Date" },
  ];

  // Toggle tag selection
  const toggleTag = (tagId: string) => {
    setSelectedTags((prev) =>
      prev.includes(tagId)
        ? prev.filter((id) => id !== tagId)
        : [...prev, tagId],
    );
  };

  // Toggle contract type selection
  const toggleType = (typeId: string) => {
    setSelectedTypes((prev) =>
      prev.includes(typeId)
        ? prev.filter((id) => id !== typeId)
        : [...prev, typeId],
    );
  };

  // Toggle status selection
  const toggleStatus = (statusId: string) => {
    setSelectedStatus((prev) =>
      prev.includes(statusId)
        ? prev.filter((id) => id !== statusId)
        : [...prev, statusId],
    );
  };

  // Toggle user selection
  const toggleUser = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId],
    );
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedTags([]);
    setSelectedTypes([]);
    setSelectedStatus([]);
    setSelectedUsers([]);
    setDateRange({ from: undefined, to: undefined });
    setShowStarredOnly(false);
    setSearchQuery("");
  };

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return "";
    return format(date, "PPP");
  };

  // Handle document upload
  const handleDocumentUploaded = (document: any) => {
    // Add the document to the documents array
    setDocuments([document, ...documents]);
  };

  return (
    <div className="w-full h-full bg-background p-6 overflow-auto">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h3 className="text-lg font-medium">Document Repository</h3>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="h-9 w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map(option => (
                <SelectItem key={option.id} value={option.id}>
                  <div className="flex items-center">
                    <ArrowUpDown className="mr-2 h-4 w-4" />
                    {option.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex border rounded-md">
            <Button
              variant={viewMode === "grid" ? "secondary" : "ghost"}
              size="icon"
              className="rounded-r-none h-9 w-9"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "secondary" : "ghost"}
              size="icon"
              className="rounded-l-none h-9 w-9"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Folder sidebar */}
        <Card className="md:col-span-1 h-fit">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Repository</CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 flex items-center gap-1"
                onClick={() => setIsUploadDialogOpen(true)}
              >
                <Upload className="h-3.5 w-3.5" />
                <span>Upload</span>
              </Button>
            </div>
            <CardDescription>Organize your documents in folders</CardDescription>
          </CardHeader>
          <CardContent>
            <FolderTree
              onFolderSelect={setSelectedFolderId}
              selectedFolderId={selectedFolderId}
              folders={folders}
              setFolders={setFolders}
            />
          </CardContent>
        </Card>

        {/* Main content */}
        <div className="md:col-span-3 space-y-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search contracts..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2 h-9">
                    <Calendar className="h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <span>
                          {formatDate(dateRange.from)} - {formatDate(dateRange.to)}
                        </span>
                      ) : (
                        formatDate(dateRange.from)
                      )
                    ) : (
                      <span>Date Range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <CalendarComponent
                    initialFocus
                    mode="range"
                    selected={dateRange}
                    onSelect={(range) => {
                      if (range) {
                        setDateRange({
                          from: range.from,
                          to: range.to || range.from
                        });
                      } else {
                        setDateRange({ from: undefined, to: undefined });
                      }
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>

              <Button
                variant={showAdvancedFilters ? "default" : "outline"}
                className="flex items-center gap-2 h-9"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                <Filter className="h-4 w-4" />
                <span>Advanced</span>
              </Button>

              <Button
                variant="outline"
                className="flex items-center gap-2 h-9"
                onClick={clearAllFilters}
              >
                <X className="h-4 w-4" />
                <span>Clear</span>
              </Button>
            </div>
          </div>

          {showAdvancedFilters && (
            <Card className="mt-4">
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Created By</h4>
                    <div className="space-y-2">
                      {users.map((user) => (
                        <div key={user.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`user-${user.id}`}
                            checked={selectedUsers.includes(user.id)}
                            onCheckedChange={() => toggleUser(user.id)}
                          />
                          <label
                            htmlFor={`user-${user.id}`}
                            className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {user.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">Additional Filters</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="starred-only"
                          checked={showStarredOnly}
                          onCheckedChange={() => setShowStarredOnly(!showStarredOnly)}
                        />
                        <label
                          htmlFor="starred-only"
                          className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                        >
                          <Star className="h-4 w-4 mr-1 text-yellow-400" />
                          Starred Only
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Current folder indicator */}
          {selectedFolderId && (
            <div className="flex flex-wrap gap-2">
              <Badge
                variant="outline"
                className="flex items-center gap-1"
              >
                <FolderOpen className="h-3.5 w-3.5 mr-1" />
                {/* Get folder name from the selected folder ID */}
                {(() => {
                  // This is a mock implementation - in a real app, you would get this from your folder data
                  const folderNames = {
                    "folder-1": "Contracts",
                    "folder-2": "NDAs",
                    "folder-3": "Service Agreements",
                    "folder-4": "Templates",
                    "folder-5": "Employment",
                    "folder-6": "Archived",
                  };
                  return folderNames[selectedFolderId as keyof typeof folderNames] || "Selected Folder";
                })()}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1"
                  onClick={() => setSelectedFolderId(null)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            </div>
          )}

          {/* Contract list/grid */}
          {viewMode === "grid" ? (
            <RepositoryGridView
              searchQuery={searchQuery}
              selectedFolderId={selectedFolderId}
              dateRange={dateRange}
              showStarredOnly={showStarredOnly}
              selectedUsers={selectedUsers}
            />
          ) : (
            <RepositoryListView
              searchQuery={searchQuery}
              selectedFolderId={selectedFolderId}
              dateRange={dateRange}
              showStarredOnly={showStarredOnly}
              selectedUsers={selectedUsers}
            />
          )}
        </div>
      </div>

      {/* Document Upload Dialog */}
      <DocumentUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        selectedFolderId={selectedFolderId}
        folders={folders}
        onDocumentUploaded={handleDocumentUploaded}
      />
    </div>
  );
};

export default ContractRepository;
