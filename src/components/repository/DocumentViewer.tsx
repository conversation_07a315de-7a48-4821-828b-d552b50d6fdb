import React, { useEffect, useState, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Download,
  Eye,
  Edit,
  Save,
  Loader2,
  AlertCircle,
  FileText,
  ExternalLink
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DocumentService } from '@/services/api-services';
import { Document } from '@/services/api-types';

interface DocumentViewerProps {
  documentId: string;
  readOnly?: boolean;
  onSave?: (content: string) => void;
  onError?: (error: string) => void;
  onContentChange?: () => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  documentId,
  readOnly = true,
  onSave,
  onError,
  onContentChange
}) => {
  const [document, setDocument] = useState<Document | null>(null);
  const [fileUrl, setFileUrl] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const editorRef = useRef<HTMLDivElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  // Fetch document and file URL
  useEffect(() => {
    const fetchDocument = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch document details
        const documentResponse = await DocumentService.getDocument(documentId);
        setDocument(documentResponse.data);

        // If document has content, use it
        if (documentResponse.data.content) {
          setContent(documentResponse.data.content);
        }

        // If document has a file, get a signed URL
        if (documentResponse.data.file_path) {
          const fileUrlResponse = await DocumentService.getDocumentFileUrl(documentId);
          setFileUrl(fileUrlResponse.data.url);
        }
      } catch (err: any) {
        console.error('Error fetching document:', err);
        const errorMessage = err.message || 'Failed to load document';
        setError(errorMessage);
        if (onError) {
          onError(errorMessage);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocument();
  }, [documentId, onError]);

  // Toggle editing mode
  const toggleEditMode = () => {
    if (isEditing && editorRef.current) {
      // If we're currently editing, save the content before switching to preview
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      if (onSave) {
        onSave(newContent);
      }
    }
    setIsEditing(!isEditing);
  };

  // Handle save
  const handleSave = async () => {
    if (!editorRef.current || !document) return;

    setIsSaving(true);
    const newContent = editorRef.current.innerHTML;

    try {
      // Update document with new content
      await DocumentService.updateDocument(documentId, {
        content: newContent
      });

      // Update local state
      setContent(newContent);

      // Call onSave callback if provided
      if (onSave) {
        onSave(newContent);
      }
    } catch (err: any) {
      console.error('Error saving document:', err);
      const errorMessage = err.message || 'Failed to save document';
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Handle command (for rich text editing)
  const handleCommand = (command: string, value: string = '') => {
    // Use modern approach instead of deprecated execCommand
    try {
      if (editorRef.current) {
        editorRef.current.focus();
        // For basic commands, we can use the Selection API
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          // This is a simplified implementation - in a real app you'd want to use a proper rich text editor
          console.warn('Rich text editing commands are deprecated. Consider using a modern editor like TipTap or Quill.');
        }
      }
    } catch (error) {
      console.error('Error executing editor command:', error);
    }
  };

  // Handle PDF export
  const handleExportPDF = () => {
    if (fileUrl) {
      // Open the file URL in a new tab
      window.open(fileUrl, '_blank');
    } else if (content && previewRef.current) {
      // For content without a file, we could implement PDF generation here
      // For now, just alert the user
      alert('PDF export from content is not implemented yet');
    }
  };

  // Handle external view
  const handleExternalView = () => {
    if (fileUrl) {
      window.open(fileUrl, '_blank');
    }
  };

  // Render file viewer or content
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      );
    }

    if (error) {
      return (
        <Alert variant="destructive" className="m-4">
          <AlertCircle className="h-4 w-4 mr-2" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      );
    }

    if (!document) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
          <FileText className="h-12 w-12 mb-4" />
          <p>Document not found</p>
        </div>
      );
    }

    // If we have a file URL and we're not editing, show iframe or embed
    if (fileUrl && !isEditing) {
      const fileExtension = document.filename.split('.').pop()?.toLowerCase();

      if (fileExtension === 'pdf') {
        return (
          <iframe
            src={`${fileUrl}#toolbar=0`}
            className="w-full h-full border-0"
            title={document.title}
          />
        );
      } else if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension || '')) {
        return (
          <div className="flex items-center justify-center h-full">
            <img
              src={fileUrl}
              alt={document.title}
              className="max-w-full max-h-full object-contain"
            />
          </div>
        );
      } else {
        // For other file types, show a download link
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <FileText className="h-16 w-16 mb-4 text-primary" />
            <h3 className="text-lg font-medium mb-2">{document.title}</h3>
            <p className="text-sm text-muted-foreground mb-4">
              This file type cannot be previewed directly.
            </p>
            <Button onClick={handleExternalView}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Open File
            </Button>
          </div>
        );
      }
    }

    // Otherwise, show the content editor/viewer
    return isEditing ? (
      <div
        ref={editorRef}
        className="min-h-full p-8 focus:outline-none"
        contentEditable
        dangerouslySetInnerHTML={{ __html: content }}
        onInput={onContentChange}
        onBlur={onContentChange}
        style={{
          fontFamily: '"Times New Roman", Times, serif',
          fontSize: '12pt',
          lineHeight: '1.5',
          color: '#333'
        }}
      />
    ) : (
      <div
        ref={previewRef}
        className="min-h-full p-8"
        style={{
          fontFamily: '"Times New Roman", Times, serif',
          fontSize: '12pt',
          lineHeight: '1.5',
          color: '#333'
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  };

  return (
    <div className="w-full h-full flex flex-col">
      <Card className="flex-1 flex flex-col overflow-hidden border-slate-200">
        <div className="border-b px-6 py-2 bg-muted/30 flex items-center justify-between">
          <div className="text-sm font-medium">
            {document?.title || 'Document Viewer'}
          </div>
          <div className="flex items-center gap-2">
            {!readOnly && (
              <Button
                variant="outline"
                size="sm"
                onClick={toggleEditMode}
                className="h-8"
                disabled={isLoading}
              >
                {isEditing ? (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </>
                ) : (
                  <>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </>
                )}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportPDF}
              className="h-8"
              disabled={isLoading || (!fileUrl && !content)}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>

        {/* Editing toolbar */}
        {!readOnly && isEditing && (
          <div className="border-b px-4 py-2 flex items-center overflow-x-auto">
            <TooltipProvider>
              {/* Rich text editing buttons would go here */}
              <div className="flex-1"></div>

              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="h-8"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
            </TooltipProvider>
          </div>
        )}

        <CardContent className="flex-1 p-0 overflow-auto">
          {renderContent()}
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentViewer;
