import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Upload, FileText, AlertCircle, Check, Loader2, FolderOpen } from 'lucide-react';
import { documentParser } from '@/services/documentParser';
import { contractExtractor } from '@/services/contractExtractor';
import { DocumentService } from '@/services/api-services';
import { useApi } from '@/lib/api';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import { Folder } from './FolderTree';

interface DocumentUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedFolderId: string | null;
  folders: Folder[];
  onDocumentUploaded: (document: {
    id: string;
    title: string;
    type: string;
    content: string;
    folderId: string | null;
    createdDate: string;
  }) => void;
}

const DocumentUploadDialog: React.FC<DocumentUploadDialogProps> = ({
  open,
  onOpenChange,
  selectedFolderId,
  folders,
  onDocumentUploaded,
}) => {
  const [activeTab, setActiveTab] = useState<string>('upload');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [extractedText, setExtractedText] = useState<string>('');
  const [extractedData, setExtractedData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [pasteText, setPasteText] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);
  const [documentTitle, setDocumentTitle] = useState<string>('');
  const [documentType, setDocumentType] = useState<string>('');
  const [targetFolderId, setTargetFolderId] = useState<string | null>(selectedFolderId);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file upload
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    setIsProcessing(true);
    setError(null);

    try {
      // Parse document to extract text
      const parsedDocument = await documentParser.parseDocument(file);
      setExtractedText(parsedDocument.text);

      // Extract contract data
      const extractedContract = await contractExtractor.extractFromFile(file);
      setExtractedData(extractedContract);

      // Set document title from file name or extracted title
      setDocumentTitle(extractedContract.metadata?.title || file.name.replace(/\.[^/.]+$/, ""));

      // Set document type if available
      setDocumentType(extractedContract.metadata?.type || "Document");

      // Show success message
      setSuccess(true);
    } catch (err) {
      console.error('Error extracting text:', err);
      setError('Failed to extract text from the document. Please try a different file format.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle text paste
  const handleTextPaste = async () => {
    if (!pasteText.trim()) return;

    setIsProcessing(true);
    setError(null);

    try {
      // Extract text directly
      setExtractedText(pasteText);

      // Extract contract data from text
      const extractedContract = contractExtractor.extractFromText(pasteText);
      setExtractedData(extractedContract);

      // Set document title from extracted title or default
      setDocumentTitle(extractedContract.metadata?.title || "Pasted Document");

      // Set document type if available
      setDocumentType(extractedContract.metadata?.type || "Document");

      // Show success message
      setSuccess(true);
    } catch (err) {
      console.error('Error processing text:', err);
      setError('Failed to process the text. Please check the format and try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleFileUpload(e.dataTransfer.files);
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(e.target.files);
  };

  // Get current workspace
  const { currentWorkspace } = useClerkWorkspace();

  // Save document
  const handleSaveDocument = async () => {
    if (!documentTitle.trim()) {
      setError('Please enter a document title');
      return;
    }

    if (!currentWorkspace?.id) {
      setError('No workspace selected. Please select a workspace first.');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // If we have a file, upload it
      if (fileInputRef.current?.files?.length) {
        const file = fileInputRef.current.files[0];
        const folder = targetFolderId || '';

        // Upload document with file
        const response = await DocumentService.uploadDocument(
          file,
          documentTitle,
          currentWorkspace.id,
          folder,
          extractedText
        );

        // Call the callback to add the document
        onDocumentUploaded({
          id: response.data.id,
          title: response.data.title,
          type: documentType || 'Document',
          content: response.data.content || extractedText,
          folderId: targetFolderId,
          createdDate: new Date(response.data.created_at).toISOString().split('T')[0],
        });
      } else {
        // Create document without file (text only)
        const response = await DocumentService.createDocument({
          title: documentTitle,
          filename: `${documentTitle.replace(/\s+/g, '_')}.txt`,
          workspace_id: currentWorkspace.id,
          content: extractedText,
          folder: targetFolderId || undefined
        });

        // Call the callback to add the document
        onDocumentUploaded({
          id: response.data.id,
          title: response.data.title,
          type: documentType || 'Document',
          content: response.data.content || extractedText,
          folderId: targetFolderId,
          createdDate: new Date(response.data.created_at).toISOString().split('T')[0],
        });
      }

      // Reset form and close dialog
      resetForm();
      onOpenChange(false);
    } catch (err: any) {
      console.error('Error saving document:', err);
      setError(err.message || 'Failed to save document. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setExtractedText('');
    setExtractedData(null);
    setDocumentTitle('');
    setDocumentType('');
    setPasteText('');
    setError(null);
    setSuccess(false);
    setIsProcessing(false);
    setActiveTab('upload');
  };

  // Handle dialog close
  const handleDialogClose = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
          <DialogDescription>
            Upload a document or paste text to add to your repository
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="paste">Paste Text</TabsTrigger>
          </TabsList>

          {/* Upload File Tab */}
          <TabsContent value="upload">
            <div className="space-y-4">
              {!extractedText ? (
                <div
                  className={`border-2 ${isDragging ? 'border-primary' : 'border-dashed'} rounded-lg p-8 text-center cursor-pointer transition-colors`}
                  onDragEnter={handleDragEnter}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileInputChange}
                    className="hidden"
                    accept=".pdf,.doc,.docx,.txt,.html"
                  />
                  <Upload className={`h-10 w-10 mx-auto mb-3 ${isDragging ? 'text-primary' : 'text-muted-foreground'}`} />
                  <h3 className="text-lg font-medium mb-1">
                    {isDragging ? 'Drop file here' : 'Drag & drop file here'}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supported formats: PDF, DOCX, TXT, HTML
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Document Title</Label>
                      <Input
                        id="title"
                        value={documentTitle}
                        onChange={(e) => setDocumentTitle(e.target.value)}
                        placeholder="Enter document title"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="type">Document Type</Label>
                      <Input
                        id="type"
                        value={documentType}
                        onChange={(e) => setDocumentType(e.target.value)}
                        placeholder="E.g., Contract, Agreement, NDA"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="folder">Folder</Label>
                      <select
                        id="folder"
                        value={targetFolderId || ""}
                        onChange={(e) => setTargetFolderId(e.target.value === "" ? null : e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Root (No Folder)</option>
                        {folders.map((folder) => (
                          <option key={folder.id} value={folder.id}>
                            {folder.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Extracted Content Preview</Label>
                    <div className="bg-muted/30 rounded-md p-4 max-h-[200px] overflow-y-auto whitespace-pre-wrap text-sm">
                      {extractedText.substring(0, 500)}
                      {extractedText.length > 500 && '...'}
                    </div>
                  </div>
                </div>
              )}

              {isProcessing && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                  <span>Processing document...</span>
                </div>
              )}

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          {/* Paste Text Tab */}
          <TabsContent value="paste">
            <div className="space-y-4">
              {!extractedText ? (
                <>
                  <Textarea
                    placeholder="Paste document text here..."
                    value={pasteText}
                    onChange={(e) => setPasteText(e.target.value)}
                    className="min-h-[200px]"
                  />
                  <Button
                    onClick={handleTextPaste}
                    disabled={!pasteText.trim() || isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      'Extract Text'
                    )}
                  </Button>
                </>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paste-title">Document Title</Label>
                      <Input
                        id="paste-title"
                        value={documentTitle}
                        onChange={(e) => setDocumentTitle(e.target.value)}
                        placeholder="Enter document title"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paste-type">Document Type</Label>
                      <Input
                        id="paste-type"
                        value={documentType}
                        onChange={(e) => setDocumentType(e.target.value)}
                        placeholder="E.g., Contract, Agreement, NDA"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paste-folder">Folder</Label>
                      <select
                        id="paste-folder"
                        value={targetFolderId || ""}
                        onChange={(e) => setTargetFolderId(e.target.value === "" ? null : e.target.value)}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="">Root (No Folder)</option>
                        {folders.map((folder) => (
                          <option key={folder.id} value={folder.id}>
                            {folder.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Extracted Content Preview</Label>
                    <div className="bg-muted/30 rounded-md p-4 max-h-[200px] overflow-y-auto whitespace-pre-wrap text-sm">
                      {extractedText.substring(0, 500)}
                      {extractedText.length > 500 && '...'}
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => handleDialogClose(false)} disabled={isProcessing}>
            Cancel
          </Button>
          {extractedText && (
            <Button
              onClick={handleSaveDocument}
              disabled={isProcessing || !documentTitle.trim() || !currentWorkspace?.id}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : success ? (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Save Document
                </>
              ) : (
                'Save Document'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentUploadDialog;
