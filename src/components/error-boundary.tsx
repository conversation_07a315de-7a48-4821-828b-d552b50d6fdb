import React, { Component, ErrorInfo, ReactNode } from 'react';
import { ApiErrorFallback } from './ui/api-error-fallback';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode | ((props: { error: Error; resetError: () => void }) => ReactNode);
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // If a fallback is provided as a function, call it with the error and reset function
      if (typeof this.props.fallback === 'function') {
        return this.props.fallback({
          error: this.state.error!,
          resetError: this.resetError,
        });
      }

      // If a fallback is provided as a ReactNode, render it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI using our ApiErrorFallback component
      return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
          <div className="w-full max-w-md">
            <ApiErrorFallback
              error={{
                message: this.state.error?.message || 'An unexpected error occurred',
                details: this.state.error?.stack,
                code: 'BOUNDARY_ERROR',
              }}
              title="Something went wrong"
              description="The application encountered an unexpected error."
              showDetails={true}
              retry={() => window.location.reload()}
              resetError={this.resetError}
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
