import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import UnifiedModal from '@/components/ui/unified-modal';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Upload, FileText, AlertCircle, Check, Loader2 } from 'lucide-react';
import { documentParser } from '@/services/documentParser';
import { contractExtractor } from '@/services/contractExtractor';

interface UnifiedImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImportComplete?: (data: any) => void;
  redirectToWizard?: boolean;
}

const UnifiedImportModal: React.FC<UnifiedImportModalProps> = ({
  open,
  onOpenChange,
  onImportComplete,
  redirectToWizard = true,
}) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [activeTab, setActiveTab] = useState('upload');
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedText, setExtractedText] = useState('');
  const [extractedData, setExtractedData] = useState<any>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [pastedText, setPastedText] = useState('');

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (!open) {
      setActiveTab('upload');
      setIsDragging(false);
      setIsProcessing(false);
      setExtractedText('');
      setExtractedData(null);
      setError('');
      setSuccess(false);
      setPastedText('');
    }
  }, [open]);

  // File handling
  const handleFileSelect = async (file: File) => {
    if (!file) return;
    
    setIsProcessing(true);
    setError('');
    setSuccess(false);
    
    try {
      // Parse document to extract text
      const parsedDocument = await documentParser.parseDocument(file);
      setExtractedText(parsedDocument.text);
      
      // Extract contract data
      const extractedContract = await contractExtractor.extractFromFile(file);
      setExtractedData(extractedContract);
      
      // Show success message
      setSuccess(true);
      
      // Automatically proceed after a short delay
      setTimeout(() => {
        handleProceed();
      }, 1500);
    } catch (err) {
      console.error('Error extracting text:', err);
      setError('Failed to extract text from the document. Please try a different file format.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Text handling
  const handleTextImport = () => {
    if (!pastedText.trim()) {
      setError('Please enter some text to import.');
      return;
    }

    setIsProcessing(true);
    setError('');
    setSuccess(false);

    try {
      // Extract contract data from text
      const extractedContract = contractExtractor.extractFromText(pastedText);
      setExtractedData(extractedContract);
      setExtractedText(pastedText);
      
      setSuccess(true);
      
      // Automatically proceed after a short delay
      setTimeout(() => {
        handleProceed();
      }, 1500);
    } catch (err) {
      console.error('Error processing text:', err);
      setError('Failed to process the text. Please check the format and try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Proceed to next step
  const handleProceed = () => {
    const importData = {
      title: extractedData?.metadata?.title || 'Imported Contract',
      content: extractedText,
      parties: extractedData?.parties || [],
      effectiveDate: extractedData?.metadata?.effectiveDate,
      type: extractedData?.metadata?.type || 'agreement',
      importedData: true
    };

    if (onImportComplete) {
      onImportComplete(importData);
    }

    if (redirectToWizard) {
      // Store extracted data in session storage
      sessionStorage.setItem('importedContractData', JSON.stringify(importData));
      navigate('/contracts/wizard?imported=true');
    }
    
    onOpenChange(false);
  };

  // Drag and drop handlers
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Build actions
  const actions = [];
  
  if (activeTab === 'paste' && !isProcessing && !success) {
    actions.push({
      label: 'Import Text',
      variant: 'default' as const,
      onClick: handleTextImport,
      disabled: !pastedText.trim(),
    });
  }

  if (success && extractedData) {
    actions.push({
      label: 'Proceed to Wizard',
      variant: 'default' as const,
      onClick: handleProceed,
    });
  }

  const renderUploadTab = () => (
    <div className="space-y-4">
      <div
        className={`border-2 ${isDragging ? 'border-primary' : 'border-dashed'} rounded-lg p-8 text-center cursor-pointer transition-colors`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileInputChange}
          className="hidden"
          accept=".pdf,.doc,.docx,.txt,.html"
        />
        <Upload className={`h-10 w-10 mx-auto mb-3 ${isDragging ? 'text-primary' : 'text-muted-foreground'}`} />
        <h3 className="text-lg font-medium mb-1">
          {isDragging ? 'Drop file here' : 'Drag & drop file here'}
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          or click to browse files
        </p>
        <p className="text-xs text-muted-foreground">
          Supports PDF, DOC, DOCX, TXT, and HTML files
        </p>
      </div>
    </div>
  );

  const renderPasteTab = () => (
    <div className="space-y-4">
      <Textarea
        placeholder="Paste your contract text here..."
        value={pastedText}
        onChange={(e) => setPastedText(e.target.value)}
        className="min-h-[200px]"
        disabled={isProcessing}
      />
      <p className="text-xs text-muted-foreground">
        Paste the full text of your contract. The system will automatically extract key information.
      </p>
    </div>
  );

  const renderContent = () => {
    if (isProcessing) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin mb-4" />
          <p className="text-muted-foreground">Processing document...</p>
        </div>
      );
    }

    if (success) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <Check className="h-12 w-12 text-green-500 mb-4" />
          <h3 className="text-lg font-medium mb-2">Import Successful!</h3>
          <p className="text-muted-foreground text-center mb-4">
            Document processed successfully. Ready to proceed to the contract wizard.
          </p>
          {extractedData?.metadata?.title && (
            <p className="text-sm text-muted-foreground">
              Detected title: <strong>{extractedData.metadata.title}</strong>
            </p>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="paste">Paste Text</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload">
            {renderUploadTab()}
          </TabsContent>
          
          <TabsContent value="paste">
            {renderPasteTab()}
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  return (
    <UnifiedModal
      open={open}
      onOpenChange={onOpenChange}
      type="dialog"
      size="lg"
      title="Import Document"
      description="Upload an existing document or paste text to create a new contract"
      actions={actions}
      loading={isProcessing}
      loadingText="Processing document..."
    >
      {renderContent()}
    </UnifiedModal>
  );
};

export default UnifiedImportModal;
