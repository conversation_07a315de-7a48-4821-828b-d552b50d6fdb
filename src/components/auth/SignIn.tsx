import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { ThemeProvider } from '@/lib/theme-provider';

const SignIn: React.FC = () => {
  return (
    <ThemeProvider defaultTheme="system" storageKey="legalai-theme">
      <div className="flex min-h-screen items-center justify-center bg-background p-4">
        <div className="w-full max-w-md">
          <div className="mb-6 text-center">
            <h1 className="text-2xl font-bold text-foreground">LegalAI</h1>
            <p className="text-muted-foreground">Sign in to your account</p>
          </div>
          <ClerkSignIn
            routing="path"
            path="/sign-in"
            signUpUrl="/sign-up"
          />
        </div>
      </div>
    </ThemeProvider>
  );
};

export default SignIn;
