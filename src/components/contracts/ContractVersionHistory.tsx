import React from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Clock, Download, Eye, History, RotateCcw } from "lucide-react";

interface ContractVersionHistoryProps {
  contractId: string;
}

const ContractVersionHistory = ({
  contractId,
}: ContractVersionHistoryProps) => {
  // Handle view version
  const handleViewVersion = (versionId: string) => {
    console.log("Viewing version:", versionId);
    alert(`Viewing version details`);
  };

  // Handle download version
  const handleDownloadVersion = (versionId: string) => {
    console.log("Downloading version:", versionId);
    alert(`Version download started`);
  };

  // Handle restore version
  const handleRestoreVersion = (versionId: string) => {
    console.log("Restoring version:", versionId);
    alert(`Contract restored to selected version`);
  };

  // Handle compare versions
  const handleCompareVersions = () => {
    console.log("Comparing versions");
    alert("Version comparison view would open here");
  };
  // Mock data for version history
  const versions = [
    {
      id: "v3",
      version: 3,
      date: "2023-06-20T14:30:00Z",
      user: {
        name: "John Doe",
        initials: "JD",
      },
      changes: [
        "Updated payment terms to Net 30",
        "Added service level agreement section",
        "Clarified intellectual property rights",
      ],
      status: "current",
    },
    {
      id: "v2",
      version: 2,
      date: "2023-06-18T10:15:00Z",
      user: {
        name: "Jane Smith",
        initials: "JS",
      },
      changes: ["Revised scope of work", "Updated deliverables timeline"],
      status: "previous",
    },
    {
      id: "v1",
      version: 1,
      date: "2023-06-15T09:00:00Z",
      user: {
        name: "Jane Smith",
        initials: "JS",
      },
      changes: ["Initial contract draft"],
      status: "previous",
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Version History</CardTitle>
              <CardDescription>
                Track all changes made to this contract
              </CardDescription>
            </div>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={handleCompareVersions}
            >
              <RotateCcw className="h-4 w-4" />
              Compare Versions
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="relative pl-6 border-l-2 border-muted space-y-6">
            {versions.map((version, index) => (
              <div key={version.id} className="relative">
                {/* Timeline dot */}
                <div className="absolute -left-[25px] top-0 h-6 w-6 rounded-full bg-background border-2 border-primary flex items-center justify-center">
                  <History className="h-3 w-3 text-primary" />
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">
                          Version {version.version}
                        </h3>
                        {version.status === "current" && (
                          <Badge variant="default">Current</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(version.date)}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewVersion(version.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDownloadVersion(version.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <Separator className="my-3" />

                  <div className="flex items-start gap-3">
                    <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                      {version.user.initials}
                    </div>
                    <div>
                      <div className="font-medium text-sm">
                        {version.user.name}
                      </div>
                      <ul className="mt-2 space-y-1">
                        {version.changes.map((change, i) => (
                          <li key={i} className="text-sm">
                            • {change}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {version.status === "current" ? (
                    <div className="mt-3 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRestoreVersion(versions[1]?.id)}
                      >
                        Revert to Previous Version
                      </Button>
                    </div>
                  ) : (
                    <div className="mt-3 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRestoreVersion(version.id)}
                      >
                        Restore This Version
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContractVersionHistory;
