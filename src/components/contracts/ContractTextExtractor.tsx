import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, FileText, Copy, Download, Search, AlertCircle } from 'lucide-react';
import { documentParser } from '@/services/documentParser';
import { contractExtractor } from '@/services/contractExtractor';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

interface ContractTextExtractorProps {
  onExtractedText?: (text: string) => void;
  onExtractedData?: (data: any) => void;
}

const ContractTextExtractor: React.FC<ContractTextExtractorProps> = ({
  onExtractedText,
  onExtractedData
}) => {
  // State
  const [activeTab, setActiveTab] = useState<string>('upload');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [extractedText, setExtractedText] = useState<string>('');
  const [extractedData, setExtractedData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [pasteText, setPasteText] = useState<string>('');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);
  const [extractOptions, setExtractOptions] = useState({
    extractParties: true,
    extractClauses: true,
    extractDates: true,
    extractAmounts: true,
    extractObligations: true
  });
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle file upload
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    setIsProcessing(true);
    setError(null);
    
    try {
      // Parse document to extract text
      const parsedDocument = await documentParser.parseDocument(file);
      setExtractedText(parsedDocument.text);
      
      // Extract contract data
      const extractedContract = await contractExtractor.extractFromFile(file);
      setExtractedData(extractedContract);
      
      // Call callbacks if provided
      if (onExtractedText) onExtractedText(parsedDocument.text);
      if (onExtractedData) onExtractedData(extractedContract);
    } catch (err) {
      console.error('Error extracting text:', err);
      setError('Failed to extract text from the document. Please try a different file format.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle text paste
  const handleTextPaste = async () => {
    if (!pasteText.trim()) return;
    
    setIsProcessing(true);
    setError(null);
    
    try {
      // Extract text directly
      setExtractedText(pasteText);
      
      // Extract contract data from text
      const extractedContract = contractExtractor.extractFromText(pasteText);
      setExtractedData(extractedContract);
      
      // Call callbacks if provided
      if (onExtractedText) onExtractedText(pasteText);
      if (onExtractedData) onExtractedData(extractedContract);
    } catch (err) {
      console.error('Error processing text:', err);
      setError('Failed to process the text. Please check the format and try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle HTML paste
  const handleHtmlPaste = async () => {
    if (!pasteText.trim()) return;
    
    setIsProcessing(true);
    setError(null);
    
    try {
      // Check if the pasted content is HTML
      const isHtml = pasteText.trim().startsWith('<') && pasteText.includes('</');
      
      if (isHtml) {
        // Extract text from HTML
        const extractedTextFromHtml = documentParser.extractTextFromHtml(pasteText);
        setExtractedText(extractedTextFromHtml);
        
        // Extract contract data from HTML
        const extractedContract = contractExtractor.extractFromHtml(pasteText);
        setExtractedData(extractedContract);
        
        // Call callbacks if provided
        if (onExtractedText) onExtractedText(extractedTextFromHtml);
        if (onExtractedData) onExtractedData(extractedContract);
      } else {
        // Treat as plain text
        handleTextPaste();
      }
    } catch (err) {
      console.error('Error processing HTML:', err);
      setError('Failed to process the HTML. Please check the format and try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle drag events
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleFileUpload(e.dataTransfer.files);
  };
  
  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(e.target.files);
  };
  
  // Copy extracted text to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(extractedText);
    // Could add a toast notification here
  };
  
  // Download extracted text as file
  const downloadExtractedText = () => {
    const blob = new Blob([extractedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted-text.txt';
    a.click();
    URL.revokeObjectURL(url);
  };
  
  // Highlight search terms in text
  const highlightSearchTerm = (text: string) => {
    if (!searchTerm.trim()) return text;
    
    const parts = text.split(new RegExp(`(${searchTerm})`, 'gi'));
    
    return parts.map((part, i) => 
      part.toLowerCase() === searchTerm.toLowerCase() 
        ? <mark key={i} className="bg-yellow-200 dark:bg-yellow-800">{part}</mark> 
        : part
    );
  };
  
  // Toggle advanced options
  const toggleAdvancedOptions = () => {
    setShowAdvancedOptions(!showAdvancedOptions);
  };
  
  // Update extraction options
  const updateExtractOption = (option: keyof typeof extractOptions, value: boolean) => {
    setExtractOptions({
      ...extractOptions,
      [option]: value
    });
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Contract Text Extractor</CardTitle>
        <CardDescription>
          Extract text and structured data from contract documents
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="paste">Paste Text</TabsTrigger>
            <TabsTrigger value="results">Results</TabsTrigger>
          </TabsList>
          
          {/* Upload File Tab */}
          <TabsContent value="upload">
            <div className="space-y-4">
              <div
                className={`border-2 ${isDragging ? 'border-primary' : 'border-dashed'} rounded-lg p-8 text-center cursor-pointer transition-colors`}
                onDragEnter={handleDragEnter}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileInputChange}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.txt,.html"
                />
                <Upload className={`h-10 w-10 mx-auto mb-3 ${isDragging ? 'text-primary' : 'text-muted-foreground'}`} />
                <h3 className="text-lg font-medium mb-1">
                  {isDragging ? 'Drop file here' : 'Drag & drop file here'}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  or click to browse
                </p>
                <div className="text-xs text-muted-foreground">
                  Supported formats: PDF, DOCX, TXT, HTML
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="advanced-options"
                  checked={showAdvancedOptions}
                  onCheckedChange={() => toggleAdvancedOptions()}
                />
                <Label htmlFor="advanced-options">Show advanced extraction options</Label>
              </div>
              
              {showAdvancedOptions && (
                <div className="bg-muted/50 p-4 rounded-md space-y-3">
                  <h4 className="text-sm font-medium mb-2">Extraction Options</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="extract-parties"
                        checked={extractOptions.extractParties}
                        onCheckedChange={(checked) => updateExtractOption('extractParties', !!checked)}
                      />
                      <Label htmlFor="extract-parties">Extract Parties</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="extract-clauses"
                        checked={extractOptions.extractClauses}
                        onCheckedChange={(checked) => updateExtractOption('extractClauses', !!checked)}
                      />
                      <Label htmlFor="extract-clauses">Extract Clauses</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="extract-dates"
                        checked={extractOptions.extractDates}
                        onCheckedChange={(checked) => updateExtractOption('extractDates', !!checked)}
                      />
                      <Label htmlFor="extract-dates">Extract Dates</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="extract-amounts"
                        checked={extractOptions.extractAmounts}
                        onCheckedChange={(checked) => updateExtractOption('extractAmounts', !!checked)}
                      />
                      <Label htmlFor="extract-amounts">Extract Amounts</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id="extract-obligations"
                        checked={extractOptions.extractObligations}
                        onCheckedChange={(checked) => updateExtractOption('extractObligations', !!checked)}
                      />
                      <Label htmlFor="extract-obligations">Extract Obligations</Label>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          
          {/* Paste Text Tab */}
          <TabsContent value="paste">
            <div className="space-y-4">
              <div>
                <Label htmlFor="paste-text">Paste contract text or HTML</Label>
                <Textarea
                  id="paste-text"
                  placeholder="Paste contract text or HTML here..."
                  className="min-h-[200px] mt-1"
                  value={pasteText}
                  onChange={(e) => setPasteText(e.target.value)}
                />
              </div>
              
              <div className="flex space-x-2">
                <Button 
                  onClick={handleTextPaste}
                  disabled={isProcessing || !pasteText.trim()}
                >
                  Extract as Text
                </Button>
                <Button 
                  variant="outline"
                  onClick={handleHtmlPaste}
                  disabled={isProcessing || !pasteText.trim()}
                >
                  Extract as HTML
                </Button>
              </div>
            </div>
          </TabsContent>
          
          {/* Results Tab */}
          <TabsContent value="results">
            {isProcessing ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
                <p className="text-muted-foreground">Processing document...</p>
              </div>
            ) : error ? (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4 mr-2" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : extractedText ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Extracted Text</h3>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={copyToClipboard}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                    <Button variant="outline" size="sm" onClick={downloadExtractedText}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search in extracted text..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                </div>
                
                <div className="bg-muted/30 rounded-md p-4 max-h-[400px] overflow-y-auto whitespace-pre-wrap text-sm">
                  {highlightSearchTerm(extractedText)}
                </div>
                
                {extractedData && (
                  <>
                    <Separator className="my-4" />
                    
                    <div>
                      <h3 className="text-lg font-medium mb-4">Extracted Data</h3>
                      
                      {/* Contract Metadata */}
                      {extractedData.metadata && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Contract Metadata</h4>
                          <div className="bg-muted/30 rounded-md p-3">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              {extractedData.metadata.title && (
                                <div>
                                  <span className="font-medium">Title:</span> {extractedData.metadata.title}
                                </div>
                              )}
                              {extractedData.metadata.type && (
                                <div>
                                  <span className="font-medium">Type:</span> {extractedData.metadata.type}
                                </div>
                              )}
                              {extractedData.metadata.effectiveDate && (
                                <div>
                                  <span className="font-medium">Effective Date:</span>{' '}
                                  {new Date(extractedData.metadata.effectiveDate).toLocaleDateString()}
                                </div>
                              )}
                              {extractedData.metadata.pageCount && (
                                <div>
                                  <span className="font-medium">Pages:</span> {extractedData.metadata.pageCount}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Parties */}
                      {extractedData.parties && extractedData.parties.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Parties</h4>
                          <div className="space-y-2">
                            {extractedData.parties.map((party: any) => (
                              <div key={party.id} className="bg-muted/30 rounded-md p-3">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="font-medium">{party.name}</div>
                                  <Badge variant="outline">{party.type}</Badge>
                                </div>
                                {party.role && <div className="text-sm text-muted-foreground">Role: {party.role}</div>}
                                {party.address && <div className="text-sm text-muted-foreground">Address: {party.address}</div>}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* Clauses */}
                      {extractedData.clauses && extractedData.clauses.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Key Clauses</h4>
                          <div className="space-y-2">
                            {extractedData.clauses.slice(0, 5).map((clause: any) => (
                              <div key={clause.id} className="bg-muted/30 rounded-md p-3">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="font-medium">{clause.title}</div>
                                  <div className="flex items-center space-x-2">
                                    <Badge variant="outline">{clause.category}</Badge>
                                    {clause.riskLevel && clause.riskLevel !== 'unknown' && (
                                      <Badge 
                                        className={
                                          clause.riskLevel === 'high' 
                                            ? 'bg-red-100 text-red-800 hover:bg-red-200' 
                                            : clause.riskLevel === 'medium'
                                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                                        }
                                      >
                                        {clause.riskLevel} risk
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                <div className="text-sm text-muted-foreground mb-1">{clause.location}</div>
                                <div className="text-sm line-clamp-2">{clause.content.substring(0, 150)}...</div>
                              </div>
                            ))}
                            {extractedData.clauses.length > 5 && (
                              <div className="text-center text-sm text-muted-foreground">
                                + {extractedData.clauses.length - 5} more clauses
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {/* Dates */}
                      {extractedData.dates && extractedData.dates.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Important Dates</h4>
                          <div className="space-y-2">
                            {extractedData.dates.map((date: any) => (
                              <div key={date.id} className="bg-muted/30 rounded-md p-3">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="font-medium">
                                    {typeof date.date === 'string' ? date.date : new Date(date.date).toLocaleDateString()}
                                  </div>
                                  <Badge variant="outline">
                                    {date.type.charAt(0).toUpperCase() + date.type.slice(1)}
                                  </Badge>
                                </div>
                                <div className="text-sm text-muted-foreground">{date.context}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* Amounts */}
                      {extractedData.amounts && extractedData.amounts.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium mb-2">Financial Terms</h4>
                          <div className="space-y-2">
                            {extractedData.amounts.map((amount: any) => (
                              <div key={amount.id} className="bg-muted/30 rounded-md p-3">
                                <div className="flex items-center justify-between mb-1">
                                  <div className="font-medium">
                                    {amount.amount} {amount.currency}
                                  </div>
                                  <Badge variant="outline">
                                    {amount.type.charAt(0).toUpperCase() + amount.type.slice(1)}
                                  </Badge>
                                </div>
                                <div className="text-sm text-muted-foreground">{amount.context}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-1">No text extracted yet</h3>
                <p className="text-sm text-muted-foreground">
                  Upload a document or paste text to extract content
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ContractTextExtractor;
