import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { MessageSquare, Send, ThumbsUp } from "lucide-react";

interface Comment {
  id: string;
  user: {
    name: string;
    initials: string;
    role: string;
  };
  text: string;
  timestamp: string;
  likes: number;
  replies?: Comment[];
}

interface ContractCommentsProps {
  contractId: string;
}

const ContractComments = ({ contractId }: ContractCommentsProps) => {
  const [newComment, setNewComment] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState("");

  // TODO: Use contractId to fetch comments from API
  // Comments are now managed in state

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),
    );

    if (diffInDays === 0) {
      return new Intl.DateTimeFormat("en-US", {
        hour: "numeric",
        minute: "numeric",
      }).format(date);
    } else if (diffInDays === 1) {
      return "Yesterday";
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
      }).format(date);
    }
  };

  const [comments, setComments] = useState<Comment[]>([
    {
      id: "c1",
      user: {
        name: "Jane Smith",
        initials: "JS",
        role: "Legal Counsel",
      },
      text: "I've reviewed the payment terms section. We should consider changing the payment schedule to align with project milestones rather than calendar dates.",
      timestamp: "2023-06-20T15:30:00Z",
      likes: 2,
      replies: [
        {
          id: "r1",
          user: {
            name: "John Doe",
            initials: "JD",
            role: "Contract Manager",
          },
          text: "Good point. I'll update the payment schedule in the next revision.",
          timestamp: "2023-06-20T16:15:00Z",
          likes: 1,
        },
      ],
    },
    {
      id: "c2",
      user: {
        name: "Michael Chen",
        initials: "MC",
        role: "Finance Director",
      },
      text: "The liability cap seems too high for this type of agreement. We typically limit it to 1x the contract value for service agreements.",
      timestamp: "2023-06-19T11:45:00Z",
      likes: 3,
    },
    {
      id: "c3",
      user: {
        name: "Sarah Johnson",
        initials: "SJ",
        role: "Compliance Officer",
      },
      text: "We need to add a clause about data protection and GDPR compliance since this involves processing customer data.",
      timestamp: "2023-06-18T09:20:00Z",
      likes: 4,
    },
  ]);

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      // Create a new comment
      const newCommentObj: Comment = {
        id: `c${comments.length + 1}`,
        user: {
          name: "You",
          initials: "YO",
          role: "Contract Manager",
        },
        text: newComment,
        timestamp: new Date().toISOString(),
        likes: 0,
      };

      // Add the new comment to the list
      setComments([newCommentObj, ...comments]);
      setNewComment("");

      // In a real app, this would add the comment to the backend
      console.log("New comment:", newComment);
    }
  };

  const handleSubmitReply = (commentId: string) => {
    if (replyText.trim()) {
      // Create a new reply
      const newReply: Comment = {
        id: `r${Math.random().toString(36).substring(2, 9)}`,
        user: {
          name: "You",
          initials: "YO",
          role: "Contract Manager",
        },
        text: replyText,
        timestamp: new Date().toISOString(),
        likes: 0,
      };

      // Add the reply to the appropriate comment
      const updatedComments = comments.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), newReply],
          };
        }
        return comment;
      });

      setComments(updatedComments);
      setReplyText("");
      setReplyingTo(null);

      // In a real app, this would add the reply to the backend
      console.log(`Reply to comment ${commentId}:`, replyText);
    }
  };

  const handleLike = (commentId: string) => {
    // Update the like count for the comment or reply
    const updatedComments = comments.map(comment => {
      // Check if this is the comment to update
      if (comment.id === commentId) {
        return { ...comment, likes: comment.likes + 1 };
      }

      // Check if the comment has replies and if one of them needs to be updated
      if (comment.replies) {
        const updatedReplies = comment.replies.map(reply => {
          if (reply.id === commentId) {
            return { ...reply, likes: reply.likes + 1 };
          }
          return reply;
        });

        return { ...comment, replies: updatedReplies };
      }

      return comment;
    });

    setComments(updatedComments);

    // In a real app, this would update the like count in the backend
    console.log(`Liked comment ${commentId}`);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <MessageSquare className="h-5 w-5 text-primary mr-2" />
            <CardTitle>Discussion</CardTitle>
          </div>
          <CardDescription>
            Collaborate with your team on this contract
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {comments.map((comment) => (
              <div key={comment.id} className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                    {comment.user.initials}
                  </div>
                  <div className="flex-1">
                    <div className="bg-muted p-3 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium">
                            {comment.user.name}
                          </span>
                          <span className="text-xs text-muted-foreground ml-2">
                            {comment.user.role}
                          </span>
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(comment.timestamp)}
                        </span>
                      </div>
                      <p className="mt-2 text-sm">{comment.text}</p>
                    </div>
                    <div className="flex items-center gap-4 mt-2 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 text-muted-foreground hover:text-foreground"
                        onClick={() => handleLike(comment.id)}
                      >
                        <ThumbsUp className="h-4 w-4 mr-1" />
                        <span className="text-xs">{comment.likes}</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 text-muted-foreground hover:text-foreground"
                        onClick={() => setReplyingTo(comment.id)}
                      >
                        <span className="text-xs">Reply</span>
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Replies */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="ml-12 space-y-4">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex items-start gap-3">
                        <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                          {reply.user.initials}
                        </div>
                        <div className="flex-1">
                          <div className="bg-muted p-3 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div>
                                <span className="font-medium">
                                  {reply.user.name}
                                </span>
                                <span className="text-xs text-muted-foreground ml-2">
                                  {reply.user.role}
                                </span>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {formatDate(reply.timestamp)}
                              </span>
                            </div>
                            <p className="mt-2 text-sm">{reply.text}</p>
                          </div>
                          <div className="flex items-center gap-4 mt-2 ml-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-auto p-0 text-muted-foreground hover:text-foreground"
                              onClick={() => handleLike(reply.id)}
                            >
                              <ThumbsUp className="h-4 w-4 mr-1" />
                              <span className="text-xs">{reply.likes}</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Reply form */}
                {replyingTo === comment.id && (
                  <div className="ml-12 flex items-start gap-3">
                    <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                      YO
                    </div>
                    <div className="flex-1">
                      <Textarea
                        placeholder="Write a reply..."
                        value={replyText}
                        onChange={(e) => setReplyText(e.target.value)}
                        className="min-h-[80px]"
                      />
                      <div className="flex justify-end gap-2 mt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setReplyingTo(null)}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleSubmitReply(comment.id)}
                        >
                          Reply
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <Separator />
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <div className="w-full space-y-4">
            <Textarea
              placeholder="Add a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex justify-end">
              <Button onClick={handleSubmitComment}>
                <Send className="h-4 w-4 mr-2" />
                Post Comment
              </Button>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ContractComments;
