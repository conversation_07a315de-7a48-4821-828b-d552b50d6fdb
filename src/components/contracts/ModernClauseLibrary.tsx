import React, { useState, useEffect } from "react";
import { useApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Checkbox } from "../ui/checkbox";
import { ScrollArea } from "../ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "../ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  Book,
  Folder,
  MoreHorizontal,
  Plus,
  Search,
  Tag,
  Copy,
  Trash,
  Eye,
  LayoutGrid,
  LayoutList,
  Zap,
  Layers,
  Star,
  Clock,
  Heart,
} from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table";

// Import new components
import ClauseCategorizationSystem from "./ClauseCategorizationSystem";
import SmartClauseSuggestions from "./SmartClauseSuggestions";

// Types for our component
interface Clause {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  lastUpdated: string;
  version: string;
  approved: boolean;
  author: string;
  isFavorite?: boolean;
  lastUsed?: string;
}

// Mock data for clauses
const mockClauses: Clause[] = [
  {
    id: "cl-001",
    title: "Standard Limitation of Liability",
    content: "In no event shall either party be liable to the other party for any indirect, special, incidental, consequential, or punitive damages, including lost profits, arising out of or relating to this Agreement, regardless of the legal or equitable theory (contract, tort, or otherwise) upon which the claim is based, even if advised of the possibility of such damages.",
    category: "liability",
    tags: ["limitation", "standard", "risk-mitigation"],
    lastUpdated: "2023-05-15",
    version: "1.2",
    approved: true,
    author: "Jane Smith",
    isFavorite: true,
    lastUsed: "2023-08-10"
  },
  {
    id: "cl-002",
    title: "Enhanced Limitation of Liability",
    content: "Neither party shall be liable for any indirect, special, incidental, consequential, punitive, or exemplary damages, including, but not limited to, damages for loss of profits, goodwill, use, data, or other intangible losses that result from the use of, or inability to use, the service or any products or services purchased therein.",
    category: "liability",
    tags: ["limitation", "enhanced", "high-risk"],
    lastUpdated: "2023-06-22",
    version: "2.0",
    approved: true,
    author: "John Doe"
  },
  {
    id: "cl-003",
    title: "Basic Confidentiality Clause",
    content: "During the term of this Agreement and for a period of three (3) years thereafter, each party shall maintain in confidence all Confidential Information of the other party.",
    category: "confidentiality",
    tags: ["nda", "standard"],
    lastUpdated: "2023-04-10",
    version: "1.0",
    approved: true,
    author: "Sarah Johnson",
    isFavorite: true,
    lastUsed: "2023-09-15"
  },
  {
    id: "cl-004",
    title: "Extended Confidentiality Clause",
    content: "During the term of this Agreement and for a period of five (5) years thereafter, each party shall maintain in confidence all Confidential Information of the other party. 'Confidential Information' means all non-public information disclosed by one party to the other that is designated as confidential or that, given the nature of the information or the circumstances surrounding its disclosure, reasonably should be considered confidential.",
    category: "confidentiality",
    tags: ["nda", "extended", "sensitive"],
    lastUpdated: "2023-07-03",
    version: "1.5",
    approved: true,
    author: "Michael Chen"
  },
  {
    id: "cl-005",
    title: "Standard Termination Clause",
    content: "Either party may terminate this Agreement upon thirty (30) days prior written notice to the other party if the other party materially breaches this Agreement and fails to cure such breach within such thirty (30) day period.",
    category: "termination",
    tags: ["standard", "breach"],
    lastUpdated: "2023-03-18",
    version: "1.1",
    approved: true,
    author: "Jane Smith"
  },
  {
    id: "cl-006",
    title: "Payment Terms (Net-30)",
    content: "Customer shall pay all invoices within thirty (30) days of receipt. Overdue payments shall bear interest at the rate of 1.5% per month or the maximum rate permitted by law, whichever is lower.",
    category: "payment",
    tags: ["financial", "standard"],
    lastUpdated: "2023-05-28",
    version: "1.0",
    approved: true,
    author: "Robert Johnson",
    lastUsed: "2023-09-20"
  },
  {
    id: "cl-007",
    title: "Force Majeure",
    content: "Neither party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including but not limited to acts of God, natural disasters, terrorism, riots, or wars.",
    category: "general",
    tags: ["standard", "risk-mitigation"],
    lastUpdated: "2023-02-14",
    version: "1.3",
    approved: true,
    author: "Emily Williams"
  },
  {
    id: "cl-008",
    title: "GDPR Compliance Clause",
    content: "Each party shall comply with all applicable data protection and privacy laws, including the General Data Protection Regulation (GDPR), when processing personal data in connection with this Agreement.",
    category: "compliance",
    tags: ["gdpr", "data-protection", "regulatory"],
    lastUpdated: "2023-06-30",
    version: "2.1",
    approved: true,
    author: "Michael Chen"
  },
];

// Category and tag data
const categories = [
  "all",
  "liability",
  "confidentiality",
  "termination",
  "payment",
  "compliance",
  "general",
];

const availableTags = [
  "standard",
  "enhanced",
  "limitation",
  "nda",
  "breach",
  "financial",
  "gdpr",
  "data-protection",
  "regulatory",
  "risk-mitigation",
  "high-risk",
  "sensitive",
  "extended",
];

interface ModernClauseLibraryProps {
  onClauseSelect?: (clause: Clause) => void;
}

const ModernClauseLibrary: React.FC<ModernClauseLibraryProps> = ({
  onClauseSelect
}) => {
  const { fetch, fetchArray, api } = useApi();
  const { toast } = useToast();
  const [clauses, setClauses] = useState<Clause[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showApprovedOnly, setShowApprovedOnly] = useState(false);
  const [activeTab, setActiveTab] = useState("browse");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Fetch clauses from API
  useEffect(() => {
    const fetchClauses = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch clauses from API using fetchArray to ensure we get an array
        const result = await fetchArray(
          () => api.get('/clauses'),
          "Loading clause library...",
          "Failed to load clauses"
        );

        if (result) {
          // Ensure result is an array before setting it
          if (Array.isArray(result)) {
            setClauses(result as Clause[]);
          } else {
            console.error("API returned non-array result for clauses:", result);
            setError("Invalid data format received from API. Using sample data instead.");
            setClauses(mockClauses);
          }
        } else {
          // If the endpoint doesn't exist yet or returns no data, use mock data
          setClauses(mockClauses);
        }
      } catch (err) {
        console.error("Error fetching clauses:", err);
        setError("Failed to load clauses. Using sample data instead.");
        // Use mock data when there's an error
        setClauses(mockClauses);
      } finally {
        setIsLoading(false);
      }
    };

    fetchClauses();
  }, [fetch, api]);

  // Contract context for smart suggestions
  const contractContext = {
    contractType: "Service Agreement",
    jurisdiction: "California, USA",
    industry: "Technology",
    parties: [
      { type: "Client", name: "Acme Corporation" },
      { type: "Service Provider", name: "Tech Solutions Inc." },
    ],
    value: 120000,
    currency: "USD",
  };

  // New clause dialog
  const [isNewClauseDialogOpen, setIsNewClauseDialogOpen] = useState(false);
  const [newClause, setNewClause] = useState<Partial<Clause>>({
    title: "",
    content: "",
    category: "general",
    tags: [],
    approved: false,
    author: "Current User", // In a real app, get this from auth context
  });

  // Filter clauses based on search, category, tags, and approval status
  const filteredClauses = Array.isArray(clauses) ? clauses.filter((clause) => {
    const matchesSearch = !searchTerm ||
      clause.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      clause.content.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === 'all' ||
      clause.category === selectedCategory;

    const matchesTags = selectedTags.length === 0 ||
      selectedTags.some(tag => clause.tags.includes(tag));

    const matchesApproval = !showApprovedOnly || clause.approved;

    return matchesSearch && matchesCategory && matchesTags && matchesApproval;
  }) : [];

  // Toggle tag selection in filters
  const toggleTag = (tag: string) => {
    setSelectedTags((prevTags) =>
      prevTags.includes(tag)
        ? prevTags.filter((t) => t !== tag)
        : [...prevTags, tag]
    );
  };

  // Handle creating a new clause
  const handleCreateClause = async () => {
    if (!newClause.title || !newClause.content || !newClause.category) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Prepare the clause data
    const clauseData = {
      title: newClause.title,
      content: newClause.content,
      category: newClause.category,
      tags: newClause.tags || [],
      approved: newClause.approved || false,
      version: "1.0",
    };

    try {
      setIsLoading(true);

      // Create clause via API
      const result = await fetch(
        () => api.post('/clauses', clauseData),
        "Creating clause...",
        "Failed to create clause"
      );

      if (result) {
        // Add the new clause to the state
        setClauses([...clauses, result as Clause]);

        toast({
          title: "Clause Created",
          description: "The clause has been created successfully",
        });
      }

      // Close dialog and reset form
      setIsNewClauseDialogOpen(false);
      setNewClause({
        title: "",
        content: "",
        category: "general",
        tags: [],
        approved: false,
        author: "Current User",
      });
    } catch (err) {
      console.error("Error creating clause:", err);

      // Fallback to client-side only if API fails
      const newClauseWithId: Clause = {
        id: `cl-${Math.floor(Math.random() * 1000)}`,
        title: newClause.title,
        content: newClause.content,
        category: newClause.category,
        tags: newClause.tags || [],
        lastUpdated: new Date().toISOString().split("T")[0],
        version: "1.0",
        approved: newClause.approved || false,
        author: newClause.author || "Current User",
      };

      setClauses([...clauses, newClauseWithId]);
      setIsNewClauseDialogOpen(false);
      setNewClause({
        title: "",
        content: "",
        category: "general",
        tags: [],
        approved: false,
        author: "Current User",
      });

      toast({
        title: "Created Locally",
        description: "The clause was created locally due to API error",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle tag selection for new clause
  const toggleNewClauseTag = (tag: string) => {
    setNewClause((prev) => ({
      ...prev,
      tags: prev.tags?.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...(prev.tags || []), tag],
    }));
  };

  // View clause details dialog state
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedClause, setSelectedClause] = useState<Clause | null>(null);

  // View clause details
  const handleViewClauseDetail = (clause: Clause) => {
    setSelectedClause(clause);
    setIsViewDialogOpen(true);
  };

  // Delete clause
  const handleDeleteClause = async (id: string) => {
    try {
      // Delete clause via API
      await fetch(
        () => api.delete(`/clauses/${id}`),
        "Deleting clause...",
        "Failed to delete clause"
      );

      // Update local state
      setClauses(clauses.filter((clause) => clause.id !== id));

      toast({
        title: "Clause Deleted",
        description: "The clause has been deleted successfully",
      });
    } catch (err) {
      console.error("Error deleting clause:", err);

      // Fallback to client-side only if API fails
      setClauses(clauses.filter((clause) => clause.id !== id));

      toast({
        title: "Deleted Locally",
        description: "The clause was removed locally due to API error",
        variant: "destructive",
      });
    }
  };

  // Toggle favorite status
  const handleToggleFavorite = async (id: string) => {
    // Find the clause
    const clause = clauses.find(c => c.id === id);
    if (!clause) return;

    // New favorite status
    const newFavoriteStatus = !clause.isFavorite;

    try {
      // Update via API
      await fetch(
        () => api.put(`/clauses/${id}`, { isFavorite: newFavoriteStatus }),
        "Updating favorite status...",
        "Failed to update favorite status"
      );

      // Update local state
      setClauses(clauses.map(clause =>
        clause.id === id
          ? { ...clause, isFavorite: newFavoriteStatus }
          : clause
      ));
    } catch (err) {
      console.error("Error updating favorite status:", err);

      // Fallback to client-side only if API fails
      setClauses(clauses.map(clause =>
        clause.id === id
          ? { ...clause, isFavorite: newFavoriteStatus }
          : clause
      ));
    }
  };

  // Update last used timestamp
  const handleUseClause = async (clause: Clause) => {
    // Current date in ISO format
    const currentDate = new Date().toISOString();
    const formattedDate = currentDate.split('T')[0];

    try {
      // Update via API
      await fetch(
        () => api.put(`/clauses/${clause.id}/use`, {
          lastUsed: currentDate
        }),
        "Updating usage...",
        "Failed to update usage"
      );

      // Create updated clause
      const updatedClause = {
        ...clause,
        lastUsed: formattedDate
      };

      // Update the clauses array
      setClauses(clauses.map(c => c.id === clause.id ? updatedClause : c));

      // Call the external handler if provided
      if (onClauseSelect) {
        onClauseSelect(updatedClause);
      } else {
        console.log("Clause selected:", updatedClause);
      }
    } catch (err) {
      console.error("Error updating clause usage:", err);

      // Fallback to client-side only if API fails
      const updatedClause = {
        ...clause,
        lastUsed: formattedDate
      };

      // Update the clauses array
      setClauses(clauses.map(c => c.id === clause.id ? updatedClause : c));

      // Call the external handler if provided
      if (onClauseSelect) {
        onClauseSelect(updatedClause);
      } else {
        console.log("Clause selected:", updatedClause);
      }
    }
  };

  // Get appropriate color class for tag badge based on design system
  const getTagColorClass = (tag: string) => {
    // Map tags to specific colors from our design system
    const tagColorMap: Record<string, string> = {
      "standard": "tag-blue",
      "enhanced": "tag-purple",
      "limitation": "tag-orange",
      "nda": "tag-pink",
      "breach": "tag-red",
      "financial": "tag-green",
      "gdpr": "tag-blue",
      "data-protection": "tag-blue",
      "regulatory": "tag-yellow",
      "risk-mitigation": "tag-orange",
      "high-risk": "tag-red",
      "sensitive": "tag-pink",
      "extended": "tag-purple",
    };

    return tagColorMap[tag] || "tag-gray";
  };

  // Get category badge class based on design system
  const getCategoryBadgeClass = (category: string) => {
    // Map categories to specific colors from our design system
    const categoryColorMap: Record<string, string> = {
      "liability": "tag-red",
      "confidentiality": "tag-blue",
      "termination": "tag-orange",
      "payment": "tag-green",
      "compliance": "tag-yellow",
      "general": "tag-gray",
    };

    return categoryColorMap[category] || "tag-gray";
  };

  // Render grid view of clauses - simplified styling
  const renderGridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {filteredClauses.map((clause) => (
        <Card key={clause.id} className="h-full flex flex-col border border-gray-200 shadow-none">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-lg font-medium">{clause.title}</CardTitle>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleUseClause(clause)}>
                    <Copy className="mr-2 h-4 w-4" /> Use Clause
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleViewClauseDetail(clause)}>
                    <Eye className="mr-2 h-4 w-4" /> View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleToggleFavorite(clause.id)}>
                    {clause.isFavorite ? (
                      <><Star className="mr-2 h-4 w-4 fill-current" /> Remove from Favorites</>
                    ) : (
                      <><Star className="mr-2 h-4 w-4" /> Add to Favorites</>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDeleteClause(clause.id)}>
                    <Trash className="mr-2 h-4 w-4" /> Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <Badge variant="outline" className={getCategoryBadgeClass(clause.category)}>
              {clause.category}
            </Badge>
          </CardHeader>
          <CardContent className="flex-grow">
            <div className="text-sm mb-4 line-clamp-3 text-gray-600">{clause.content}</div>
            <div className="flex flex-wrap gap-1 mb-4">
              {clause.tags.map((tag) => (
                <Badge key={tag} className={getTagColorClass(tag)}>
                  {tag}
                </Badge>
              ))}
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-auto">
              <span>v{clause.version}</span>
              <span>Updated: {clause.lastUpdated}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Render list view of clauses - simplified styling
  const renderListView = () => (
    <Card className="border border-gray-200 shadow-none">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Clause</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Version</TableHead>
            <TableHead>Updated</TableHead>
            <TableHead>Tags</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredClauses.map((clause) => (
            <TableRow key={clause.id}>
              <TableCell className="font-medium">{clause.title}</TableCell>
              <TableCell>
                <Badge variant="outline" className={getCategoryBadgeClass(clause.category)}>
                  {clause.category}
                </Badge>
              </TableCell>
              <TableCell className="text-gray-600">v{clause.version}</TableCell>
              <TableCell className="text-gray-600">{clause.lastUpdated}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {clause.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} className={getTagColorClass(tag)}>
                      {tag}
                    </Badge>
                  ))}
                  {clause.tags.length > 2 && (
                    <Badge variant="outline" className="border border-gray-200 bg-transparent text-gray-600">
                      +{clause.tags.length - 2}
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button variant="ghost" size="icon" onClick={() => handleUseClause(clause)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => handleViewClauseDetail(clause)}>
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleToggleFavorite(clause.id)}
                    className={clause.isFavorite ? "text-amber-500" : ""}
                  >
                    <Star className={`h-4 w-4 ${clause.isFavorite ? "fill-current" : ""}`} />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleDeleteClause(clause.id)}>
                        <Trash className="mr-2 h-4 w-4" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );

  return (
    <div className="w-full h-full p-6 bg-background overflow-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-xl font-medium mb-4">Clause Library</h1>
        <div className="flex items-center gap-2">
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === "grid" ? "secondary" : "ghost"}
              size="icon"
              className="rounded-r-none"
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "secondary" : "ghost"}
              size="icon"
              className="rounded-l-none"
              onClick={() => setViewMode("list")}
            >
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
          <Button onClick={() => setIsNewClauseDialogOpen(true)} variant="outline" className="border-gray-300">
            <Plus className="h-4 w-4 mr-2" /> New Clause
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="browse">Browse</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
          <TabsTrigger value="recent">Recently Used</TabsTrigger>
          <TabsTrigger value="organize">
            <Layers className="h-4 w-4 mr-2" />
            Organize
          </TabsTrigger>
          <TabsTrigger value="suggestions">
            <Zap className="h-4 w-4 mr-2" />
            Smart Suggestions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="browse" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Left Column - Filters */}
            <div className="space-y-6">
              <Card className="border border-gray-200 shadow-none">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-medium">Search</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search clauses..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border border-gray-200 shadow-none">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-medium">Filters</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2 flex items-center">
                      <Folder className="h-4 w-4 mr-2" /> Categories
                    </h3>
                    <div className="space-y-1">
                      {categories.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Button
                            variant={selectedCategory === category ? "secondary" : "ghost"}
                            size="sm"
                            className="justify-start h-8 px-2 text-sm w-full"
                            onClick={() => setSelectedCategory(category)}
                          >
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2 flex items-center">
                      <Tag className="h-4 w-4 mr-2" /> Tags
                    </h3>
                    <ScrollArea className="h-40">
                      <div className="space-y-1">
                        {availableTags.map((tag) => (
                          <div key={tag} className="flex items-center space-x-2">
                            <Checkbox
                              id={`tag-${tag}`}
                              checked={selectedTags.includes(tag)}
                              onCheckedChange={() => toggleTag(tag)}
                            />
                            <label
                              htmlFor={`tag-${tag}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {tag}
                            </label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>

                  <div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="approved-only"
                        checked={showApprovedOnly}
                        onCheckedChange={(checked) =>
                          setShowApprovedOnly(checked as boolean)
                        }
                      />
                      <label
                        htmlFor="approved-only"
                        className="text-sm font-medium leading-none"
                      >
                        Show approved only
                      </label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Clauses */}
            <div className="md:col-span-3">
              {filteredClauses.length === 0 ? (
                <Card className="p-8 text-center border border-gray-200 shadow-none">
                  <Book className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No clauses found</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Try adjusting your filters or create a new clause.
                  </p>
                  <Button onClick={() => setIsNewClauseDialogOpen(true)} variant="outline" className="border-gray-300">
                    <Plus className="h-4 w-4 mr-2" /> New Clause
                  </Button>
                </Card>
              ) : (
                viewMode === "grid" ? renderGridView() : renderListView()
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="favorites">
          {clauses.some(clause => clause.isFavorite) ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {clauses
                .filter(clause => clause.isFavorite)
                .map((clause) => (
                  <Card key={clause.id} className="h-full flex flex-col border border-gray-200 shadow-none">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg font-medium">{clause.title}</CardTitle>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleToggleFavorite(clause.id)}
                            className="text-amber-500"
                          >
                            <Star className="h-4 w-4 fill-current" />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleUseClause(clause)}>
                                <Copy className="mr-2 h-4 w-4" /> Use Clause
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleViewClauseDetail(clause)}>
                                <Eye className="mr-2 h-4 w-4" /> View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteClause(clause.id)}>
                                <Trash className="mr-2 h-4 w-4" /> Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      <Badge variant="outline" className={getCategoryBadgeClass(clause.category)}>
                        {clause.category}
                      </Badge>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      <div className="text-sm mb-4 line-clamp-3 text-gray-600">{clause.content}</div>
                      <div className="flex flex-wrap gap-1 mb-4">
                        {clause.tags.map((tag) => (
                          <Badge key={tag} className={getTagColorClass(tag)}>
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-auto">
                        <span>v{clause.version}</span>
                        <span>Updated: {clause.lastUpdated}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          ) : (
            <Card className="border border-gray-200 shadow-none">
              <CardHeader>
                <CardTitle className="font-medium">Favorites</CardTitle>
                <CardDescription>
                  Your favorite clauses will appear here.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                  <p className="text-sm text-gray-500 mb-4">
                    You haven't added any clauses to your favorites yet.
                  </p>
                  <p className="text-sm text-gray-500">
                    Click the star icon on any clause to add it to your favorites.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="recent">
          {clauses.some(clause => clause.lastUsed) ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {clauses
                .filter(clause => clause.lastUsed)
                .sort((a, b) => {
                  // Sort by lastUsed date, most recent first
                  const dateA = a.lastUsed ? new Date(a.lastUsed).getTime() : 0;
                  const dateB = b.lastUsed ? new Date(b.lastUsed).getTime() : 0;
                  return dateB - dateA;
                })
                .map((clause) => (
                  <Card key={clause.id} className="h-full flex flex-col border border-gray-200 shadow-none">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg font-medium">{clause.title}</CardTitle>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleToggleFavorite(clause.id)}
                            className={clause.isFavorite ? "text-amber-500" : ""}
                          >
                            <Star className={`h-4 w-4 ${clause.isFavorite ? "fill-current" : ""}`} />
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleUseClause(clause)}>
                                <Copy className="mr-2 h-4 w-4" /> Use Clause
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleViewClauseDetail(clause)}>
                                <Eye className="mr-2 h-4 w-4" /> View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteClause(clause.id)}>
                                <Trash className="mr-2 h-4 w-4" /> Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      <Badge variant="outline" className={getCategoryBadgeClass(clause.category)}>
                        {clause.category}
                      </Badge>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      <div className="text-sm mb-4 line-clamp-3 text-gray-600">{clause.content}</div>
                      <div className="flex flex-wrap gap-1 mb-4">
                        {clause.tags.map((tag) => (
                          <Badge key={tag} className={getTagColorClass(tag)}>
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-auto">
                        <span>v{clause.version}</span>
                        <span>Last used: {clause.lastUsed}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          ) : (
            <Card className="border border-gray-200 shadow-none">
              <CardHeader>
                <CardTitle className="font-medium">Recently Used</CardTitle>
                <CardDescription>
                  Clauses you've recently used will appear here.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                  <p className="text-sm text-gray-500 mb-4">
                    You haven't used any clauses recently.
                  </p>
                  <p className="text-sm text-gray-500">
                    Use clauses from the Browse tab to see them here.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="organize" className="space-y-4">
          <ClauseCategorizationSystem
            onCategoryCreate={(category) => {
              console.log("Category created:", category);
            }}
            onCategoryUpdate={(category) => {
              console.log("Category updated:", category);
            }}
            onCategoryDelete={(categoryId) => {
              console.log("Category deleted:", categoryId);
            }}
            onTagCreate={(tag) => {
              console.log("Tag created:", tag);
            }}
            onTagUpdate={(tag) => {
              console.log("Tag updated:", tag);
            }}
            onTagDelete={(tagId) => {
              console.log("Tag deleted:", tagId);
            }}
          />
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-4">
          <SmartClauseSuggestions
            contractContext={contractContext}
            onSelectClause={(clause) => {
              console.log("Clause selected:", clause);
              // Create a complete Clause object with all required properties
              const completeClause: Clause = {
                id: clause.id,
                title: clause.title,
                content: clause.content,
                category: clause.category,
                tags: clause.tags,
                lastUpdated: new Date().toISOString().split('T')[0],
                version: "1.0",
                approved: true,
                author: "AI Assistant"
              };
              onClauseSelect(completeClause);
            }}
          />
        </TabsContent>
      </Tabs>

      {/* View Clause Details Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[700px] border-gray-200">
          <DialogHeader>
            <div className="flex justify-between items-center">
              <div>
                <DialogTitle className="font-medium">{selectedClause?.title}</DialogTitle>
                <DialogDescription className="text-gray-500 mt-1">
                  {selectedClause?.category} clause • v{selectedClause?.version}
                </DialogDescription>
              </div>
              {selectedClause && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleToggleFavorite(selectedClause.id)}
                  className={selectedClause.isFavorite ? "text-amber-500" : ""}
                >
                  <Star className={`h-5 w-5 ${selectedClause.isFavorite ? "fill-current" : ""}`} />
                </Button>
              )}
            </div>
          </DialogHeader>
          <div className="py-4">
            <div className="flex flex-wrap gap-1 mb-4">
              {selectedClause?.tags.map((tag) => (
                <Badge key={tag} className={getTagColorClass(tag)}>
                  {tag}
                </Badge>
              ))}
            </div>
            <div className="border border-gray-200 rounded-md p-4 mb-4 text-gray-700 whitespace-pre-wrap">
              {selectedClause?.content}
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>Author: {selectedClause?.author}</span>
              <span>Last updated: {selectedClause?.lastUpdated}</span>
            </div>
          </div>
          <DialogFooter className="flex justify-between items-center">
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)} className="border-gray-300">
              Close
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="border-gray-300"
                onClick={() => {
                  if (selectedClause) {
                    handleUseClause(selectedClause);
                    setIsViewDialogOpen(false);
                  }
                }}
              >
                <Copy className="h-4 w-4 mr-2" /> Use Clause
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Clause Dialog */}
      <Dialog open={isNewClauseDialogOpen} onOpenChange={setIsNewClauseDialogOpen}>
        <DialogContent className="sm:max-w-[600px] border-gray-200">
          <DialogHeader>
            <DialogTitle className="font-medium">Create New Clause</DialogTitle>
            <DialogDescription className="text-gray-500">
              Add a new clause to your library. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="clause-title" className="text-sm font-medium">
                Title
              </label>
              <Input
                id="clause-title"
                value={newClause.title}
                onChange={(e) => setNewClause({ ...newClause, title: e.target.value })}
                placeholder="e.g., Standard Confidentiality Clause"
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="clause-content" className="text-sm font-medium">
                Content
              </label>
              <Textarea
                id="clause-content"
                value={newClause.content}
                onChange={(e) => setNewClause({ ...newClause, content: e.target.value })}
                placeholder="Enter the clause text here..."
                rows={6}
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="clause-category" className="text-sm font-medium">
                Category
              </label>
              <Select
                value={newClause.category}
                onValueChange={(value) => setNewClause({ ...newClause, category: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.filter(c => c !== 'all').map((category) => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <label className="text-sm font-medium">Tags</label>
              <div className="flex flex-wrap gap-2">
                {availableTags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className={`cursor-pointer border ${
                      newClause.tags?.includes(tag)
                        ? "border-gray-400 bg-gray-50 text-gray-800"
                        : "border-gray-200 bg-transparent text-gray-600"
                    }`}
                    onClick={() => toggleNewClauseTag(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="clause-approved"
                checked={newClause.approved}
                onCheckedChange={(checked) =>
                  setNewClause({ ...newClause, approved: checked as boolean })
                }
              />
              <label
                htmlFor="clause-approved"
                className="text-sm font-medium leading-none"
              >
                Mark as approved
              </label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsNewClauseDialogOpen(false)} className="border-gray-300">
              Cancel
            </Button>
            <Button onClick={handleCreateClause} variant="outline" className="bg-gray-900 text-white hover:bg-gray-800">Save Clause</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ModernClauseLibrary;