// Function to generate contract content based on data
export function generateContractContent(data: any) {
  const underscore = (v: any, fallback = '__________') => (v ? v : fallback);

  // Helper to get the right party display name and info based on type
  const getPartyDisplay = (party: any, idx: number) => {
    if (!party) return `Party ${String.fromCharCode(65 + idx)}`;

    const partyName = underscore(party.name, `Party ${String.fromCharCode(65 + idx)}`);
    const partyRole = party.role ? ` (${party.role})` : '';

    let partyDetails = '';
    switch (party.type) {
      case 'individual':
        partyDetails = `an individual having an address at [${underscore(party.address, 'address')}]`;
        break;
      case 'company':
        partyDetails = `a [business entity] having its principal place of business at [${underscore(party.address, 'address')}]`;
        if (party.registration) {
          partyDetails += `, with company registration number ${party.registration}`;
        }
        if (party.vat) {
          partyDetails += `, VAT number ${party.vat}`;
        }
        break;
      case 'government':
        partyDetails = `a governmental entity having its principal place of business at [${underscore(party.address, 'address')}]`;
        if (party.division) {
          partyDetails += `, division: ${party.division}`;
        }
        break;
      case 'nonprofit':
        partyDetails = `a non-profit organization having its principal place of business at [${underscore(party.address, 'address')}]`;
        if (party.taxExempt) {
          partyDetails += `, tax-exempt status: ${party.taxExempt}`;
        }
        break;
      default:
        partyDetails = `having an address at [${underscore(party.address, 'address')}]`;
    }

    return `<strong>[${partyName}]</strong>, ${partyDetails} ("Party ${String.fromCharCode(65 + idx)}"${partyRole})`;
  };

  // Get relevant industry-specific sections if they exist
  const getIndustrySpecificSections = () => {
    if (!data.contractType || !data.industry) return '';

    // Add industry-specific content here
    const industryContentMap: {[key: string]: {[key: string]: string}} = {
      'Technology': {
        'service': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">Technical Requirements</span><br/>
            <span style="margin-left:1.5em;">
              ${underscore(data.technicalRequirements || data.maintenanceSupport || data.serviceLevel || 'N/A')}
            </span>
          </li>
        `,
        'nda': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">Source Code Access</span><br/>
            <span style="margin-left:1.5em;">
              ${underscore(data.sourceCodeAccess ? 'Permitted under this agreement' : 'Not permitted under this agreement')}
            </span>
          </li>
        `
      },
      'Healthcare': {
        'service': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">HIPAA Compliance</span><br/>
            <span style="margin-left:1.5em;">
              ${underscore(data.hipaaCompliance ? 'Compliance with HIPAA regulations is required.' : 'HIPAA compliance is not applicable to this agreement.')}
            </span>
          </li>
        `,
        'nda': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">Patient Data Access</span><br/>
            <span style="margin-left:1.5em;">
              Access level: ${underscore(data.patientDataAccess || 'None')}
            </span>
          </li>
        `
      }
    };

    // Get content for the current industry and contract type
    return (industryContentMap[data.industry] && industryContentMap[data.industry][data.contractType]) || '';
  };

  return `
    <!-- Document Header/Letterhead -->
    <div style="text-align: center; padding: 0.75in 0 0.5in 0; border-bottom: 2px solid #1f2937; margin-bottom: 1.5em; position: relative;">
      <div style="position: absolute; top: 0; left: 0; right: 0; height: 0.75in; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-bottom: 1px solid #e2e8f0; z-index: -1;"></div>
      <h1 style="font-family: 'Times New Roman', serif; font-size: 18pt; font-weight: bold; text-transform: uppercase; letter-spacing: 0.1em; margin-bottom: 0.5em; color: #1f2937;">${underscore(data.title, 'Contract Title')}</h1>
      <div style="font-size: 14pt; font-weight: 500; color: #4b5563; margin-bottom: 0.25em;">${underscore(data.contractType, 'Agreement')}</div>
      <div style="font-size: 11pt; color: #6b7280; font-style: italic;">
        Contract No.: CONT-${Math.floor(10000 + Math.random() * 90000)}<br/>
        Effective Date: ${underscore(data.effectiveDate, new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }))}
      </div>
    </div>

    <!-- Page Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 10pt; color: #6b7280; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.125in; margin-bottom: 1.5em;">
      <span>${underscore(data.title, 'Contract Title')}</span>
      <span>Page 1</span>
    </div>
      <!-- Parties Section -->
      <div style="margin-bottom: 2em;">
        <p style="font-weight: 600; margin-bottom: 1.2em; text-align: justify;">
          THIS AGREEMENT is made and entered into as of <strong>${underscore(data.effectiveDate, 'the Effective Date')}</strong>, by and between the following parties:
        </p>

        <div style="display: flex; flex-direction: column; gap: 1.5em; margin-bottom: 1.5em;">
          ${
            Array.isArray(data.parties) && data.parties.length
              ? data.parties.map((party: any, idx: number) => `
                  <div style="border: 1px solid #d1d5db; padding: 1em 1.25em; background-color: #f9fafb; box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);">
                    <div style="font-weight: bold; text-transform: uppercase; font-size: 11pt; letter-spacing: 0.05em; margin-bottom: 0.5em; padding-bottom: 0.25em; border-bottom: 1px solid #d1d5db; color: #1f2937;">
                      ${idx === 0 ? 'FIRST PARTY' : 'SECOND PARTY'}:
                    </div>
                    <div style="padding-left: 1em; font-size: 12pt; line-height: 1.6;">
                      ${getPartyDisplay(party, idx)}
                    </div>
                  </div>
                  ${idx === 0 && data.parties.length > 1 ? '<div style="text-align: center; font-weight: 600; margin: 0.5em 0; font-size: 14pt;">AND</div>' : ''}
                `).join('')
              : ''
          }
        </div>

        <p style="margin: 1.2em 0 0.7em 0; text-align: justify; font-style: italic;">
          ${data.parties && data.parties.length > 1
            ? `The First Party and Second Party may be referred to individually as a "Party" and collectively as the "Parties" in this Agreement.`
            : `The First Party shall be referred to as the "Party" in this Agreement.`}
        </p>
      </div>
      <!-- Recitals Section -->
      <div class="contract-section-header">1. RECITALS</div>
      <div class="contract-recitals">
        <p style="margin-bottom: 0.8em;"><strong>WHEREAS,</strong> ${underscore(data.description, '[Contract Description]')};</p>
        <p style="margin-bottom: 0.8em;"><strong>WHEREAS,</strong> the Parties wish to enter into this Agreement to define their respective rights and obligations;</p>
        <p style="margin-bottom: 0.8em;"><strong>NOW, THEREFORE,</strong> in consideration of the mutual covenants and agreements contained herein, and other good and valuable consideration, the receipt and sufficiency of which is hereby acknowledged, the Parties agree as follows:</p>
      </div>
      <!-- Terms and Conditions Section -->
      <div class="contract-section-header">2. TERMS AND CONDITIONS</div>
      <ol class="contract-list" style="padding-left: 2.2em; margin-bottom: 0;">
        <li style="margin-bottom: 2em; line-height: 1.7;">
          <div class="contract-subsection-header">2.1 Term and Termination</div>
          <div class="contract-clause">
            <p style="margin-bottom: 0.8em;">This Agreement shall commence on the <strong>Effective Date</strong> of ${underscore(data.effectiveDate, '[date]')} and continue for a period of <strong>${underscore(data.duration, '[duration]')}</strong>, unless earlier terminated in accordance with the provisions set forth herein.</p>
            <p style="margin-bottom: 0.8em;">Either Party may terminate this Agreement for cause in the event of a material breach by the other Party, if such breach remains uncured for thirty (30) days following written notice to the breaching Party.</p>
          </div>
        </li>

        <li style="margin-bottom: 2em; line-height: 1.7;">
          <div class="contract-subsection-header">2.2 Payment & Deliverables</div>

          <table class="contract-table" style="margin: 1em 0;">
            <thead>
              <tr>
                <th style="width: 30%;">Item</th>
                <th style="width: 70%;">Details</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>Contract Value</strong></td>
                <td>${underscore(data.contractValue, '[amount]')} ${underscore(data.currency, '[currency]')}</td>
              </tr>
              <tr>
                <td><strong>Payment Terms</strong></td>
                <td>${underscore(data.paymentTerms, '[payment terms]')}</td>
              </tr>
              <tr>
                <td><strong>Payment Method</strong></td>
                <td>${underscore(data.paymentMethod, '[payment method]')}</td>
              </tr>
            </tbody>
          </table>

          <div class="contract-clause">
            <div class="contract-clause-number">Deliverables:</div>
            <ul class="contract-list">
              ${(data.deliverables && data.deliverables.length)
                ? data.deliverables.map((d: string) => `<li style='margin-bottom: 0.5em; text-align: justify;'>${d}</li>`).join('')
                : '<li>No deliverables specified.</li>'
              }
            </ul>
          </div>
        </li>

        <!-- Industry-specific sections -->
        ${getIndustrySpecificSections()}

        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.3 Legal Provisions</div>
          <div style="margin-left:1.5em;">
            ${(() => {
              const clauseMap = {
                confidentiality: {
                  title: 'Confidentiality',
                  content: 'Each Party shall maintain the confidentiality of all Confidential Information disclosed to it by the other Party and shall not use such Confidential Information for any purpose other than as expressly permitted under this Agreement. "Confidential Information" means any non-public information that is designated as confidential or that, given the nature of the information or circumstances surrounding its disclosure, reasonably should be considered as confidential.'
                },
                limitation_of_liability: {
                  title: 'Limitation of Liability',
                  content: 'IN NO EVENT SHALL EITHER PARTY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, ARISING OUT OF OR RELATING TO THIS AGREEMENT, WHETHER BASED ON CONTRACT, TORT, NEGLIGENCE, STRICT LIABILITY OR OTHERWISE, EVEN IF SUCH PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.'
                },
                termination: {
                  title: 'Termination for Convenience',
                  content: 'Either Party may terminate this Agreement at any time without cause upon thirty (30) days\' prior written notice to the other Party. In the event of such termination, Client shall pay Contractor for all services performed and expenses incurred up to the effective date of termination.'
                },
                force_majeure: {
                  title: 'Force Majeure',
                  content: 'Neither Party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including but not limited to acts of God, natural disasters, terrorism, riots, or war. The affected Party shall promptly notify the other Party of the force majeure event and use reasonable efforts to resume performance as soon as possible.'
                },
                dispute_resolution: {
                  title: 'Dispute Resolution',
                  content: 'Any dispute arising out of or relating to this Agreement shall first be attempted to be resolved through good faith negotiation between the Parties. If such negotiation fails, the dispute shall be resolved by binding arbitration in accordance with the rules of the American Arbitration Association, and judgment upon the award may be entered in any court having jurisdiction.'
                },
                intellectual_property: {
                  title: 'Intellectual Property Rights',
                  content: 'All intellectual property rights, including patents, trademarks, copyrights, and trade secrets, in and to the deliverables shall be owned exclusively by the Client upon full payment of all fees due under this Agreement. Contractor hereby assigns all right, title, and interest in such intellectual property to Client.'
                },
                nonCompete: {
                  title: 'Non-Competition',
                  content: 'During the term of this Agreement and for a period of one (1) year thereafter, each Party agrees not to engage in any business activity that directly competes with the other Party in relation to the specific subject matter of this Agreement within the geographical area where the other Party conducts business.'
                },
                nonSolicitation: {
                  title: 'Non-Solicitation',
                  content: 'During the term of this Agreement and for a period of one (1) year thereafter, neither Party shall directly or indirectly solicit, hire, or attempt to solicit any employees, contractors, or clients of the other Party without the prior written consent of the other Party.'
                },
                returnOfInfo: {
                  title: 'Return of Information',
                  content: 'Upon termination of this Agreement for any reason, each Party shall promptly return to the other Party all Confidential Information, including all copies, notes, and derivatives thereof, and shall cease all further use of such Confidential Information. Upon request, each Party shall certify in writing that it has complied with this provision.'
                },
                jurisdictionSpecific: {
                  title: 'Governing Law',
                  content: 'This Agreement shall be governed by and construed in accordance with the laws of [Jurisdiction], without giving effect to any choice of law or conflict of law provisions. The Parties consent to the exclusive jurisdiction and venue in the courts of [Jurisdiction] for any disputes arising out of or relating to this Agreement.'
                }
              };

              const clauseItems: string[] = [];
              let clauseNum = 1;

              if (Array.isArray(data.standardClauses) && data.standardClauses.length > 0) {
                data.standardClauses.forEach((id: string) => {
                  const clause = clauseMap[id as keyof typeof clauseMap] || { title: id, content: '' };
                  clauseItems.push(`
                    <div style="margin-bottom:1.5em; border-left:3px solid #e5e7eb; padding-left:1em;">
                      <div style="font-weight:600; margin-bottom:0.5em; color:#1e293b;">2.3.${clauseNum} ${clause.title}</div>
                      <div style="text-align:justify; line-height:1.6;">${clause.content}</div>
                    </div>
                  `);
                  clauseNum++;
                });
              }

              if (Array.isArray(data.libraryClauses) && data.libraryClauses.length > 0) {
                data.libraryClauses.forEach((clause: any) => {
                  clauseItems.push(`
                    <div style="margin-bottom:1.5em; border-left:3px solid #e5e7eb; padding-left:1em;">
                      <div style="font-weight:600; margin-bottom:0.5em; color:#1e293b;">2.3.${clauseNum} ${clause.title}</div>
                      <div style="text-align:justify; line-height:1.6;">${clause.content}</div>
                    </div>
                  `);
                  clauseNum++;
                });
              }

              if (Array.isArray(data.customClauses) && data.customClauses.length > 0) {
                data.customClauses.forEach((c: any) => {
                  const title = typeof c === 'string' ? `Custom Clause ${clauseNum}` : c.title || `Custom Clause ${clauseNum}`;
                  const content = typeof c === 'string' ? c : c.content || '';
                  clauseItems.push(`
                    <div style="margin-bottom:1.5em; border-left:3px solid #e5e7eb; padding-left:1em;">
                      <div style="font-weight:600; margin-bottom:0.5em; color:#1e293b;">2.3.${clauseNum} ${title}</div>
                      <div style="text-align:justify; line-height:1.6;">${content}</div>
                    </div>
                  `);
                  clauseNum++;
                });
              }

              if (clauseItems.length === 0) {
                return '<div style="font-style:italic; color:#6b7280; margin:1em 0;">No legal clauses have been selected for this contract.</div>';
              }

              return clauseItems.join('');
            })()}
          </div>
        </li>
        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.4 Attachments</div>
          <div style="margin-left:1.5em;">
            ${
              (data.attachments && data.attachments.length)
              ? `
                <div style="margin-bottom:1em;">The following documents are attached to and form an integral part of this Agreement:</div>
                <div style="border:1px solid #e5e7eb; border-radius:0.25em; overflow:hidden;">
                  <table style="width:100%; border-collapse:collapse; text-align:left;">
                    <thead>
                      <tr style="background-color:#f8fafc; border-bottom:1px solid #e5e7eb;">
                        <th style="padding:0.75em; font-weight:600;">Attachment Name</th>
                        <th style="padding:0.75em; font-weight:600;">Type</th>
                        <th style="padding:0.75em; font-weight:600;">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${data.attachments.map((a: any) => `
                        <tr style="border-bottom:1px solid #e5e7eb;">
                          <td style="padding:0.75em;">${a.name || 'Untitled'}</td>
                          <td style="padding:0.75em;">${a.type || 'Document'}</td>
                          <td style="padding:0.75em;">${a.description || 'No description provided'}</td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                </div>
              `
              : '<div style="font-style:italic; color:#6b7280;">No attachments have been added to this contract.</div>'
            }
          </div>
        </li>

        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.5 Approval Workflow</div>
          <div style="margin-left:1.5em;">
            <div style="margin-bottom:1em;">This Agreement requires approval from the following individuals:</div>

            ${
              (data.approvers && data.approvers.length)
              ? `
                <div style="border:1px solid #e5e7eb; border-radius:0.25em; overflow:hidden; margin-bottom:1.5em;">
                  <table style="width:100%; border-collapse:collapse; text-align:left;">
                    <thead>
                      <tr style="background-color:#f8fafc; border-bottom:1px solid #e5e7eb;">
                        <th style="padding:0.75em; font-weight:600;">Name</th>
                        <th style="padding:0.75em; font-weight:600;">Role</th>
                        <th style="padding:0.75em; font-weight:600;">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${data.approvers.map((a: any) => `
                        <tr style="border-bottom:1px solid #e5e7eb;">
                          <td style="padding:0.75em;">${a.name || 'Unnamed Approver'}</td>
                          <td style="padding:0.75em;">${a.role || 'Reviewer'}</td>
                          <td style="padding:0.75em;">Pending</td>
                        </tr>
                      `).join('')}
                    </tbody>
                  </table>
                </div>
              `
              : '<div style="font-style:italic; color:#6b7280; margin-bottom:1.5em;">No approvers have been designated for this contract.</div>'
            }

            <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
              <div style="font-weight:600;">Approval Process:</div>
              <div>${underscore(data.approvalProcess, 'Sequential approval process')}</div>
            </div>
          </div>
        </li>
      </ol>
    </div>
      <!-- Signature Section -->
      <div class="contract-signature-section">
        <div class="contract-witness-section">IN WITNESS WHEREOF</div>
        <p style="margin: 2em 0; text-align: justify; line-height: 1.6;">
          The Parties hereto have executed this Agreement as of the Effective Date first written above, by their duly authorized representatives.
        </p>

        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 3em; gap: 3em;">
          ${Array.isArray(data.parties) && data.parties.map((party: any, idx: number) => {
            const representativeTitle = party.title || (party.type === 'individual' ? '' : 'Authorized Representative');
            return `
              <div class="contract-signature-block" style="flex: 1; min-width: 280px;">
                <div class="contract-party-label" style="margin-bottom: 1em;">
                  ${idx === 0 ? 'FIRST PARTY' : 'SECOND PARTY'}
                </div>

                <div class="signature-line"></div>

                <table style="width: 100%; margin-top: 2em; border-collapse: collapse;">
                  <tr>
                    <td style="font-weight: 600; padding: 0.5em 0; width: 30%;">Name:</td>
                    <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">${underscore(party.name, '[Full Name]')}</td>
                  </tr>
                  ${party.representative ? `
                    <tr>
                      <td style="font-weight: 600; padding: 0.5em 0;">By:</td>
                      <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">${underscore(party.representative, '[Representative Name]')}</td>
                    </tr>
                  ` : ''}
                  ${representativeTitle ? `
                    <tr>
                      <td style="font-weight: 600; padding: 0.5em 0;">Title:</td>
                      <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">${underscore(representativeTitle, '[Title]')}</td>
                    </tr>
                  ` : ''}
                  <tr>
                    <td style="font-weight: 600; padding: 0.5em 0;">Date:</td>
                    <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">____________________</td>
                  </tr>
                </table>
              </div>
            `;
          }).join('')}
        </div>
      </div>

      <!-- Page Footer -->
      <div class="contract-page-footer">
        <span>Contract executed on ${underscore(data.effectiveDate, new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }))}</span>
      </div>

      <!-- Optional Watermark -->
      ${data.status === 'draft' ? '<div class="contract-watermark">DRAFT</div>' : ''}
    </div>
  `;
}
