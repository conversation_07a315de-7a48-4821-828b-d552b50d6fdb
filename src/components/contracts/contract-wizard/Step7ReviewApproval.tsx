import React, { useRef, useState, useEffect } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { useNavigate } from 'react-router-dom';
import html2pdf from 'html2pdf.js';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ChevronLeft, ChevronRight, FileText, Send, Download, Check, Clock, Users, Loader2, AlertCircle, Save } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useApi } from '@/lib/api';
import { UserService } from '@/services/api-services';
import type { User as ApiUser } from '@/services/api-types';
import SaveAsTemplateModal from './SaveAsTemplateModal';

interface Approver {
  id: string;
  name: string;
  email: string;
  role: string;
}

const Step7ReviewApproval: React.FC = () => {
  const navigate = useNavigate();
  const { fetch } = useApi();
  const { data, setData, prevStep, clearDraft, saveContract, isLoading, error } = useContractWizard();

  // State for workspace users
  const [systemApprovers, setSystemApprovers] = useState<Approver[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [showExported, setShowExported] = useState(false);
  const [showSubmitted, setShowSubmitted] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [selectedApprovers, setSelectedApprovers] = useState<string[]>([]);
  const [approvalProcess, setApprovalProcess] = useState<'sequential' | 'parallel'>(data.approvalProcess || 'sequential');
  const [saveAsTemplateOpen, setSaveAsTemplateOpen] = useState(false);
  const contractRef = useRef<HTMLDivElement>(null);

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      setLoadingUsers(true);

      try {
        const result = await fetch(
          () => UserService.getCurrentUser(),
          "Loading users...",
          "Failed to load users"
        );

        if (result) {
          // For now, just use the current user as an approver
          // In a real app, you would fetch all workspace users with appropriate permissions
          const currentUser: Approver = {
            id: result.id,
            name: `${result.first_name} ${result.last_name}`,
            email: result.email,
            role: 'Administrator'
          };

          // Add some mock users for demo purposes
          const mockUsers: Approver[] = [
            { id: 'user-2', name: 'Jane Doe', email: '<EMAIL>', role: 'Legal Reviewer' },
            { id: 'user-3', name: 'Alice Johnson', email: '<EMAIL>', role: 'Contract Manager' },
          ];

          setSystemApprovers([currentUser, ...mockUsers]);
        }
      } catch (err) {
        console.error("Error fetching users:", err);
      } finally {
        setLoadingUsers(false);
      }
    };

    fetchUsers();
  }, [fetch]);

  // Initialize approvers from data or empty array
  useEffect(() => {
    if (data.approvers && data.approvers.length > 0 && systemApprovers.length > 0) {
      // Extract IDs from existing approvers if they match system approvers
      const approverIds = data.approvers
        .map((approver: any) => {
          const matchingSystemApprover = systemApprovers.find(
            sa => sa.name === approver.name || sa.email === approver.email
          );
          return matchingSystemApprover?.id;
        })
        .filter(Boolean) as string[];

      setSelectedApprovers(approverIds);
    }

    if (data.approvalProcess) {
      setApprovalProcess(data.approvalProcess);
    }
  }, [data.approvers, data.approvalProcess, systemApprovers]);

  // Update contract data when approvers change
  useEffect(() => {
    const approvers = selectedApprovers.map(id => {
      const approver = systemApprovers.find(a => a.id === id);
      return {
        id: approver?.id,
        name: approver?.name || '',
        role: approver?.role || '',
        email: approver?.email || ''
      };
    });

    setData(d => ({
      ...d,
      approvers,
      approvalProcess
    }));
  }, [selectedApprovers, approvalProcess, setData]);

  const handleExport = async (type: 'pdf' | 'txt') => {
    setExporting(true);
    setDropdownOpen(false);
    if (type === 'pdf' && contractRef.current) {
      await html2pdf().from(contractRef.current).save(`${data.title || 'contract'}.pdf`);
    } else if (type === 'txt') {
      const text = contractRef.current?.innerText || '';
      const blob = new Blob([text], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${data.title || 'contract'}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }
    setExporting(false);
    setShowExported(true);
    setTimeout(() => setShowExported(false), 2000);
  };

  const handleSubmitForApproval = async () => {
    try {
      // Save the contract to the API
      const contractId = await saveContract();

      if (contractId) {
        setShowSubmitted(true);
        clearDraft();

        // Navigate to the contract details page after a short delay
        setTimeout(() => {
          navigate(`/contracts/${contractId}`);
        }, 2000);
      }
    } catch (err) {
      console.error("Error submitting contract:", err);
    }
  };

  const toggleApprover = (approverId: string) => {
    setSelectedApprovers(current =>
      current.includes(approverId)
        ? current.filter(id => id !== approverId)
        : [...current, approverId]
    );
  };

  const isValid = selectedApprovers.length > 0;

  // Show loading state while fetching users
  if (loadingUsers) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
        <h3 className="text-lg font-medium">Loading users...</h3>
        <p className="text-muted-foreground mt-2">
          Please wait while we fetch the available approvers
        </p>
      </div>
    );
  }

  // Show error state if there was an error in the contract wizard
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error</h3>
        <p className="text-muted-foreground mt-2">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={prevStep}
        >
          <ChevronLeft className="mr-1 h-3 w-3" />
          Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex flex-col">
        {/* Contract Summary */}
        <div className="mb-3">
          <Label className="text-sm font-medium mb-1 block">Contract Summary</Label>
          <Card className="shadow-sm">
            <CardContent className="p-3">
              <div ref={contractRef} className="text-xs space-y-1.5">
                <div className="grid grid-cols-2 gap-2">
                  <div><span className="font-medium">Contract Type:</span> {data.contractType}</div>
                  <div><span className="font-medium">Jurisdiction:</span> {data.jurisdiction}</div>
                  <div><span className="font-medium">Title:</span> {data.title}</div>
                  <div><span className="font-medium">Effective Date:</span> {data.effectiveDate}</div>
                </div>

                <Separator className="my-2" />

                <div><span className="font-medium">Parties:</span></div>
                <ul className="ml-4 list-disc">
                  {Array.isArray(data.parties) && data.parties.map((party, idx) => (
                    <li key={idx}>{party.name} {party.role && `(${party.role})`}</li>
                  ))}
                </ul>

                <Separator className="my-2" />

                <div className="grid grid-cols-2 gap-2">
                  <div><span className="font-medium">Contract Value:</span> {data.contractValue} {data.currency}</div>
                  <div><span className="font-medium">Payment Terms:</span> {data.paymentTerms}</div>
                </div>

                <Separator className="my-2" />

                <div><span className="font-medium">Attachments:</span> {data.attachments && data.attachments.length
                  ? data.attachments.map((a: any) => a.name).join(', ')
                  : 'None'}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Approvers Selection */}
        <div className="mb-3">
          <Label className="text-sm font-medium mb-1 block">Select Approvers <span className="text-destructive">*</span></Label>
          <Card className="shadow-sm">
            <CardContent className="p-3">
              <div className="space-y-2 max-h-[200px] overflow-y-auto pr-1">
                {systemApprovers.map((approver) => (
                  <div key={approver.id} className="flex items-center space-x-2 py-1 border-b border-slate-100 dark:border-slate-800 last:border-0">
                    <Checkbox
                      id={`approver-${approver.id}`}
                      checked={selectedApprovers.includes(approver.id)}
                      onCheckedChange={() => toggleApprover(approver.id)}
                    />
                    <div className="grid grid-cols-[1fr_auto] w-full gap-2 items-center">
                      <label
                        htmlFor={`approver-${approver.id}`}
                        className="text-xs font-medium cursor-pointer"
                      >
                        {approver.name}
                        <span className="block text-xs text-slate-500 dark:text-slate-400">{approver.email}</span>
                      </label>
                      <span className="text-xs bg-slate-100 dark:bg-slate-800 px-2 py-0.5 rounded-full">
                        {approver.role}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {selectedApprovers.length === 0 && (
                <div className="text-xs text-destructive mt-1">At least one approver is required.</div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Approval Process */}
        <div className="mb-3">
          <Label className="text-sm font-medium mb-1 block">Approval Process</Label>
          <Card className="shadow-sm">
            <CardContent className="p-3">
              <RadioGroup
                value={approvalProcess}
                onValueChange={(value) => setApprovalProcess(value as 'sequential' | 'parallel')}
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="sequential" id="sequential" />
                  <label htmlFor="sequential" className="text-xs flex items-center gap-1.5 cursor-pointer">
                    <ChevronRight className="h-3 w-3" />
                    Sequential
                    <span className="text-slate-500 dark:text-slate-400">(One after another)</span>
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="parallel" id="parallel" />
                  <label htmlFor="parallel" className="text-xs flex items-center gap-1.5 cursor-pointer">
                    <Users className="h-3 w-3" />
                    Parallel
                    <span className="text-slate-500 dark:text-slate-400">(All at once)</span>
                  </label>
                </div>
              </RadioGroup>

              {selectedApprovers.length > 0 && approvalProcess === 'sequential' && (
                <div className="mt-2 border-t border-slate-100 dark:border-slate-800 pt-2">
                  <div className="text-xs font-medium mb-1">Approval Order:</div>
                  <div className="space-y-1">
                    {selectedApprovers.map((id, index) => {
                      const approver = systemApprovers.find(a => a.id === id);
                      return (
                        <div key={id} className="flex items-center gap-2">
                          <div className="bg-slate-100 dark:bg-slate-800 rounded-full w-4 h-4 flex items-center justify-center text-[10px]">
                            {index + 1}
                          </div>
                          <span className="text-xs">{approver?.name}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="pt-4 pb-3 mt-4 flex justify-between gap-2 sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent z-10">
        <Button
          type="button"
          variant="outline"
          className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
          onClick={prevStep}
        >
          <ChevronLeft className="mr-1 h-3 w-3" />
          Back
        </Button>

        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
            onClick={() => setSaveAsTemplateOpen(true)}
          >
            <Save className="mr-1.5 h-3 w-3" />
            Save as Template
          </Button>

          <div className="relative">
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
              onClick={() => setDropdownOpen((v) => !v)}
              aria-haspopup="listbox"
              aria-expanded={dropdownOpen}
              disabled={exporting}
            >
              <Download className="mr-1.5 h-3 w-3" />
              Export
              <ChevronRight className="ml-1 h-3 w-3" />
            </Button>
            {dropdownOpen && !exporting && (
              <div className="absolute right-0 mt-1 w-32 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded shadow-lg z-10">
                <Button
                  variant="ghost"
                  className="w-full text-left px-2 py-1.5 justify-start text-xs h-auto"
                  onClick={() => handleExport('pdf')}
                >
                  Export as PDF
                </Button>
                <Button
                  variant="ghost"
                  className="w-full text-left px-2 py-1.5 justify-start text-xs h-auto"
                  onClick={() => handleExport('txt')}
                >
                  Export as TXT
                </Button>
              </div>
            )}
          </div>

          <Button
            type="button"
            className={cn(
              "flex items-center h-7 px-3 shadow-sm transition-all hover:shadow text-xs",
              !isValid && "opacity-50 cursor-not-allowed"
            )}
            disabled={!isValid || showSubmitted || isLoading}
            onClick={handleSubmitForApproval}
          >
            {showSubmitted ? (
              <>
                <Check className="mr-1.5 h-3 w-3" />
                Submitted
              </>
            ) : isLoading ? (
              <>
                <Loader2 className="mr-1.5 h-3 w-3 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Send className="mr-1.5 h-3 w-3" />
                Submit for Approval
              </>
            )}
          </Button>
        </div>
      </div>

      {showExported && (
        <Alert className="mt-2 py-1.5 dark:bg-slate-800 dark:border-slate-700">
          <AlertDescription className="text-xs">Contract exported successfully!</AlertDescription>
        </Alert>
      )}

      {showSubmitted && (
        <Alert className="mt-2 py-1.5 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400">
          <AlertDescription className="text-xs flex items-center">
            <Check className="h-3 w-3 mr-1" />
            Contract saved successfully! Redirecting to contract details...
          </AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert className="mt-2 py-1.5 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400">
          <AlertDescription className="text-xs flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Save as Template Modal */}
      <SaveAsTemplateModal
        open={saveAsTemplateOpen}
        onOpenChange={setSaveAsTemplateOpen}
      />
    </div>
  );
};

export default Step7ReviewApproval;
