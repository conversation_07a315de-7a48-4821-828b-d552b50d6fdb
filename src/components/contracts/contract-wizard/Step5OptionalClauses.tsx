// This file has been deprecated and is no longer used in the contract wizard.
import React, { useState } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { optionalClausesList } from './optionalClausesList';

const Step5OptionalClauses: React.FC = () => {
  const { data, setData, nextStep, prevStep } = useContractWizard();
  const [customOptionalClause, setCustomOptionalClause] = useState('');
  const [touched, setTouched] = useState(false);

  const isValid =
    (data.optionalClauses && data.optionalClauses.length > 0) ||
    (data.customOptionalClauses && data.customOptionalClauses.length > 0 && data.customOptionalClauses.every((c: string) => c.trim() !== ''));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);
    if (isValid) nextStep();
  };

  const toggleOptionalClause = (id: string) => {
    setData(d => ({
      ...d,
      optionalClauses: d.optionalClauses?.includes(id)
        ? d.optionalClauses.filter((c: string) => c !== id)
        : [...(d.optionalClauses || []), id],
    }));
  };

  const addCustomOptionalClause = () => {
    if (customOptionalClause.trim()) {
      setData(d => ({ ...d, customOptionalClauses: [...(d.customOptionalClauses || []), customOptionalClause.trim()] }));
      setCustomOptionalClause('');
    }
  };

  const removeCustomOptionalClause = (idx: number) => {
    setData(d => ({ ...d, customOptionalClauses: d.customOptionalClauses.filter((_: string, i: number) => i !== idx) }));
  };

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      <div>
        <label className="block text-sm font-medium mb-2">Recommended Optional Clauses</label>
        <div className="space-y-2">
          {optionalClausesList.map(clause => (
            <label key={clause.id} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={data.optionalClauses?.includes(clause.id) || false}
                onChange={() => toggleOptionalClause(clause.id)}
              />
              <span>{clause.label}</span>
            </label>
          ))}
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">Custom Optional Clauses</label>
        <div className="space-y-2">
          {data.customOptionalClauses?.map((clause: string, idx: number) => (
            <div key={idx} className="flex items-center space-x-2">
              <input
                className="flex-1 border rounded px-3 py-2"
                value={clause}
                onChange={e => setData(d => ({ ...d, customOptionalClauses: d.customOptionalClauses.map((c: string, i: number) => i === idx ? e.target.value : c) }))}
              />
              <button type="button" className="text-red-500 px-2" onClick={() => removeCustomOptionalClause(idx)}>&times;</button>
            </div>
          ))}
          <div className="flex items-center space-x-2 mt-2">
            <input
              className="flex-1 border rounded px-3 py-2"
              value={customOptionalClause}
              onChange={e => setCustomOptionalClause(e.target.value)}
              placeholder="Add custom optional clause"
            />
            <button type="button" className="bg-gray-200 text-gray-700 px-3 py-1 rounded hover:bg-gray-300" onClick={addCustomOptionalClause}>Add</button>
          </div>
        </div>
      </div>
      {touched && !isValid && (
        <div className="text-xs text-red-600 mt-1">At least one optional or custom optional clause is required.</div>
      )}
      <div className="pt-4 flex justify-between">
        <Button type="button" variant="outline" size="sm" className="h-8 px-3" onClick={prevStep}>
          Back
        </Button>
        <Button
          type="button"
          size="sm"
          className="flex items-center h-8 px-3"
          onClick={() => {
            setTouched(true);
            if (isValid) nextStep();
            else nextStep(); // allow navigation but show validation if invalid
          }}
        >
          Next
        </Button>
      </div>
    </form>
  );
};

export default Step5OptionalClauses; 