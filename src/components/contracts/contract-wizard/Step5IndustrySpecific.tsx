import React from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import DynamicFieldRenderer from './DynamicFieldRenderer';
import { Textarea } from '@/components/ui/textarea';

const Step5IndustrySpecific: React.FC = () => {
  const { data, setData, nextStep, prevStep, selectedTemplate } = useContractWizard();

  // Get industry-specific fields based on template and industry
  const getIndustrySpecificFields = () => {
    if (!selectedTemplate?.industrySpecificFields || !data.industry) {
      return [];
    }

    return selectedTemplate.industrySpecificFields[data.industry] || [];
  };

  // Regulatory compliance options based on industry
  const getComplianceOptions = () => {
    switch (data.industry) {
      case 'Healthcare':
        return [
          { id: 'hipaa', label: 'HIPAA (Health Insurance Portability and Accountability Act)' },
          { id: 'hitech', label: 'HITECH (Health Information Technology for Economic and Clinical Health Act)' },
          { id: 'fda', label: 'FDA Compliance' },
        ];
      case 'Finance':
        return [
          { id: 'sox', label: 'Sarbanes-Oxley Act' },
          { id: 'aml', label: 'Anti-Money Laundering (AML)' },
          { id: 'gdpr_finance', label: 'GDPR Financial Services Provisions' },
        ];
      case 'Technology':
        return [
          { id: 'gdpr_tech', label: 'GDPR Technology Provisions' },
          { id: 'ccpa', label: 'CCPA (California Consumer Privacy Act)' },
          { id: 'coppa', label: 'COPPA (Children\'s Online Privacy Protection Act)' },
        ];
      case 'Retail':
        return [
          { id: 'pci_dss', label: 'PCI DSS (Payment Card Industry Data Security Standard)' },
          { id: 'consumer_protection', label: 'Consumer Protection Laws' },
        ];
      default:
        return [];
    }
  };

  // Get selected compliance items
  const complianceOptions = getComplianceOptions();
  const industryFields = getIndustrySpecificFields();

  // Check if we have any industry-specific content to show
  const hasIndustryContent =
    (data.industry && (industryFields.length > 0 || complianceOptions.length > 0)) ||
    selectedTemplate?.customSections?.length > 0;

  // Toggle compliance requirement
  const toggleCompliance = (id: string) => {
    const currentCompliance: string[] = data.complianceRequirements || [];

    if (currentCompliance.includes(id)) {
      setData(d => ({
        ...d,
        complianceRequirements: currentCompliance.filter((c: string) => c !== id)
      }));
    } else {
      setData(d => ({
        ...d,
        complianceRequirements: [...currentCompliance, id]
      }));
    }
  };

  // Custom section handling
  const handleCustomSectionChange = (idx: number, content: string) => {
    const currentSections = data.customSectionContent || [];
    const updatedSections = [...currentSections];
    updatedSections[idx] = content;

    setData(d => ({
      ...d,
      customSectionContent: updatedSections
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    nextStep();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {!hasIndustryContent ? (
        <div className="text-center py-3">
          <p className="text-gray-500 dark:text-gray-400 text-sm">No industry-specific content available for this contract type or industry.</p>
          <p className="text-xs text-muted-foreground mt-1">You can still add custom clauses in other sections.</p>
        </div>
      ) : (
        <>
          {/* Industry-specific fields */}
          {industryFields.length > 0 && (
            <div className="space-y-1">
              <Label className="text-sm font-medium">Industry-Specific Information</Label>
              <div className="border border-slate-200 dark:border-slate-700 rounded-md shadow-sm">
                <div className="p-3">
                  <DynamicFieldRenderer fields={industryFields} data={data} setData={setData} />
                </div>
              </div>
            </div>
          )}

          {/* Regulatory compliance requirements */}
          {complianceOptions.length > 0 && (
            <div className="space-y-1 mt-2">
              <Label className="text-sm font-medium">Regulatory Compliance</Label>
              <div className="border border-slate-200 dark:border-slate-700 rounded-md shadow-sm">
                <div className="p-3 space-y-2">
                  {complianceOptions.map(option => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={option.id}
                        checked={(data.complianceRequirements || []).includes(option.id)}
                        onCheckedChange={() => toggleCompliance(option.id)}
                      />
                      <label
                        htmlFor={option.id}
                        className="text-xs dark:text-slate-300"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Custom sections from template */}
          {selectedTemplate?.customSections && selectedTemplate.customSections.length > 0 && (
            <div className="space-y-1 mt-2">
              <Label className="text-sm font-medium">Additional Sections</Label>
              {selectedTemplate.customSections.map((section, idx) => (
                <div key={section} className="border border-slate-200 dark:border-slate-700 rounded-md shadow-sm overflow-hidden">
                  <div className="border-b border-slate-100 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 px-3 py-2">
                    <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">{section}</h3>
                  </div>
                  <div className="p-3">
                    <Textarea
                      placeholder={`Enter content for ${section}...`}
                      value={(data.customSectionContent || [])[idx] || ''}
                      onChange={e => handleCustomSectionChange(idx, e.target.value)}
                      rows={3}
                      className="min-h-[60px] border-slate-200 dark:border-slate-700 text-xs py-1.5 bg-white dark:bg-slate-950"
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}

      <div className="pt-4 pb-3 mt-4 flex justify-between gap-2 sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent z-10">
        <Button type="button" variant="outline" className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs" onClick={prevStep}>
          <ChevronLeft className="mr-1 h-3 w-3" />
          Back
        </Button>
        <Button type="submit" className="flex items-center h-7 px-3 shadow-sm transition-all hover:shadow text-xs">
          Next
          <ChevronRight className="ml-1 h-3 w-3" />
        </Button>
      </div>
    </form>
  );
};

export default Step5IndustrySpecific;