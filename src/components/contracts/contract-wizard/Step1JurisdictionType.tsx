import React from 'react';
import { useContractWizard } from './ContractWizardContext';
import { contractTemplates, industryOptions } from './contractTemplates';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronRight } from 'lucide-react';

const Step1JurisdictionType: React.FC = () => {
  const { data, setData, nextStep, selectTemplateById, selectedTemplate } = useContractWizard();

  const handleJurisdictionChange = (value: string) => {
    setData({ ...data, jurisdiction: value });
  };

  const handleContractTypeChange = (value: string) => {
    setData({ ...data, contractType: value });
    selectTemplateById(value);
  };

  const handleIndustryChange = (value: string) => {
    setData({ ...data, industry: value });
  };

  // Get applicable industries for the selected template
  const getAvailableIndustries = () => {
    if (!selectedTemplate) return industryOptions;

    if (selectedTemplate.industry && selectedTemplate.industry.length > 0) {
      // If template specifies industries, use those
      if (selectedTemplate.industry.includes('All')) {
        return industryOptions;
      }
      return selectedTemplate.industry;
    }

    // Default to all industries
    return industryOptions;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    nextStep();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-3">
        <div className="space-y-2 bg-slate-50/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-100 dark:border-slate-800 mb-4">
          <Label htmlFor="jurisdiction" className="text-sm font-medium flex items-center">
            Jurisdiction
            <span className="text-destructive ml-1">*</span>
          </Label>
          <Select
            value={data.jurisdiction}
            onValueChange={handleJurisdictionChange}
            required
          >
            <SelectTrigger id="jurisdiction" className="w-full h-10 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-800">
              <SelectValue placeholder="Select jurisdiction" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="us">United States</SelectItem>
              <SelectItem value="uk">United Kingdom</SelectItem>
              <SelectItem value="eu">European Union</SelectItem>
              <SelectItem value="ca">Canada</SelectItem>
              <SelectItem value="au">Australia</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">The legal jurisdiction that governs this contract</p>
        </div>

        <div className="space-y-2 bg-slate-50/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-100 dark:border-slate-800 mb-4">
          <Label htmlFor="contractType" className="text-sm font-medium flex items-center">
            Contract Type
            <span className="text-destructive ml-1">*</span>
          </Label>
          <Select
            value={data.contractType}
            onValueChange={handleContractTypeChange}
            required
          >
            <SelectTrigger id="contractType" className="w-full h-10 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-800">
              <SelectValue placeholder="Select contract type" />
            </SelectTrigger>
            <SelectContent>
              {contractTemplates.map(template => (
                <SelectItem key={template.id} value={template.id}>{template.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">The type of contract you want to create</p>
        </div>

        <div className="space-y-2 bg-slate-50/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-100 dark:border-slate-800">
          <Label htmlFor="industry" className="text-sm font-medium flex items-center">
            Industry <span className="text-xs text-slate-400 dark:text-slate-500 ml-1">(optional)</span>
          </Label>
          <Select
            value={data.industry}
            onValueChange={handleIndustryChange}
          >
            <SelectTrigger id="industry" className="w-full h-10 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-800">
              <SelectValue placeholder="Select industry" />
            </SelectTrigger>
            <SelectContent>
              {getAvailableIndustries().map(industry => (
                <SelectItem key={industry} value={industry}>{industry}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">The industry this contract relates to (enables industry-specific clauses)</p>
        </div>
      </div>

      <div className="sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent pt-6 pb-4 mt-8 z-10">
        <div className="flex justify-end">
          <Button
            type="submit"
            className="flex items-center h-9 px-4 rounded-full shadow-sm transition-all hover:shadow"
          >
            Continue
            <ChevronRight className="ml-1.5 h-3.5 w-3.5" />
          </Button>
        </div>
      </div>
    </form>
  );
};

export default Step1JurisdictionType;