import React, { useState, useEffect } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { contractTemplates, ContractTemplate, ContractField } from './contractTemplates';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Minus, 
  Settings, 
  Save, 
  RotateCcw, 
  Eye, 
  EyeOff,
  ChevronDown,
  ChevronRight,
  Copy,
  Trash2
} from 'lucide-react';

interface EnhancedTemplateCustomizerProps {
  onTemplateChange?: (template: ContractTemplate) => void;
}

/**
 * Enhanced Template Customizer with dynamic field management
 * - Real-time template customization
 * - Dynamic field addition/removal
 * - Industry-specific adaptations
 * - Template versioning and saving
 */
const EnhancedTemplateCustomizer: React.FC<EnhancedTemplateCustomizerProps> = ({
  onTemplateChange
}) => {
  const { data, setData, selectedTemplate, setSelectedTemplate } = useContractWizard();
  const [customTemplate, setCustomTemplate] = useState<ContractTemplate | null>(null);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['basic']));
  const [previewMode, setPreviewMode] = useState(false);

  // Initialize custom template from selected template
  useEffect(() => {
    if (selectedTemplate && !customTemplate) {
      setCustomTemplate({ ...selectedTemplate });
    }
  }, [selectedTemplate, customTemplate]);

  // Dynamic template adaptation based on selections
  useEffect(() => {
    if (customTemplate && (data.jurisdiction || data.industry)) {
      const adaptedTemplate = adaptTemplateToContext(customTemplate, data.jurisdiction, data.industry);
      if (JSON.stringify(adaptedTemplate) !== JSON.stringify(customTemplate)) {
        setCustomTemplate(adaptedTemplate);
        onTemplateChange?.(adaptedTemplate);
      }
    }
  }, [data.jurisdiction, data.industry, customTemplate, onTemplateChange]);

  const adaptTemplateToContext = (
    template: ContractTemplate, 
    jurisdiction: string, 
    industry: string
  ): ContractTemplate => {
    const adapted = { ...template };
    
    // Add jurisdiction-specific fields
    if (jurisdiction === 'us') {
      adapted.fields = [...adapted.fields, {
        key: 'stateSpecificClause',
        label: 'State-Specific Provisions',
        type: 'textarea',
        placeholder: 'Enter state-specific legal provisions...'
      }];
    }

    // Add industry-specific fields
    if (industry === 'technology') {
      adapted.fields = [...adapted.fields, {
        key: 'intellectualProperty',
        label: 'Intellectual Property Rights',
        type: 'textarea',
        required: true,
        placeholder: 'Define IP ownership and licensing terms...'
      }];
    }

    return adapted;
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const addCustomField = () => {
    if (!customTemplate) return;

    const newField: ContractField = {
      key: `custom_field_${Date.now()}`,
      label: 'New Custom Field',
      type: 'text',
      required: false,
      placeholder: 'Enter value...'
    };

    const updated = {
      ...customTemplate,
      fields: [...customTemplate.fields, newField]
    };

    setCustomTemplate(updated);
    onTemplateChange?.(updated);
  };

  const updateField = (index: number, field: Partial<ContractField>) => {
    if (!customTemplate) return;

    const updatedFields = [...customTemplate.fields];
    updatedFields[index] = { ...updatedFields[index], ...field };

    const updated = {
      ...customTemplate,
      fields: updatedFields
    };

    setCustomTemplate(updated);
    onTemplateChange?.(updated);
  };

  const removeField = (index: number) => {
    if (!customTemplate) return;

    const updated = {
      ...customTemplate,
      fields: customTemplate.fields.filter((_, i) => i !== index)
    };

    setCustomTemplate(updated);
    onTemplateChange?.(updated);
  };

  const duplicateField = (index: number) => {
    if (!customTemplate) return;

    const fieldToDuplicate = customTemplate.fields[index];
    const duplicated = {
      ...fieldToDuplicate,
      key: `${fieldToDuplicate.key}_copy_${Date.now()}`,
      label: `${fieldToDuplicate.label} (Copy)`
    };

    const updated = {
      ...customTemplate,
      fields: [
        ...customTemplate.fields.slice(0, index + 1),
        duplicated,
        ...customTemplate.fields.slice(index + 1)
      ]
    };

    setCustomTemplate(updated);
    onTemplateChange?.(updated);
  };

  const resetTemplate = () => {
    if (selectedTemplate) {
      setCustomTemplate({ ...selectedTemplate });
      onTemplateChange?.(selectedTemplate);
    }
  };

  const saveAsNewTemplate = () => {
    if (!customTemplate) return;
    
    // In a real implementation, this would save to the backend
    console.log('Saving custom template:', customTemplate);
    
    // For now, just update the selected template
    setSelectedTemplate(customTemplate);
  };

  if (!customTemplate) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Template Customizer</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Select a contract type to begin customizing the template.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">Template Customizer</h3>
          <Badge variant="outline">{customTemplate.label}</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPreviewMode(!previewMode)}
          >
            {previewMode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button variant="outline" size="sm" onClick={resetTemplate}>
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset
          </Button>
          <Button variant="outline" size="sm" onClick={saveAsNewTemplate}>
            <Save className="h-4 w-4 mr-1" />
            Save Template
          </Button>
        </div>
      </div>

      {/* Template Customization Interface */}
      {!previewMode ? (
        <div className="space-y-4">
          {/* Basic Template Info */}
          <Card>
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleSection('basic')}
            >
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Basic Information</CardTitle>
                {expandedSections.has('basic') ? 
                  <ChevronDown className="h-4 w-4" /> : 
                  <ChevronRight className="h-4 w-4" />
                }
              </div>
            </CardHeader>
            {expandedSections.has('basic') && (
              <CardContent className="space-y-4">
                <div>
                  <Label>Template Name</Label>
                  <Input
                    value={customTemplate.label}
                    onChange={(e) => setCustomTemplate({
                      ...customTemplate,
                      label: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label>Description</Label>
                  <Textarea
                    value={customTemplate.description}
                    onChange={(e) => setCustomTemplate({
                      ...customTemplate,
                      description: e.target.value
                    })}
                  />
                </div>
              </CardContent>
            )}
          </Card>

          {/* Custom Fields */}
          <Card>
            <CardHeader 
              className="cursor-pointer"
              onClick={() => toggleSection('fields')}
            >
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Custom Fields ({customTemplate.fields.length})
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      addCustomField();
                    }}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                  {expandedSections.has('fields') ? 
                    <ChevronDown className="h-4 w-4" /> : 
                    <ChevronRight className="h-4 w-4" />
                  }
                </div>
              </div>
            </CardHeader>
            {expandedSections.has('fields') && (
              <CardContent className="space-y-4">
                {customTemplate.fields.map((field, index) => (
                  <div key={field.key} className="border rounded p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{field.type}</Badge>
                        {field.required && <Badge variant="destructive">Required</Badge>}
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => duplicateField(index)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeField(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label>Field Label</Label>
                        <Input
                          value={field.label}
                          onChange={(e) => updateField(index, { label: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label>Field Type</Label>
                        <Select
                          value={field.type}
                          onValueChange={(value) => updateField(index, { type: value as any })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">Text</SelectItem>
                            <SelectItem value="textarea">Textarea</SelectItem>
                            <SelectItem value="date">Date</SelectItem>
                            <SelectItem value="number">Number</SelectItem>
                            <SelectItem value="select">Select</SelectItem>
                            <SelectItem value="checkbox">Checkbox</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label>Placeholder</Label>
                      <Input
                        value={field.placeholder || ''}
                        onChange={(e) => updateField(index, { placeholder: e.target.value })}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={field.required || false}
                        onCheckedChange={(checked) => updateField(index, { required: checked })}
                      />
                      <Label>Required Field</Label>
                    </div>
                  </div>
                ))}
              </CardContent>
            )}
          </Card>
        </div>
      ) : (
        /* Preview Mode */
        <Card>
          <CardHeader>
            <CardTitle>Template Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">{customTemplate.label}</h4>
                <p className="text-sm text-muted-foreground">{customTemplate.description}</p>
              </div>
              <div className="space-y-3">
                {customTemplate.fields.map((field) => (
                  <div key={field.key} className="space-y-1">
                    <Label>
                      {field.label}
                      {field.required && <span className="text-destructive ml-1">*</span>}
                    </Label>
                    {field.type === 'textarea' ? (
                      <Textarea placeholder={field.placeholder} disabled />
                    ) : field.type === 'select' ? (
                      <Select disabled>
                        <SelectTrigger>
                          <SelectValue placeholder={field.placeholder} />
                        </SelectTrigger>
                      </Select>
                    ) : (
                      <Input placeholder={field.placeholder} disabled />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedTemplateCustomizer;
