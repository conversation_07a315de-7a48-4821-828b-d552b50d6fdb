import React, { useEffect } from 'react';
import { ContractField } from './contractTemplates';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

interface DynamicFieldRendererProps {
  fields: ContractField[];
  data: Record<string, any>;
  setData: (updater: (prev: any) => any) => void;
}

const DynamicFieldRenderer: React.FC<DynamicFieldRendererProps> = ({ fields, data, setData }) => {
  // Helper function to determine if a conditional field should be shown
  const shouldShowField = (field: ContractField): boolean => {
    if (!field.conditionalOn) return true;

    const conditionValue = data[field.conditionalOn];

    // If no condition value is set yet, don't show conditional field
    if (conditionValue === undefined || conditionValue === null || conditionValue === '') {
      return false;
    }

    const conditionType = field.conditionalType || 'eq';
    const targetValue = field.conditionalValue;

    switch (conditionType) {
      case 'eq':
        return Array.isArray(targetValue) ? targetValue.includes(conditionValue) : conditionValue === targetValue;
      case 'neq':
        return Array.isArray(targetValue) ? !targetValue.includes(conditionValue) : conditionValue !== targetValue;
      case 'contains':
        return typeof conditionValue === 'string' && typeof targetValue === 'string' && conditionValue.includes(targetValue);
      case 'gt':
        return typeof conditionValue === 'number' && typeof targetValue === 'number' && conditionValue > targetValue;
      case 'lt':
        return typeof conditionValue === 'number' && typeof targetValue === 'number' && conditionValue < targetValue;
      default:
        return true;
    }
  };

  // Reset dependent fields when a parent field changes
  useEffect(() => {
    fields.forEach(field => {
      if (field.conditionalOn && !shouldShowField(field) && data[field.key]) {
        // Reset this field's value if its condition is not met
        setData((prev: any) => ({ ...prev, [field.key]: '' }));
      }
    });
  }, [data, fields, setData]);

  return (
    <div className="space-y-3">
      {fields.map(field => {
        // Skip rendering if conditional field and condition not met
        if (!shouldShowField(field)) return null;

        const value = data[field.key] !== undefined ? data[field.key] : '';
        const handleChange = (val: any) => {
          setData((prev: any) => ({ ...prev, [field.key]: val }));
        };

        return (
          <div key={field.key} className="bg-white dark:bg-slate-900 rounded-md shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden">
            <div className="border-b border-slate-100 dark:border-slate-800 bg-slate-50 dark:bg-slate-800/50 px-3 py-2">
              <h3 className="font-medium text-sm text-slate-700 dark:text-slate-300">
                {field.label} {field.required && <span className="text-destructive ml-1">*</span>}
              </h3>
            </div>
            <div className="p-3">
              {field.type === 'text' && (
                <Input
                  id={field.key}
                  type="text"
                  value={value}
                  onChange={e => handleChange(e.target.value)}
                  required={field.required}
                  placeholder={field.placeholder}
                  className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                />
              )}

              {field.type === 'number' && (
                <Input
                  id={field.key}
                  type="number"
                  value={value}
                  onChange={e => handleChange(e.target.value)}
                  required={field.required}
                  placeholder={field.placeholder}
                  className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                />
              )}

              {field.type === 'textarea' && (
                <textarea
                  id={field.key}
                  className="w-full border border-slate-200 dark:border-slate-700 rounded-md px-3 py-1.5 h-20 bg-white dark:bg-slate-950"
                  value={value}
                  onChange={e => handleChange(e.target.value)}
                  required={field.required}
                  placeholder={field.placeholder}
                  rows={3}
                />
              )}

              {field.type === 'date' && (
                <Input
                  id={field.key}
                  type="date"
                  value={value}
                  onChange={e => handleChange(e.target.value)}
                  required={field.required}
                  className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                />
              )}

              {field.type === 'select' && field.options && (
                <Select value={value} onValueChange={handleChange} required={field.required}>
                  <SelectTrigger id={field.key} className="w-full h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700">
                    <SelectValue placeholder={field.placeholder || field.label} />
                  </SelectTrigger>
                  <SelectContent>
                    {field.options.map(opt => (
                      <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              {field.type === 'checkbox' && (
                <div className="flex items-center space-x-2 mt-0.5">
                  <Checkbox
                    id={field.key}
                    checked={value === true}
                    onCheckedChange={checked => handleChange(checked === true)}
                  />
                  <label
                    htmlFor={field.key}
                    className="text-xs text-slate-700 dark:text-slate-300"
                  >
                    {field.placeholder || `Enable ${field.label}`}
                  </label>
                </div>
              )}

              {field.type === 'file' && (
                <Input
                  id={field.key}
                  type="file"
                  onChange={e => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleChange({
                        name: file.name,
                        type: file.type,
                        size: file.size,
                      });
                    }
                  }}
                  required={field.required}
                  className="cursor-pointer h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                />
              )}

              {field.type === 'multiselect' && field.options && (
                <div className="border border-slate-200 dark:border-slate-700 rounded-md p-2 space-y-1 bg-white dark:bg-slate-950">
                  {field.options.map(opt => (
                    <div key={opt} className="flex items-center space-x-2">
                      <Checkbox
                        id={`${field.key}-${opt}`}
                        checked={Array.isArray(value) && value.includes(opt)}
                        onCheckedChange={checked => {
                          const currentValues = Array.isArray(value) ? value : [];
                          if (checked) {
                            if (!currentValues.includes(opt)) {
                              handleChange([...currentValues, opt]);
                            }
                          } else {
                            handleChange(currentValues.filter(v => v !== opt));
                          }
                        }}
                      />
                      <label
                        htmlFor={`${field.key}-${opt}`}
                        className="text-xs text-slate-700 dark:text-slate-300"
                      >
                        {opt}
                      </label>
                    </div>
                  ))}
                </div>
              )}

              {field.type === 'signature' && (
                <div className="border border-slate-200 dark:border-slate-700 rounded-md p-3 space-y-1 bg-white dark:bg-slate-950">
                  <div className="flex items-center space-x-2">
                    <Input
                      id={`${field.key}-name`}
                      type="text"
                      value={value?.name || ''}
                      onChange={e => handleChange({ ...value, name: e.target.value })}
                      placeholder="Type your full name"
                      className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 mb-1"
                    />
                  </div>
                  <div className="border-t border-dashed border-slate-200 dark:border-slate-700 pt-2 text-center">
                    <p className="text-xs text-slate-500 dark:text-slate-400">By typing your name above, you agree this represents your electronic signature.</p>
                  </div>
                </div>
              )}

              {field.helpText && <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">{field.helpText}</p>}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default DynamicFieldRenderer;
