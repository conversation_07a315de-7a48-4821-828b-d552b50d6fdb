import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs';
import { useContractWizard, Party, ContractData } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronRight, ChevronLeft, Plus, Trash2 } from 'lucide-react';
import { partyTypes } from './contractTemplates';

const Step2PartiesInfo: React.FC = () => {
  const { data, setData, nextStep, prevStep, selectedTemplate } = useContractWizard();
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({
    title: false,
    effectiveDate: false,
  });

  // Initialize parties with proper structure if they don't exist
  React.useEffect(() => {
    if (!Array.isArray(data.parties) || data.parties.length === 0) {
      const initialParties = [
        { type: 'individual', name: '', address: '', representative: '', title: '', role: selectedTemplate?.parties?.roles?.['party1'] || '' },
        { type: 'company', name: '', address: '', representative: '', title: '', role: selectedTemplate?.parties?.roles?.['party2'] || '' }
      ];
      setData(d => ({ ...d, parties: initialParties }));
    } else if (selectedTemplate?.parties?.roles) {
      // Ensure roles are assigned based on template
      const updatedParties = [...data.parties];
      let changed = false;

      Object.entries(selectedTemplate.parties.roles).forEach(([key, role], index) => {
        if (updatedParties[index] && !updatedParties[index].role) {
          updatedParties[index] = { ...updatedParties[index], role };
          changed = true;
        }
      });

      if (changed) {
        setData(d => ({ ...d, parties: updatedParties }));
      }
    }
  }, [data.parties, selectedTemplate, setData]);

  // Set basic validation - check if required fields are filled
  const validateParty = (party: Party): boolean => {
    if (!party.type) return false;

    // Get fields for this party type
    const typeConfig = partyTypes.find(t => t.type === party.type);
    if (!typeConfig) return false;

    // Check if all required fields for this party type are filled
    return typeConfig.fields
      .filter(field => field.required)
      .every(field => party[field.key] && party[field.key].trim() !== '');
  };

  const isValid =
    data.title &&
    data.effectiveDate &&
    Array.isArray(data.parties) &&
    data.parties.length >= (selectedTemplate?.parties?.min || 2) &&
    data.parties.every(validateParty);

  const handleBlur = (field: string) => {
    setTouched(t => ({ ...t, [field]: true }));
  };

  const handlePartyChange = (idx: number, field: string, value: any) => {
    setData(d => ({
      ...d,
      parties: d.parties.map((p, i) => i === idx ? { ...p, [field]: value } : p)
    }) as ContractData);
  };

  const handlePartyTypeChange = (idx: number, type: string) => {
    const oldParty = data.parties[idx];

    // Create a new party object with type-specific defaults
    const newParty: Party = {
      type,
      role: oldParty.role || '', // Preserve role
      name: oldParty.name || '', // Preserve name if possible
      address: oldParty.address || '', // Initialize required properties
      representative: oldParty.representative || '',
      title: oldParty.title || ''
    };

    // Find the party type definition
    const typeConfig = partyTypes.find(t => t.type === type);
    if (typeConfig) {
      // Initialize all fields in this party type
      typeConfig.fields.forEach(field => {
        // If the field existed in the old party, preserve its value
        newParty[field.key] = oldParty[field.key] || '';
      });
    }

    // Update the party at this index
    setData(d => ({
      ...d,
      parties: d.parties.map((p, i) => i === idx ? newParty : p)
    }) as ContractData);
  };

  const addParty = () => {
    const newPartyIndex = data.parties.length;
    const defaultType = selectedTemplate?.parties?.types?.[0] || 'individual';

    const newParty: Party = {
      type: defaultType,
      role: selectedTemplate?.parties?.roles?.[`party${newPartyIndex + 1}`] || '',
      name: '', // Initialize required properties with empty strings
      address: '',
      representative: '',
      title: ''
    };

    // Initialize with default fields for the party type
    const typeConfig = partyTypes.find(t => t.type === defaultType);
    if (typeConfig) {
      typeConfig.fields.forEach(field => {
        newParty[field.key] = '';
      });
    }

    setData(d => ({
      ...d,
      parties: [...d.parties, newParty]
    }) as ContractData);
  };

  const removeParty = (idx: number) => {
    const minParties = selectedTemplate?.parties?.min || 2;
    if (data.parties.length <= minParties) return; // Always keep minimum required

    setData(d => ({
      ...d,
      parties: d.parties.filter((_, i) => i !== idx)
    }) as ContractData);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(t => {
      const newTouched = { ...t, title: true, effectiveDate: true };
      data.parties.forEach((_, idx) => {
        newTouched[`partyName${idx}`] = true;
      });
      return newTouched;
    });
    if (isValid) nextStep();
  };

  // Get available party types from the template or use all
  const availablePartyTypes = selectedTemplate?.parties?.types ||
                             partyTypes.map(t => t.type);

  // Get maximum allowed parties
  const maxParties = selectedTemplate?.parties?.max || 10;

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="mb-3 bg-slate-100 dark:bg-slate-800/50 p-0.5 rounded-md">
          <TabsTrigger value="general" className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:shadow-sm text-xs py-1 h-7">General Info</TabsTrigger>
          {data.parties.map((_, idx) => (
            <TabsTrigger key={idx} value={`party-${idx}`} className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:shadow-sm text-xs py-1 h-7">Party {idx + 1}</TabsTrigger>
          ))}
        </TabsList>

        {/* General Info Tab */}
        <TabsContent value="general">
          <div className="space-y-3 max-w-xl">
            <div className="space-y-1">
              <Label htmlFor="title" className="text-sm font-medium flex items-center">
                Contract Title <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="title"
                value={data.title}
                onChange={e => setData(d => ({ ...d, title: e.target.value }))}
                onBlur={() => handleBlur('title')}
                required
                placeholder="e.g. Master Services Agreement"
                className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
              />
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">A short, descriptive title for this contract</p>
              {touched.title && !data.title && (
                <div className="text-xs text-destructive mt-0.5">Contract title is required.</div>
              )}
            </div>

            <div className="space-y-2 mt-2">
              <Label className="text-sm font-medium">Contract Dates</Label>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex-1 space-y-1">
                  <Label htmlFor="effectiveDate" className="text-xs font-medium flex items-center">
                    Effective Date <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Input
                    id="effectiveDate"
                    type="date"
                    value={data.effectiveDate}
                    onChange={e => setData(d => ({ ...d, effectiveDate: e.target.value }))}
                    onBlur={() => handleBlur('effectiveDate')}
                    required
                    className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                  />
                  {touched.effectiveDate && !data.effectiveDate && (
                    <div className="text-xs text-destructive mt-0.5">Effective date is required.</div>
                  )}
                </div>
                <div className="flex-1 space-y-1">
                  <Label htmlFor="duration" className="text-xs font-medium">Duration</Label>
                  <Input
                    id="duration"
                    value={data.duration}
                    onChange={e => setData(d => ({ ...d, duration: e.target.value }))}
                    placeholder="e.g. 12 months"
                    className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">How long this contract will be in effect</p>
                </div>
              </div>
            </div>

            <div className="space-y-1 mt-2">
              <Label htmlFor="description" className="text-sm font-medium">Contract Description</Label>
              <Input
                id="description"
                value={data.description}
                onChange={e => setData(d => ({ ...d, description: e.target.value }))}
                placeholder="Brief summary or background of the contract"
                className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
              />
            </div>

            <div className="space-y-1 mt-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Parties to this Contract</Label>
                {data.parties.length < maxParties && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addParty}
                    className="h-6 flex items-center gap-1 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
                  >
                    <Plus className="h-3 w-3" />
                    Add Party
                  </Button>
                )}
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400">Use the tabs above to edit each party's information</p>
            </div>
          </div>
        </TabsContent>

        {/* Party Tabs */}
        {data.parties.map((party, idx) => {
          const partyTypeConfig = partyTypes.find(t => t.type === party.type);
          const fields = partyTypeConfig?.fields || [];

          return (
            <TabsContent key={idx} value={`party-${idx}`}>
              <div className="space-y-3 max-w-xl">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-2">
                    <h3 className="text-sm font-medium">Party {idx + 1}</h3>
                    {party.role && (
                      <div className="text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-2 py-0.5 rounded-full font-medium">
                        {party.role}
                      </div>
                    )}
                  </div>
                  {data.parties.length > (selectedTemplate?.parties?.min || 2) && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="text-destructive h-6 hover:bg-red-50 dark:hover:bg-red-900/20"
                      onClick={() => removeParty(idx)}
                      aria-label={`Remove Party ${idx + 1}`}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Remove
                    </Button>
                  )}
                </div>

                {/* Party Type Selector */}
                <div className="space-y-1">
                  <Label htmlFor={`party-type-${idx}`} className="text-sm font-medium flex items-center">
                    Party Type <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Select
                    value={party.type || ''}
                    onValueChange={value => handlePartyTypeChange(idx, value)}
                  >
                    <SelectTrigger id={`party-type-${idx}`} className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700">
                      <SelectValue placeholder="Select party type" />
                    </SelectTrigger>
                    <SelectContent>
                      {partyTypes.filter(t => availablePartyTypes.includes(t.type)).map(type => (
                        <SelectItem key={type.type} value={type.type}>{type.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Dynamic fields based on party type */}
                <div className="space-y-3 mt-2">
                  {fields.map(field => (
                    <div key={field.key} className="space-y-1">
                      <Label htmlFor={`party-${idx}-${field.key}`} className="text-sm font-medium flex items-center">
                        {field.label} {field.required && <span className="text-destructive ml-1">*</span>}
                      </Label>
                      <Input
                        id={`party-${idx}-${field.key}`}
                        type={field.type === 'date' ? 'date' : 'text'}
                        value={party[field.key] || ''}
                        onChange={e => handlePartyChange(idx, field.key, e.target.value)}
                        required={field.required}
                        placeholder={field.placeholder}
                        className="h-8 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                      />
                      {field.helpText && <p className="text-xs text-slate-500 dark:text-slate-400 mt-0.5">{field.helpText}</p>}
                      {touched[`party-${idx}-${field.key}`] && field.required && !party[field.key] && (
                        <div className="text-xs text-destructive mt-0.5">{field.label} is required.</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          );
        })}
      </Tabs>

      {/* Navigation Buttons */}
      <div className="sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent pt-4 pb-3 mt-4 z-10">
        <div className="flex justify-between gap-2 max-w-xl">
          <Button
            type="button"
            variant="outline"
            className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
            onClick={prevStep}
          >
            <ChevronLeft className="mr-1 h-3 w-3" />
            Back
          </Button>
          <Button
            type="submit"
            className="flex items-center h-7 px-3 shadow-sm transition-all hover:shadow text-xs"
            disabled={!isValid}
          >
            Continue
            <ChevronRight className="ml-1 h-3 w-3" />
          </Button>
        </div>
      </div>
    </form>
  );
};

export default Step2PartiesInfo;