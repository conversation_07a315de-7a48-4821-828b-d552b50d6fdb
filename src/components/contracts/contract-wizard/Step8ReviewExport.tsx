import React, { useRef, useState } from 'react';
import { useContractWizard } from './ContractWizardContext';
import html2pdf from 'html2pdf.js';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Template, templateStore } from '@/types/template';

const Step8ReviewExport: React.FC = () => {
  const { data, prevStep, clearDraft } = useContractWizard();
  const [showExported, setShowExported] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const contractRef = useRef<HTMLDivElement>(null);

  // Template dialog state
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [templateTitle, setTemplateTitle] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateType, setTemplateType] = useState('');
  const [templateComplexity, setTemplateComplexity] = useState<'simple' | 'medium' | 'complex'>('medium');
  const [templateIndustry, setTemplateIndustry] = useState('');
  const [templateSaved, setTemplateSaved] = useState(false);

  // Handle export
  const handleExport = async (type: 'pdf' | 'txt') => {
    setExporting(true);
    setDropdownOpen(false);
    if (type === 'pdf' && contractRef.current) {
      await html2pdf().from(contractRef.current).save(`${data.title || 'contract'}.pdf`);
    } else if (type === 'txt') {
      const text = contractRef.current?.innerText || '';
      const blob = new Blob([text], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${data.title || 'contract'}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }
    setExporting(false);
    setShowExported(true);
    clearDraft();
    setTimeout(() => setShowExported(false), 2000);
  };

  // Open template dialog with pre-filled data
  const openSaveAsTemplateDialog = () => {
    setTemplateTitle(data.title || '');
    setTemplateDescription(data.description || '');
    setTemplateType(data.contractType || '');
    setTemplateDialogOpen(true);
  };

  // Save as template
  const handleSaveAsTemplate = () => {
    if (!templateTitle.trim()) {
      return; // Don't save without a title
    }

    // Create template object
    const newTemplate: Omit<Template, 'id'> = {
      title: templateTitle,
      description: templateDescription,
      type: templateType,
      complexity: templateComplexity,
      lastUpdated: new Date().toISOString().split('T')[0],
      usageCount: 0,
      industry: templateIndustry || undefined,
      tags: data.tags || [],
      icon: getIconFromType(templateType),
      isUserCreated: true,
      createdBy: {
        name: 'Current User', // In a real app, get from user context
        id: 'current-user'
      }
    };

    // Add to template store
    templateStore.addTemplate(newTemplate);

    // Close dialog and show success message
    setTemplateDialogOpen(false);
    setTemplateSaved(true);
    setTimeout(() => setTemplateSaved(false), 2000);
  };

  // Helper to determine icon based on contract type
  const getIconFromType = (type: string): string => {
    const typeMap: Record<string, string> = {
      'Service Agreement': 'service',
      'NDA': 'nda',
      'Employment': 'employment',
      'License': 'software',
      'Lease': 'lease',
      'Manufacturing': 'manufacturing',
      'Sales': 'sales'
    };

    return typeMap[type] || 'blank';
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Card className="flex flex-1 flex-col justify-stretch min-h-0">
        <CardContent className="p-4 flex-1 flex flex-col">
          <Label className="text-base mb-2">Review Contract Details</Label>
          <div ref={contractRef} className="bg-muted rounded p-4 text-sm mt-2 flex-1 overflow-auto">
            <div><strong>Jurisdiction:</strong> {data.jurisdiction}</div>
            <div><strong>Contract Type:</strong> {data.contractType}</div>
            <div><strong>Title:</strong> {data.title}</div>
            <div><strong>Effective Date:</strong> {data.effectiveDate}</div>
            <div><strong>Duration:</strong> {data.duration}</div>
            <div><strong>Description:</strong> {data.description}</div>
            <div className="mt-2"><strong>Parties:</strong></div>
            <ul className="ml-4 list-disc">
              {Array.isArray(data.parties) && data.parties.map((party, idx) => (
                <li key={idx}>{party.name} {party.title && `(${party.title})`}</li>
              ))}
            </ul>
            <div className="mt-2"><strong>Contract Value:</strong> {data.contractValue} {data.currency}</div>
            <div><strong>Payment Terms:</strong> {data.paymentTerms}</div>
            <div><strong>Payment Method:</strong> {data.paymentMethod}</div>
            <div className="mt-2"><strong>Deliverables:</strong> {data.deliverables && data.deliverables.length ? data.deliverables.join(', ') : 'None'}</div>
            <div className="mt-2"><strong>Standard Clauses:</strong> {data.standardClauses && data.standardClauses.length ? data.standardClauses.join(', ') : 'None'}</div>
            <div><strong>Custom Clauses:</strong> {data.customClauses && data.customClauses.length ? data.customClauses.join(', ') : 'None'}</div>


            <div className="mt-2"><strong>Attachments:</strong> {data.attachments && data.attachments.length ? data.attachments.map((a: any) => `${a.name} (${a.type})`).join(', ') : 'None'}</div>
            <div className="mt-2"><strong>Approvers:</strong> {data.approvers && data.approvers.length ? data.approvers.map((a: any) => `${a.name} (${a.role})`).join(', ') : 'None'}</div>
            <div><strong>Approval Process:</strong> {data.approvalProcess}</div>
          </div>
        </CardContent>
      </Card>
      <div className="pt-4 flex justify-between items-center gap-2">
        <Button type="button" variant="outline" size="sm" className="h-8 px-3" onClick={prevStep}>
          Back
        </Button>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-8 px-3"
            onClick={openSaveAsTemplateDialog}
          >
            Save as Template
          </Button>
          <div className="relative">
            <Button
              type="button"
              size="sm"
              className="bg-green-600 text-white flex items-center gap-2"
              onClick={() => setDropdownOpen((v) => !v)}
              aria-haspopup="listbox"
              aria-expanded={dropdownOpen}
              disabled={exporting}
            >
              {exporting ? (
                <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" /><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" /></svg>
              ) : (
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" /></svg>
              )}
              Export
              <svg className="h-4 w-4 ml-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
            </Button>
            {dropdownOpen && !exporting && (
              <ul
                className="absolute right-0 mt-2 w-40 bg-white border border-gray-200 rounded shadow-lg z-10"
                role="listbox"
                tabIndex={-1}
              >
                <li>
                  <Button
                    variant="ghost"
                    className="w-full text-left px-4 py-2 justify-start"
                    onClick={() => handleExport('pdf')}
                    role="option"
                    aria-label="Export as PDF"
                  >
                    Export as PDF
                  </Button>
                </li>
                <li>
                  <Button
                    variant="ghost"
                    className="w-full text-left px-4 py-2 justify-start"
                    onClick={() => handleExport('txt')}
                    role="option"
                    aria-label="Export as TXT"
                  >
                    Export as TXT
                  </Button>
                </li>
              </ul>
            )}
          </div>
        </div>
      </div>

      {/* Success messages */}
      {showExported && (
        <div className="pt-2">
          <Alert>
            <AlertDescription>Contract exported and draft cleared!</AlertDescription>
          </Alert>
        </div>
      )}

      {templateSaved && (
        <div className="pt-2">
          <Alert>
            <AlertDescription>Contract saved as template! You can find it in the Templates folder in the Repository.</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Save as Template Dialog */}
      <Dialog open={templateDialogOpen} onOpenChange={setTemplateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Save as Template</DialogTitle>
            <DialogDescription>
              Create a reusable template from this contract. Templates are stored in the Repository.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-title" className="text-right">
                Title
              </Label>
              <Input
                id="template-title"
                value={templateTitle}
                onChange={(e) => setTemplateTitle(e.target.value)}
                className="col-span-3"
                placeholder="Template title"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-type" className="text-right">
                Type
              </Label>
              <Input
                id="template-type"
                value={templateType}
                onChange={(e) => setTemplateType(e.target.value)}
                className="col-span-3"
                placeholder="Contract type"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-complexity" className="text-right">
                Complexity
              </Label>
              <Select
                value={templateComplexity}
                onValueChange={(value) => setTemplateComplexity(value as 'simple' | 'medium' | 'complex')}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select complexity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">Simple</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="complex">Complex</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-industry" className="text-right">
                Industry
              </Label>
              <Input
                id="template-industry"
                value={templateIndustry}
                onChange={(e) => setTemplateIndustry(e.target.value)}
                className="col-span-3"
                placeholder="Industry (optional)"
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="template-description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="template-description"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                className="col-span-3"
                placeholder="Template description"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setTemplateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveAsTemplate}>
              Save Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Step8ReviewExport;