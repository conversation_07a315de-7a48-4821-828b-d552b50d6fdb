import React, { useRef, useEffect } from 'react';
import html2pdf from 'html2pdf.js';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';

import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Download,
  Undo,
  Redo,
  Table as TableIcon
} from 'lucide-react';
import { useContractWizard } from './ContractWizardContext';
import { generateContractContent } from './contractUtils';

/**
 * ContractDocumentPreview: Presentational contract preview UI with editing capabilities
 * - Professional legal document styling
 * - Realistic contract formatting and layout
 * - Editing toolbar and functionality
 */
const ContractDocumentPreview: React.FC = () => {
  const { data } = useContractWizard();
  const previewRef = useRef<HTMLDivElement>(null);

  // Generate default content
  const defaultContent = data.importedContent || generateContractContent(data);

  // Initialize TipTap editor with professional document styling
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
    ],
    content: defaultContent,
    editable: true,
    editorProps: {
      attributes: {
        class: 'focus:outline-none',
        style: 'font-family: "Times New Roman", serif; font-size: 12pt; line-height: 1.6; color: #1a1a1a; min-height: 100%; padding: 0 1in 1in 1in; margin: 0; background: white; max-width: 8.5in; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);'
      },
    },
  });

  // Update editor content when data changes
  useEffect(() => {
    if (editor) {
      const newContent = data.importedContent || generateContractContent(data);
      editor.commands.setContent(newContent);
    }
  }, [editor, data, data.importedContent]);

  // Handle PDF export with professional styling
  const handleExportPDF = () => {
    if (previewRef.current) {
      // Create a temporary container with professional print styles
      const printContainer = document.createElement('div');
      printContainer.innerHTML = editor?.getHTML() || defaultContent;
      printContainer.className = 'professional-contract-document';
      printContainer.style.cssText = `
        font-family: "Times New Roman", serif;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: white;
        padding: 1in;
        max-width: 8.5in;
        margin: 0 auto;
      `;

      // Temporarily add to document for PDF generation
      document.body.appendChild(printContainer);

      html2pdf().from(printContainer).set({
        margin: [0.5, 0.5, 0.5, 0.5],
        filename: `${data.title || 'contract'}.pdf`,
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait',
          compress: true
        },
      }).save().then(() => {
        // Clean up temporary container
        document.body.removeChild(printContainer);
      });
    }
  };



  // Handle editor commands using TipTap
  const handleCommand = (command: string, value: string = '') => {
    if (!editor) return;

    switch (command) {
      case 'bold':
        editor.chain().focus().toggleBold().run();
        break;
      case 'italic':
        editor.chain().focus().toggleItalic().run();
        break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run();
        break;
      case 'justifyLeft':
        editor.chain().focus().setTextAlign('left').run();
        break;
      case 'justifyCenter':
        editor.chain().focus().setTextAlign('center').run();
        break;
      case 'justifyRight':
        editor.chain().focus().setTextAlign('right').run();
        break;
      case 'insertUnorderedList':
        editor.chain().focus().toggleBulletList().run();
        break;
      case 'insertOrderedList':
        editor.chain().focus().toggleOrderedList().run();
        break;
      case 'formatBlock':
        if (value === '<h1>') {
          editor.chain().focus().toggleHeading({ level: 1 }).run();
        } else if (value === '<h2>') {
          editor.chain().focus().toggleHeading({ level: 2 }).run();
        }
        break;
      case 'undo':
        editor.chain().focus().undo().run();
        break;
      case 'redo':
        editor.chain().focus().redo().run();
        break;
      case 'fontName':
        // Font family changes would need custom extension
        console.log('Font change:', value);
        break;
      case 'fontSize':
        // Font size changes would need custom extension
        console.log('Font size change:', value);
        break;
      default:
        console.warn(`Unknown command: ${command}`);
    }
  };

  // Simple table function using TipTap
  const insertTable = () => {
    if (!editor) return;

    // Insert a simple HTML table using TipTap's insertContent command
    const tableHTML = `
      <table style="width:100%; border-collapse: collapse; border: 1px solid #ccc;">
        <tr>
          <th style="border: 1px solid #ccc; padding: 8px; background-color: #f2f2f2;">Header 1</th>
          <th style="border: 1px solid #ccc; padding: 8px; background-color: #f2f2f2;">Header 2</th>
          <th style="border: 1px solid #ccc; padding: 8px; background-color: #f2f2f2;">Header 3</th>
        </tr>
        <tr>
          <td style="border: 1px solid #ccc; padding: 8px;">Cell 1-1</td>
          <td style="border: 1px solid #ccc; padding: 8px;">Cell 1-2</td>
          <td style="border: 1px solid #ccc; padding: 8px;">Cell 1-3</td>
        </tr>
        <tr>
          <td style="border: 1px solid #ccc; padding: 8px;">Cell 2-1</td>
          <td style="border: 1px solid #ccc; padding: 8px;">Cell 2-2</td>
          <td style="border: 1px solid #ccc; padding: 8px;">Cell 2-3</td>
        </tr>
      </table>
      <p></p>
    `;

    editor.chain().focus().insertContent(tableHTML).run();
  };





  return (
    <div className="w-full h-full flex flex-col overflow-hidden" style={{ height: '100%', minHeight: '80vh' }}>
      {/* Simple header with title and export button */}
      <div className="flex items-center justify-between py-2 px-4">
        <h2>{data.title || 'Contract Preview'}</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportPDF}
            className="h-8"
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Enhanced toolbar with fonts, font size, and table */}
      <div className="flex items-center gap-1 py-1 px-4 border-y overflow-x-auto">
        {/* Font family selector */}
        <Select onValueChange={(value) => handleCommand('fontName', value)}>
          <SelectTrigger className="h-8 w-[130px] mr-1">
            <SelectValue placeholder="Font" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Arial">Arial</SelectItem>
            <SelectItem value="Times New Roman">Times New Roman</SelectItem>
            <SelectItem value="Courier New">Courier New</SelectItem>
            <SelectItem value="Georgia">Georgia</SelectItem>
            <SelectItem value="Verdana">Verdana</SelectItem>
            <SelectItem value="Helvetica">Helvetica</SelectItem>
          </SelectContent>
        </Select>

        {/* Font size selector */}
        <Select onValueChange={(value) => handleCommand('fontSize', value)}>
          <SelectTrigger className="h-8 w-[80px] mr-1">
            <SelectValue placeholder="Size" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">8pt</SelectItem>
            <SelectItem value="2">10pt</SelectItem>
            <SelectItem value="3">12pt</SelectItem>
            <SelectItem value="4">14pt</SelectItem>
            <SelectItem value="5">18pt</SelectItem>
            <SelectItem value="6">24pt</SelectItem>
            <SelectItem value="7">36pt</SelectItem>
          </SelectContent>
        </Select>

        <span className="mx-1 border-l h-5" />

        {/* Text formatting */}
        <Button
          variant={editor?.isActive('bold') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('bold')}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={editor?.isActive('italic') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('italic')}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={editor?.isActive('underline') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('underline')}
        >
          <UnderlineIcon className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Alignment */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyLeft')}>
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyCenter')}>
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyRight')}>
          <AlignRight className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Lists */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertUnorderedList')}>
          <List className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertOrderedList')}>
          <ListOrdered className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Headings */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('formatBlock', '<h1>')}>
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('formatBlock', '<h2>')}>
          <Heading2 className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Table */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => insertTable()}>
          <TableIcon className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Undo/Redo */}
        <Button
          variant="ghost"
          size="sm"
          className="h-8"
          onClick={() => handleCommand('undo')}
          disabled={!editor?.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8"
          onClick={() => handleCommand('redo')}
          disabled={!editor?.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>

      {/* Document Content - TipTap Editor */}
      <div className="flex-1 overflow-auto bg-gray-50 flex justify-center" style={{ maxHeight: 'calc(100vh - 100px)' }}>
        <EditorContent
          editor={editor}
          className="min-h-full focus:outline-none"
        />
        {/* Hidden preview div for PDF export */}
        <div
          className="hidden"
          ref={previewRef}
          dangerouslySetInnerHTML={{ __html: editor?.getHTML() || defaultContent }}
        />
      </div>
    </div>
  );
};

export default ContractDocumentPreview;
