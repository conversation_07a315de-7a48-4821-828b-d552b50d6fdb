import React, { useRef, useState, useMemo } from 'react';
import html2pdf from 'html2pdf.js';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import { Color } from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';

import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Download,
  Undo,
  Redo,
  Table as TableIcon,
  Highlighter,
  Move,
  Eye,
  EyeOff
} from 'lucide-react';
import { useContractWizard } from './ContractWizardContext';
import { generateContractContent } from './contractUtils';
import { useRealTimeSync } from './RealTimeSyncManager';
import SectionManager from './SectionManager';

/**
 * Enhanced ContractDocumentPreview with real-time sync and advanced formatting
 * - Professional legal document styling
 * - Real-time synchronization with form data
 * - Advanced editing toolbar and functionality
 * - Section management capabilities
 */
const ContractDocumentPreview: React.FC = () => {
  const { data, setData } = useContractWizard();
  const previewRef = useRef<HTMLDivElement>(null);
  const [isRealTimeSync, setIsRealTimeSync] = useState(true);
  const [showSectionManager, setShowSectionManager] = useState(false);
  const [selectedFont, setSelectedFont] = useState('Times New Roman');
  const [selectedFontSize, setSelectedFontSize] = useState('12pt');

  // Memoized content generation for performance
  const defaultContent = useMemo(() => {
    return data.importedContent || generateContractContent(data);
  }, [data.importedContent, data.title, data.parties, data.jurisdiction, data.contractType, data.effectiveDate, data.description, data.standardClauses, data.customClauses, data.libraryClauses]);

  // Initialize TipTap editor with enhanced extensions
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      TextStyle,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
    ],
    content: defaultContent,
    editable: true,
    editorProps: {
      attributes: {
        class: 'focus:outline-none',
        style: `font-family: "${selectedFont}", serif; font-size: ${selectedFontSize}; line-height: 1.6; color: #1a1a1a; min-height: 100%; padding: 0 1in 1in 1in; margin: 0; background: white; max-width: 8.5in; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);`
      },
    },
  });

  // Use real-time sync hook
  const { syncStatus, SyncManager } = useRealTimeSync(editor, isRealTimeSync);

  // Handle PDF export with professional styling
  const handleExportPDF = () => {
    if (previewRef.current) {
      // Create a temporary container with professional print styles
      const printContainer = document.createElement('div');
      printContainer.innerHTML = editor?.getHTML() || defaultContent;
      printContainer.className = 'professional-contract-document';
      printContainer.style.cssText = `
        font-family: "Times New Roman", serif;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: white;
        padding: 1in;
        max-width: 8.5in;
        margin: 0 auto;
      `;

      // Temporarily add to document for PDF generation
      document.body.appendChild(printContainer);

      html2pdf().from(printContainer).set({
        margin: [0.5, 0.5, 0.5, 0.5],
        filename: `${data.title || 'contract'}.pdf`,
        html2canvas: {
          scale: 2,
          useCORS: true,
          letterRendering: true
        },
        jsPDF: {
          unit: 'in',
          format: 'a4',
          orientation: 'portrait',
          compress: true
        },
      }).save().then(() => {
        // Clean up temporary container
        document.body.removeChild(printContainer);
      });
    }
  };



  // Enhanced command handling with new features
  const handleCommand = (command: string, value: string = '') => {
    if (!editor) return;

    switch (command) {
      case 'bold':
        editor.chain().focus().toggleBold().run();
        break;
      case 'italic':
        editor.chain().focus().toggleItalic().run();
        break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run();
        break;
      case 'justifyLeft':
        editor.chain().focus().setTextAlign('left').run();
        break;
      case 'justifyCenter':
        editor.chain().focus().setTextAlign('center').run();
        break;
      case 'justifyRight':
        editor.chain().focus().setTextAlign('right').run();
        break;
      case 'insertUnorderedList':
        editor.chain().focus().toggleBulletList().run();
        break;
      case 'insertOrderedList':
        editor.chain().focus().toggleOrderedList().run();
        break;
      case 'formatBlock':
        if (value === '<h1>') {
          editor.chain().focus().toggleHeading({ level: 1 }).run();
        } else if (value === '<h2>') {
          editor.chain().focus().toggleHeading({ level: 2 }).run();
        }
        break;
      case 'undo':
        editor.chain().focus().undo().run();
        break;
      case 'redo':
        editor.chain().focus().redo().run();
        break;
      case 'fontName':
        setSelectedFont(value);
        editor.chain().focus().setFontFamily(value).run();
        break;
      case 'fontSize':
        setSelectedFontSize(value);
        // Apply font size via CSS style
        editor.chain().focus().setMark('textStyle', { fontSize: value }).run();
        break;
      case 'textColor':
        editor.chain().focus().setColor(value).run();
        break;
      case 'highlight':
        editor.chain().focus().toggleHighlight({ color: value || '#ffff00' }).run();
        break;
      case 'removeHighlight':
        editor.chain().focus().unsetHighlight().run();
        break;
      case 'insertTable':
        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        break;
      case 'deleteTable':
        editor.chain().focus().deleteTable().run();
        break;
      case 'addColumnBefore':
        editor.chain().focus().addColumnBefore().run();
        break;
      case 'addColumnAfter':
        editor.chain().focus().addColumnAfter().run();
        break;
      case 'deleteColumn':
        editor.chain().focus().deleteColumn().run();
        break;
      case 'addRowBefore':
        editor.chain().focus().addRowBefore().run();
        break;
      case 'addRowAfter':
        editor.chain().focus().addRowAfter().run();
        break;
      case 'deleteRow':
        editor.chain().focus().deleteRow().run();
        break;
      case 'toggleHeaderColumn':
        editor.chain().focus().toggleHeaderColumn().run();
        break;
      case 'toggleHeaderRow':
        editor.chain().focus().toggleHeaderRow().run();
        break;
      case 'toggleHeaderCell':
        editor.chain().focus().toggleHeaderCell().run();
        break;
      case 'mergeCells':
        editor.chain().focus().mergeCells().run();
        break;
      case 'splitCell':
        editor.chain().focus().splitCell().run();
        break;
      case 'toggleRealTimeSync':
        setIsRealTimeSync(!isRealTimeSync);
        break;
      case 'showSectionManager':
        setShowSectionManager(!showSectionManager);
        break;
      default:
        console.warn(`Unknown command: ${command}`);
    }
  };

  // Sync status indicator
  const getSyncStatusColor = () => {
    switch (syncStatus) {
      case 'syncing': return 'bg-yellow-100 text-yellow-800';
      case 'synced': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };





  return (
    <div className="w-full h-full flex flex-col overflow-hidden" style={{ height: '100%', minHeight: '80vh' }}>
      {/* Simple header with title and export button */}
      <div className="flex items-center justify-between py-2 px-4">
        <h2>{data.title || 'Contract Preview'}</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportPDF}
            className="h-8"
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Enhanced toolbar with advanced formatting features */}
      <div className="flex items-center gap-1 py-1 px-4 border-y overflow-x-auto">
        {/* Real-time sync toggle */}
        <Button
          variant={isRealTimeSync ? 'default' : 'ghost'}
          size="sm"
          className="h-8 mr-2"
          onClick={() => handleCommand('toggleRealTimeSync')}
          title="Toggle real-time sync"
        >
          {isRealTimeSync ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Font family selector */}
        <Select value={selectedFont} onValueChange={(value) => handleCommand('fontName', value)}>
          <SelectTrigger className="h-8 w-[130px] mr-1">
            <SelectValue placeholder="Font" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Arial">Arial</SelectItem>
            <SelectItem value="Times New Roman">Times New Roman</SelectItem>
            <SelectItem value="Courier New">Courier New</SelectItem>
            <SelectItem value="Georgia">Georgia</SelectItem>
            <SelectItem value="Verdana">Verdana</SelectItem>
            <SelectItem value="Helvetica">Helvetica</SelectItem>
          </SelectContent>
        </Select>

        {/* Font size selector */}
        <Select value={selectedFontSize} onValueChange={(value) => handleCommand('fontSize', value)}>
          <SelectTrigger className="h-8 w-[80px] mr-1">
            <SelectValue placeholder="Size" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="8pt">8pt</SelectItem>
            <SelectItem value="10pt">10pt</SelectItem>
            <SelectItem value="12pt">12pt</SelectItem>
            <SelectItem value="14pt">14pt</SelectItem>
            <SelectItem value="16pt">16pt</SelectItem>
            <SelectItem value="18pt">18pt</SelectItem>
            <SelectItem value="24pt">24pt</SelectItem>
          </SelectContent>
        </Select>

        <span className="mx-1 border-l h-5" />

        {/* Text formatting */}
        <Button
          variant={editor?.isActive('bold') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('bold')}
        >
          <Bold className="h-4 w-4" />
        </Button>
        <Button
          variant={editor?.isActive('italic') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('italic')}
        >
          <Italic className="h-4 w-4" />
        </Button>
        <Button
          variant={editor?.isActive('underline') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('underline')}
        >
          <UnderlineIcon className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Alignment */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyLeft')}>
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyCenter')}>
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyRight')}>
          <AlignRight className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Lists */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertUnorderedList')}>
          <List className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertOrderedList')}>
          <ListOrdered className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Headings */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('formatBlock', '<h1>')}>
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('formatBlock', '<h2>')}>
          <Heading2 className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Color and Highlighting */}
        <input
          type="color"
          className="w-8 h-8 border rounded cursor-pointer"
          onChange={(e) => handleCommand('textColor', e.target.value)}
          title="Text Color"
        />
        <Button
          variant={editor?.isActive('highlight') ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('highlight')}
          title="Highlight"
        >
          <Highlighter className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Table */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertTable')} title="Insert Table">
          <TableIcon className="h-4 w-4" />
        </Button>

        {/* Section Manager */}
        <Button
          variant={showSectionManager ? 'default' : 'ghost'}
          size="sm"
          className="h-8"
          onClick={() => handleCommand('showSectionManager')}
          title="Section Manager"
        >
          <Move className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Undo/Redo */}
        <Button
          variant="ghost"
          size="sm"
          className="h-8"
          onClick={() => handleCommand('undo')}
          disabled={!editor?.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8"
          onClick={() => handleCommand('redo')}
          disabled={!editor?.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Section Manager Sidebar */}
        {showSectionManager && (
          <SectionManager
            editor={editor}
            className="w-64 border-r"
            onSectionChange={(sections) => {
              console.log('Sections updated:', sections);
            }}
          />
        )}

        {/* Document Content - TipTap Editor */}
        <div className="flex-1 overflow-auto bg-gray-50 flex justify-center" style={{ maxHeight: 'calc(100vh - 100px)' }}>
          <EditorContent
            editor={editor}
            className="min-h-full focus:outline-none"
          />
          {/* Hidden preview div for PDF export */}
          <div
            className="hidden"
            ref={previewRef}
            dangerouslySetInnerHTML={{ __html: editor?.getHTML() || defaultContent }}
          />
        </div>
      </div>

      {/* Real-time Sync Manager */}
      <SyncManager />

      {/* Sync Status Indicator */}
      {isRealTimeSync && (
        <div className={`absolute bottom-4 right-4 px-2 py-1 rounded text-xs ${getSyncStatusColor()}`}>
          {syncStatus === 'syncing' && 'Syncing...'}
          {syncStatus === 'synced' && 'Real-time sync enabled'}
          {syncStatus === 'error' && 'Sync error'}
        </div>
      )}
    </div>
  );
};

export default ContractDocumentPreview;
