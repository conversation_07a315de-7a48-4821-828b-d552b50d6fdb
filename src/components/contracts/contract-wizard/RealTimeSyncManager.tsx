import React, { useEffect, useRef, useCallback } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { generateContractContent } from './contractUtils';
import { debounce } from 'lodash';

interface RealTimeSyncManagerProps {
  editor: any; // TipTap editor instance
  isEnabled: boolean;
  onSyncStatusChange?: (status: 'syncing' | 'synced' | 'error') => void;
}

/**
 * Real-time Sync Manager for Contract Wizard
 * - Manages bidirectional sync between form data and contract preview
 * - Handles conflict resolution and merge strategies
 * - Provides sync status and error handling
 * - Optimizes performance with debouncing and change detection
 */
const RealTimeSyncManager: React.FC<RealTimeSyncManagerProps> = ({
  editor,
  isEnabled,
  onSyncStatusChange
}) => {
  const { data, setData } = useContractWizard();
  const lastSyncedData = useRef<string>('');
  const lastSyncedContent = useRef<string>('');
  const syncInProgress = useRef<boolean>(false);

  // Generate content hash for change detection
  const generateDataHash = useCallback((contractData: any) => {
    const relevantData = {
      title: contractData.title,
      parties: contractData.parties,
      jurisdiction: contractData.jurisdiction,
      contractType: contractData.contractType,
      effectiveDate: contractData.effectiveDate,
      description: contractData.description,
      standardClauses: contractData.standardClauses,
      customClauses: contractData.customClauses,
      libraryClauses: contractData.libraryClauses,
      paymentTerms: contractData.paymentTerms,
      contractValue: contractData.contractValue,
      currency: contractData.currency,
      scope: contractData.scope,
      deliverables: contractData.deliverables
    };
    return JSON.stringify(relevantData);
  }, []);

  // Sync form data to editor content
  const syncDataToEditor = useCallback(
    debounce(() => {
      if (!editor || !isEnabled || syncInProgress.current) return;

      const currentDataHash = generateDataHash(data);
      
      // Only sync if data has actually changed
      if (currentDataHash === lastSyncedData.current) return;

      try {
        onSyncStatusChange?.('syncing');
        syncInProgress.current = true;

        const newContent = generateContractContent(data);
        const currentContent = editor.getHTML();

        // Check if content is significantly different to avoid unnecessary updates
        if (newContent !== currentContent && !data.importedContent) {
          // Preserve cursor position if possible
          const { from, to } = editor.state.selection;
          
          editor.commands.setContent(newContent);
          
          // Try to restore cursor position
          try {
            if (from <= editor.state.doc.content.size) {
              editor.commands.setTextSelection({ from: Math.min(from, editor.state.doc.content.size) });
            }
          } catch (e) {
            // Cursor restoration failed, but that's okay
          }

          lastSyncedContent.current = newContent;
        }

        lastSyncedData.current = currentDataHash;
        onSyncStatusChange?.('synced');
      } catch (error) {
        console.error('Error syncing data to editor:', error);
        onSyncStatusChange?.('error');
      } finally {
        syncInProgress.current = false;
      }
    }, 300),
    [editor, data, isEnabled, generateDataHash, onSyncStatusChange]
  );

  // Sync editor content back to form data
  const syncEditorToData = useCallback(
    debounce((content: string) => {
      if (!isEnabled || syncInProgress.current) return;

      try {
        // Only update if content has changed significantly
        if (content !== lastSyncedContent.current) {
          setData(prev => ({
            ...prev,
            importedContent: content
          }));
          lastSyncedContent.current = content;
        }
      } catch (error) {
        console.error('Error syncing editor to data:', error);
        onSyncStatusChange?.('error');
      }
    }, 500),
    [isEnabled, setData, onSyncStatusChange]
  );

  // Handle editor updates
  useEffect(() => {
    if (!editor || !isEnabled) return;

    const handleUpdate = ({ editor: updatedEditor }: { editor: any }) => {
      const content = updatedEditor.getHTML();
      syncEditorToData(content);
    };

    editor.on('update', handleUpdate);

    return () => {
      editor.off('update', handleUpdate);
    };
  }, [editor, isEnabled, syncEditorToData]);

  // Handle data changes
  useEffect(() => {
    if (isEnabled) {
      syncDataToEditor();
    }

    return () => {
      syncDataToEditor.cancel();
    };
  }, [data, isEnabled, syncDataToEditor]);

  // Smart merge function for handling conflicts
  const mergeChanges = useCallback((formData: any, editorContent: string) => {
    // This is a simplified merge strategy
    // In a real implementation, you might want more sophisticated conflict resolution
    
    const generatedContent = generateContractContent(formData);
    
    // If editor content is significantly different from generated content,
    // it means user has made manual edits
    const editorWords = editorContent.replace(/<[^>]*>/g, '').split(/\s+/).length;
    const generatedWords = generatedContent.replace(/<[^>]*>/g, '').split(/\s+/).length;
    
    // If word count difference is more than 20%, consider it manually edited
    const wordDifference = Math.abs(editorWords - generatedWords) / generatedWords;
    
    if (wordDifference > 0.2) {
      // Content has been significantly modified, preserve editor content
      return {
        strategy: 'preserve-editor',
        content: editorContent,
        reason: 'Significant manual edits detected'
      };
    } else {
      // Content is similar, use generated content
      return {
        strategy: 'use-generated',
        content: generatedContent,
        reason: 'Form data takes precedence'
      };
    }
  }, []);

  // Conflict resolution handler
  const handleConflict = useCallback((formData: any, editorContent: string) => {
    const mergeResult = mergeChanges(formData, editorContent);
    
    console.log('Conflict resolution:', mergeResult);
    
    // Apply the merge result
    if (mergeResult.strategy === 'preserve-editor') {
      setData(prev => ({
        ...prev,
        importedContent: editorContent
      }));
    } else {
      if (editor) {
        editor.commands.setContent(mergeResult.content);
      }
    }
    
    return mergeResult;
  }, [editor, setData, mergeChanges]);

  // Manual sync trigger
  const triggerManualSync = useCallback(() => {
    if (!editor || !isEnabled) return;

    syncInProgress.current = false; // Reset sync lock
    syncDataToEditor();
    syncDataToEditor.flush(); // Force immediate execution
  }, [editor, isEnabled, syncDataToEditor]);

  // Expose sync controls
  useEffect(() => {
    // Attach sync controls to window for debugging
    (window as any).contractSyncManager = {
      triggerManualSync,
      handleConflict,
      isEnabled,
      syncInProgress: syncInProgress.current
    };

    return () => {
      delete (window as any).contractSyncManager;
    };
  }, [triggerManualSync, handleConflict, isEnabled]);

  // Performance monitoring
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > 100) {
        console.warn(`RealTimeSyncManager lifecycle took ${duration.toFixed(2)}ms`);
      }
    };
  }, [data]);

  // This component doesn't render anything, it's just for managing sync
  return null;
};

export default RealTimeSyncManager;

// Hook for using sync manager
export const useRealTimeSync = (editor: any, isEnabled: boolean = true) => {
  const [syncStatus, setSyncStatus] = React.useState<'syncing' | 'synced' | 'error'>('synced');
  
  return {
    syncStatus,
    SyncManager: () => (
      <RealTimeSyncManager
        editor={editor}
        isEnabled={isEnabled}
        onSyncStatusChange={setSyncStatus}
      />
    )
  };
};
