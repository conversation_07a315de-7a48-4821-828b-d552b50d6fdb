import { useState, useEffect } from 'react';
import { useContractWizard } from './ContractWizardContext';

export function useContractWizardValidation() {
  const { 
    data, 
    currentStep, 
    setCurrentStep, 
    nextStep, 
    prevStep, 
    totalSteps,
    saveContract,
    isLoading,
    error
  } = useContractWizard();
  
  const [canProceed, setCanProceed] = useState(false);

  // Validate current step whenever data or currentStep changes
  useEffect(() => {
    setCanProceed(isStepValid(currentStep));
  }, [data, currentStep]);

  // Check if a specific step is valid
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 0: // Jurisdiction & Type
        return !!data.jurisdiction && !!data.contractType;
      
      case 1: // Parties
        return data.parties.every(party => 
          !!party.name && !!party.address && 
          (party.type === 'individual' || (!!party.representative && !!party.title))
        );
      
      case 2: // Contract Terms
        return !!data.title && !!data.effectiveDate && !!data.description;
      
      case 3: // Legal Clauses
        return data.standardClauses.length > 0;
      
      case 4: // Industry Specific
        return true; // Optional step
      
      case 5: // Attachments
        return true; // Optional step
      
      case 6: // Review & Approval
        return true; // Can proceed to final step regardless
      
      default:
        return false;
    }
  };

  // Check if a step is complete (for stepper UI)
  const isStepComplete = (step: number): boolean => {
    if (step > currentStep) return false;
    return isStepValid(step);
  };

  // Go to next step if validation passes
  const goToNextStep = () => {
    if (canProceed) {
      nextStep();
    }
  };

  // Go to previous step
  const goToPreviousStep = () => {
    prevStep();
  };

  return {
    isStepValid,
    isStepComplete,
    canProceed,
    goToNextStep,
    goToPreviousStep,
    currentStep,
    setCurrentStep,
    totalSteps,
    saveContract,
    isLoading,
    error
  };
}
