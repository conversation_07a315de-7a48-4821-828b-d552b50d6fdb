import React, { useEffect, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useContractWizard } from './ContractWizardContext';
import ContractToolbar from './ContractToolbar';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';


// Function to generate contract content based on data
/**
 * Generate contract HTML content from contract wizard data.
 * @param data - Contract wizard data object
 * @returns HTML string for contract content
 */
function generateContractContent(data: Record<string, unknown>) {
  /**
   * Returns value or fallback underscore string.
   * @param v - value to check
   * @param fallback - fallback string
   */
  const underscore = (v: unknown, fallback = '__________') => (typeof v === 'string' && v.trim() !== '' ? v : fallback);

  // Helper to get the right party display name and info based on type
  /**
   * Returns formatted party display string.
   * @param party - party object
   * @param idx - party index
   */
  const getPartyDisplay = (party: Record<string, unknown> | undefined, idx: number) => {
    if (!party) return `Party ${String.fromCharCode(65 + idx)}`;
    // Type assertions for party fields
    const name = typeof party.name === 'string' ? party.name : undefined;
    const role = typeof party.role === 'string' ? party.role : undefined;
    const address = typeof party.address === 'string' ? party.address : undefined;
    const registration = typeof party.registration === 'string' ? party.registration : undefined;
    const vat = typeof party.vat === 'string' ? party.vat : undefined;
    const division = typeof party.division === 'string' ? party.division : undefined;
    const taxExempt = typeof party.taxExempt === 'string' ? party.taxExempt : undefined;
    const type = typeof party.type === 'string' ? party.type : undefined;

    const partyName = underscore(name, `Party ${String.fromCharCode(65 + idx)}`);
    const partyRole = role ? ` (${role})` : '';

    let partyDetails = '';
    switch (type) {
      case 'individual':
        partyDetails = `an individual having an address at [${underscore(address, 'address')}]`;
        break;
      case 'company':
        partyDetails = `a [business entity] having its principal place of business at [${underscore(address, 'address')}]`;
        if (registration) {
          partyDetails += `, with company registration number ${registration}`;
        }
        if (vat) {
          partyDetails += `, VAT number ${vat}`;
        }
        break;
      case 'government':
        partyDetails = `a governmental entity having its principal place of business at [${underscore(address, 'address')}]`;
        if (division) {
          partyDetails += `, division: ${division}`;
        }
        break;
      case 'nonprofit':
        partyDetails = `a non-profit organization having its principal place of business at [${underscore(address, 'address')}]`;
        if (taxExempt) {
          partyDetails += `, tax-exempt status: ${taxExempt}`;
        }
        break;
      default:
        partyDetails = `having an address at [${underscore(address, 'address')}]`;
    }

    return `<strong>[${partyName}]</strong>, ${partyDetails} ("Party ${String.fromCharCode(65 + idx)}"${partyRole})`;
  };

  /**
   * Returns HTML for industry-specific contract sections.
   */
  const getIndustrySpecificSections = (): string => {
    const contractType = typeof data.contractType === 'string' ? data.contractType : '';
    const industry = typeof data.industry === 'string' ? data.industry : '';
    if (!contractType || !industry) return '';

    // Add industry-specific content here
    const industryContentMap: Record<string, Record<string, string>> = {
      'Technology': {
        'service': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">Technical Requirements</span><br/>
            <span style="margin-left:1.5em;">
              ${underscore(data.technicalRequirements || data.maintenanceSupport || data.serviceLevel || 'N/A')}
            </span>
          </li>
        `,
        'nda': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">Source Code Access</span><br/>
            <span style="margin-left:1.5em;">
              ${underscore(data.sourceCodeAccess ? 'Permitted under this agreement' : 'Not permitted under this agreement')}
            </span>
          </li>
        `
      },
      'Healthcare': {
        'service': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">HIPAA Compliance</span><br/>
            <span style="margin-left:1.5em;">
              ${underscore(data.hipaaCompliance ? 'Compliance with HIPAA regulations is required.' : 'HIPAA compliance is not applicable to this agreement.')}
            </span>
          </li>
        `,
        'nda': `
          <li style="margin-bottom:1.3em; line-height:1.7;">
            <span style="font-weight:bold;">Patient Data Access</span><br/>
            <span style="margin-left:1.5em;">
              Access level: ${underscore(data.patientDataAccess || 'None')}
            </span>
          </li>
        `
      }
    };

    // Get content for the current industry and contract type
    return (industryContentMap[industry] && industryContentMap[industry][contractType]) || '';
  };

  return `
    <div style="text-align:center; margin-bottom: 2.5em; padding-top: 1.5em;">
      <div style="font-size:2em; font-weight:700; margin-bottom:0.2em; letter-spacing:0.05em; text-transform:uppercase;">[${underscore(data.title, 'Contract Title')}]
      </div>
      <div style="font-size:1.1em; color:#334155; font-weight:500; margin-bottom:0.1em; letter-spacing:0.03em;">${underscore(data.contractType, 'Agreement')}</div>
      <div style="font-size:0.98em; color:#64748b; margin-bottom:1.1em; font-style:italic;">Effective Date: ${underscore(data.effectiveDate, new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }))}</div>
    </div>
    <div style="margin-bottom:2.4em; padding-left:0.5em;">
      <div style="font-weight:600; margin-bottom:1.2em; text-align:justify;">THIS AGREEMENT is made and entered into as of <strong>${underscore(data.effectiveDate, 'the Effective Date')}</strong>, by and between the following parties:</div>

      <div style="display:flex; flex-direction:column; gap:1.5em; margin-bottom:1.5em;">
        ${
          Array.isArray(data.parties) && (data.parties as Array<Record<string, unknown>>).length > 0
            ? (data.parties as Array<Record<string, unknown>>).map((party, idx) => `
                <div style="border:1px solid #e5e7eb; padding:1em; background-color:#f9fafb; border-radius:0.25em; box-shadow:0 1px 3px rgba(0,0,0,0.05);">
                  <div style="font-weight:700; text-transform:uppercase; margin-bottom:0.5em; border-bottom:1px solid #e5e7eb; padding-bottom:0.3em;">
                    ${idx === 0 ? 'FIRST PARTY' : 'SECOND PARTY'}:
                  </div>
                  <div style="padding-left:1em;">
                    ${getPartyDisplay(party, idx)}
                  </div>
                </div>
                ${idx === 0 && Array.isArray(data.parties) && (data.parties as Array<Record<string, unknown>>).length > 1 ? '<div style="text-align:center; font-weight:600; margin:0.5em 0;">AND</div>' : ''}
              `).join('')
            : ''
        }
      </div>

      <div style="margin:1.2em 0 0.7em 0; text-align:justify; font-style:italic;">
        ${Array.isArray(data.parties) && (data.parties as Array<Record<string, unknown>>).length > 1
          ? `The First Party and Second Party may be referred to individually as a "Party" and collectively as the "Parties" in this Agreement.`
          : `The First Party shall be referred to as the "Party" in this Agreement.`}
      </div>
    </div>
    <hr style="margin:2.2em 0 2em 0; border-top:1px solid #e5e7eb;"/>
    <div style="margin-bottom:2em; padding-left:0.5em;">
      <div style="font-weight:700; text-transform:uppercase; margin-bottom:1em; letter-spacing:0.05em; font-size:1.2em; text-align:center; color:#1e293b;">1. RECITALS</div>
      <div style="margin-left:1.5em; line-height:1.8; text-align:justify;">
        <p style="margin-bottom:0.8em;">WHEREAS, ${underscore(data.description, '[Contract Description]')};</p>
        <p style="margin-bottom:0.8em;">WHEREAS, the Parties wish to enter into this Agreement to define their respective rights and obligations;</p>
        <p style="margin-bottom:0.8em;">NOW, THEREFORE, in consideration of the mutual covenants and agreements contained herein, and other good and valuable consideration, the receipt and sufficiency of which is hereby acknowledged, the Parties agree as follows:</p>
      </div>
    </div>
    <div style="margin-bottom:2em; padding-left:0.5em;">
      <div style="font-weight:700; text-transform:uppercase; margin-bottom:1em; letter-spacing:0.05em; font-size:1.2em; text-align:center; color:#1e293b;">2. TERMS AND CONDITIONS</div>
      <ol style="padding-left:2.2em; margin-bottom:0;">
        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.1 Term and Termination</div>
          <div style="margin-left:1.5em; text-align:justify;">
            <p style="margin-bottom:0.8em;">This Agreement shall commence on the <strong>Effective Date</strong> of ${underscore(data.effectiveDate, '[date]')} and continue for a period of <strong>${underscore(data.duration, '[duration]')}</strong>, unless earlier terminated in accordance with the provisions set forth herein.</p>
            <p style="margin-bottom:0.8em;">Either Party may terminate this Agreement for cause in the event of a material breach by the other Party, if such breach remains uncured for thirty (30) days following written notice to the breaching Party.</p>
          </div>
        </li>

        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.2 Payment & Deliverables</div>

          <div style="margin-left:1.5em; border:1px solid #e5e7eb; padding:1em; background-color:#f9fafb; border-radius:0.25em; margin-bottom:1em;">
            <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
              <div style="font-weight:600;">Contract Value:</div>
              <div>${underscore(data.contractValue, '[amount]')} ${underscore(data.currency, '[currency]')}</div>
            </div>

            <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
              <div style="font-weight:600;">Payment Terms:</div>
              <div>${underscore(data.paymentTerms, '[payment terms]')}</div>
            </div>

            <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
              <div style="font-weight:600;">Payment Method:</div>
              <div>${underscore(data.paymentMethod, '[payment method]')}</div>
            </div>
          </div>

          <div style="margin-left:1.5em; margin-bottom:0.8em;">
            <div style="font-weight:600; margin-bottom:0.5em;">Deliverables:</div>
            <ul style="list-style-type:disc; margin-left:2em;">
              ${Array.isArray(data.deliverables) && (data.deliverables as string[]).length > 0
                ? (data.deliverables as string[]).map((d) => `<li style='margin-bottom:0.5em; text-align:justify;'>${d}</li>`).join('')
                : '<li>No deliverables specified.</li>'
              }
            </ul>
          </div>
        </li>

        <!-- Industry-specific sections -->
        ${getIndustrySpecificSections()}

        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.3 Legal Provisions</div>
          <div style="margin-left:1.5em;">
            ${(() => {
              const clauseMap: Record<string, { title: string; content: string }> = {
                confidentiality: {
                  title: 'Confidentiality',
                  content: 'Each Party shall maintain the confidentiality of all Confidential Information disclosed to it by the other Party and shall not use such Confidential Information for any purpose other than as expressly permitted under this Agreement. "Confidential Information" means any non-public information that is designated as confidential or that, given the nature of the information or circumstances surrounding its disclosure, reasonably should be considered as confidential.'
                },
                limitation_of_liability: {
                  title: 'Limitation of Liability',
                  content: 'IN NO EVENT SHALL EITHER PARTY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, ARISING OUT OF OR RELATING TO THIS AGREEMENT, WHETHER BASED ON CONTRACT, TORT, NEGLIGENCE, STRICT LIABILITY OR OTHERWISE, EVEN IF SUCH PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.'
                },
                termination: {
                  title: 'Termination for Convenience',
                  content: 'Either Party may terminate this Agreement at any time without cause upon thirty (30) days\' prior written notice to the other Party. In the event of such termination, Client shall pay Contractor for all services performed and expenses incurred up to the effective date of termination.'
                },
                force_majeure: {
                  title: 'Force Majeure',
                  content: 'Neither Party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including but not limited to acts of God, natural disasters, terrorism, riots, or war. The affected Party shall promptly notify the other Party of the force majeure event and use reasonable efforts to resume performance as soon as possible.'
                },
                dispute_resolution: {
                  title: 'Dispute Resolution',
                  content: 'Any dispute arising out of or relating to this Agreement shall first be attempted to be resolved through good faith negotiation between the Parties. If such negotiation fails, the dispute shall be resolved by binding arbitration in accordance with the rules of the American Arbitration Association, and judgment upon the award may be entered in any court having jurisdiction.'
                },
                intellectual_property: {
                  title: 'Intellectual Property Rights',
                  content: 'All intellectual property rights, including patents, trademarks, copyrights, and trade secrets, in and to the deliverables shall be owned exclusively by the Client upon full payment of all fees due under this Agreement. Contractor hereby assigns all right, title, and interest in such intellectual property to Client.'
                },
                nonCompete: {
                  title: 'Non-Competition',
                  content: 'During the term of this Agreement and for a period of one (1) year thereafter, each Party agrees not to engage in any business activity that directly competes with the other Party in relation to the specific subject matter of this Agreement within the geographical area where the other Party conducts business.'
                },
                nonSolicitation: {
                  title: 'Non-Solicitation',
                  content: 'During the term of this Agreement and for a period of one (1) year thereafter, neither Party shall directly or indirectly solicit, hire, or attempt to solicit any employees, contractors, or clients of the other Party without the prior written consent of the other Party.'
                },
                returnOfInfo: {
                  title: 'Return of Information',
                  content: 'Upon termination of this Agreement for any reason, each Party shall promptly return to the other Party all Confidential Information, including all copies, notes, and derivatives thereof, and shall cease all further use of such Confidential Information. Upon request, each Party shall certify in writing that it has complied with this provision.'
                },
                jurisdictionSpecific: {
                  title: 'Governing Law',
                  content: 'This Agreement shall be governed by and construed in accordance with the laws of [Jurisdiction], without giving effect to any choice of law or conflict of law provisions. The Parties consent to the exclusive jurisdiction and venue in the courts of [Jurisdiction] for any disputes arising out of or relating to this Agreement.'
                }
              };

              const clauseItems: string[] = [];
              let clauseNum = 1;

              if (Array.isArray(data.standardClauses) && data.standardClauses.length > 0) {
                (data.standardClauses as string[]).forEach((id) => {
                  const clause = clauseMap[id] || { title: id, content: '' };
                  clauseItems.push(`
                    <div style="margin-bottom:1.5em; border-left:3px solid #e5e7eb; padding-left:1em;">
                      <div style="font-weight:600; margin-bottom:0.5em; color:#1e293b;">2.3.${clauseNum} ${clause.title}</div>
                      <div style="text-align:justify; line-height:1.6;">${clause.content}</div>
                    </div>
                  `);
                  clauseNum++;
                });
              }

              if (Array.isArray(data.libraryClauses) && data.libraryClauses.length > 0) {
                (data.libraryClauses as Array<{ title: string; content: string }>).forEach((clause) => {
                  clauseItems.push(`
                    <div style="margin-bottom:1.5em; border-left:3px solid #e5e7eb; padding-left:1em;">
                      <div style="font-weight:600; margin-bottom:0.5em; color:#1e293b;">2.3.${clauseNum} ${clause.title}</div>
                      <div style="text-align:justify; line-height:1.6;">${clause.content}</div>
                    </div>
                  `);
                  clauseNum++;
                });
              }

              if (Array.isArray(data.customClauses) && data.customClauses.length > 0) {
                (data.customClauses as Array<{ title?: string; content?: string } | string>).forEach((c) => {
                  const title = typeof c === 'string' ? `Custom Clause ${clauseNum}` : c.title || `Custom Clause ${clauseNum}`;
                  const content = typeof c === 'string' ? c : c.content || '';
                  clauseItems.push(`
                    <div style="margin-bottom:1.5em; border-left:3px solid #e5e7eb; padding-left:1em;">
                      <div style="font-weight:600; margin-bottom:0.5em; color:#1e293b;">2.3.${clauseNum} ${title}</div>
                      <div style="text-align:justify; line-height:1.6;">${content}</div>
                    </div>
                  `);
                  clauseNum++;
                });
              }

              if (clauseItems.length === 0) {
                return '<div style="font-style:italic; color:#6b7280; margin:1em 0;">No legal clauses have been selected for this contract.</div>';
              }

              return clauseItems.join('');
            })()}
          </div>
        </li>
        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.4 Attachments</div>
          <div style="margin-left:1.5em;">
            ${
              Array.isArray(data.attachments) && (data.attachments as Array<{ name?: string; type?: string; description?: string }> ).length > 0
                ? `
                  <div style="margin-bottom:1em;">The following documents are attached to and form an integral part of this Agreement:</div>
                  <div style="border:1px solid #e5e7eb; border-radius:0.25em; overflow:hidden;">
                    <table style="width:100%; border-collapse:collapse; text-align:left;">
                      <thead>
                        <tr style="background-color:#f8fafc; border-bottom:1px solid #e5e7eb;">
                          <th style="padding:0.75em; font-weight:600;">Attachment Name</th>
                          <th style="padding:0.75em; font-weight:600;">Type</th>
                          <th style="padding:0.75em; font-weight:600;">Description</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${(Array.isArray(data.attachments) ? data.attachments as Array<{ name?: string; type?: string; description?: string }> : []).map((a) => `
                          <tr style="border-bottom:1px solid #e5e7eb;">
                            <td style="padding:0.75em;">${a.name || 'Untitled'}</td>
                            <td style="padding:0.75em;">${a.type || 'Document'}</td>
                            <td style="padding:0.75em;">${a.description || 'No description provided'}</td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                `
                : '<div style="font-style:italic; color:#6b7280;">No attachments have been added to this contract.</div>'
            }
          </div>
        </li>

        <li style="margin-bottom:2em; line-height:1.7;">
          <div style="font-weight:600; margin-bottom:0.8em; color:#1e293b;">2.5 Approval Workflow</div>
          <div style="margin-left:1.5em;">
            <div style="margin-bottom:1em;">This Agreement requires approval from the following individuals:</div>

            ${
              Array.isArray(data.approvers) && (data.approvers as Array<{ name?: string; role?: string }> ).length > 0
                ? `
                  <div style="border:1px solid #e5e7eb; border-radius:0.25em; overflow:hidden; margin-bottom:1.5em;">
                    <table style="width:100%; border-collapse:collapse; text-align:left;">
                      <thead>
                        <tr style="background-color:#f8fafc; border-bottom:1px solid #e5e7eb;">
                          <th style="padding:0.75em; font-weight:600;">Name</th>
                          <th style="padding:0.75em; font-weight:600;">Role</th>
                          <th style="padding:0.75em; font-weight:600;">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${(Array.isArray(data.approvers) ? data.approvers as Array<{ name?: string; role?: string }> : []).map((a) => `
                          <tr style="border-bottom:1px solid #e5e7eb;">
                            <td style="padding:0.75em;">${a.name || 'Unnamed Approver'}</td>
                            <td style="padding:0.75em;">${a.role || 'Reviewer'}</td>
                            <td style="padding:0.75em;">Pending</td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                `
                : '<div style="font-style:italic; color:#6b7280; margin-bottom:1.5em;">No approvers have been designated for this contract.</div>'
            }

            <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
              <div style="font-weight:600;">Approval Process:</div>
              <div>${underscore(data.approvalProcess, 'Sequential approval process')}</div>
            </div>
          </div>
        </li>
      </ol>
    </div>
    <hr style="margin:3em 0 2em 0; border-top:1px solid #e5e7eb;"/>
    <div style="margin-top:2em; padding-bottom:1em;">
      <div style="margin-bottom:2em; font-size:1.1em; text-align:center; font-weight:600;">IN WITNESS WHEREOF</div>
      <div style="margin-bottom:2em; text-align:justify; line-height:1.6;">
        The Parties hereto have executed this Agreement as of the Effective Date first written above, by their duly authorized representatives.
      </div>

      <div style="display:flex; flex-wrap:wrap; justify-content:space-between; margin-top:3em; gap:3em;">
        ${Array.isArray(data.parties) && (data.parties as Array<Record<string, unknown>>).map((party, idx) => {
          const name = typeof party.name === 'string' ? party.name : undefined;
          const type = typeof party.type === 'string' ? party.type : undefined;
          const title = typeof party.title === 'string' ? party.title : undefined;
          const representative = typeof party.representative === 'string' ? party.representative : undefined;
          const representativeTitle = title || (type === 'individual' ? '' : 'Authorized Representative');
          return `
            <div style="flex:1; min-width:220px; border:1px solid #e5e7eb; padding:1.5em; border-radius:0.25em; background-color:#f9fafb;">
              <div style="font-weight:700; text-transform:uppercase; margin-bottom:1em; font-size:0.9em; color:#475569; border-bottom:1px solid #e5e7eb; padding-bottom:0.5em;">
                ${idx === 0 ? 'FIRST PARTY' : 'SECOND PARTY'}
              </div>

              <div style="margin-bottom:3em;">
                <div style="font-weight:600; margin-bottom:0.3em;">Signature:</div>
                <div style="border-bottom:1px solid #000; height:1.5em;"></div>
              </div>

              <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
                <div style="font-weight:600;">Name:</div>
                <div>${underscore(name, '[Full Name]')}</div>
              </div>

              ${representative ? `
                <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
                  <div style="font-weight:600;">By:</div>
                  <div>${underscore(representative, '[Representative Name]')}</div>
                </div>
              ` : ''}

              ${representativeTitle ? `
                <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
                  <div style="font-weight:600;">Title:</div>
                  <div>${underscore(representativeTitle, '[Title]')}</div>
                </div>
              ` : ''}

              <div style="display:grid; grid-template-columns:1fr 2fr; gap:0.5em; margin-bottom:0.8em;">
                <div style="font-weight:600;">Date:</div>
                <div>____________________</div>
              </div>
            </div>
          `;
        }).join('')}
      </div>
    </div>
  `;
}

/**
 * ContractEditor component displays a rich text contract editor with toolbar and contract preview.
 */
const ContractEditor: React.FC = () => {
  const { data, saveDraft, loadDraft } = useContractWizard();
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({ types: ['heading', 'paragraph'] })
    ],
    content: generateContractContent(data),
    editable: true,
  });

  useEffect(() => {
    if (editor) {
      editor.commands.setContent(generateContractContent(data));
    }
  }, [data, editor]);

  // Toolbar command handler
  /**
   * Handles toolbar commands for the editor.
   * @param command - Command string
   * @param value - Optional value (not used)
   */
  const handleToolbarCommand = useCallback((command: string) => {
    if (!editor) return;
    switch (command) {
      case 'bold':
        editor.chain().focus().toggleBold().run(); break;
      case 'italic':
        editor.chain().focus().toggleItalic().run(); break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run(); break;
      case 'bulletList':
        editor.chain().focus().toggleBulletList().run(); break;
      case 'orderedList':
        editor.chain().focus().toggleOrderedList().run(); break;
      case 'alignLeft':
        editor.chain().focus().setTextAlign('left').run(); break;
      case 'alignCenter':
        editor.chain().focus().setTextAlign('center').run(); break;
      case 'alignRight':
        editor.chain().focus().setTextAlign('right').run(); break;
      case 'undo':
        editor.chain().focus().undo().run(); break;
      case 'redo':
        editor.chain().focus().redo().run(); break;
      default:
        break;
    }
  }, [editor]);

  return (
    <div className="flex flex-col gap-10">
      {/* Editor Section */}
      <div className="bg-white dark:bg-slate-900 text-foreground rounded-md border shadow-sm p-0 min-h-[70vh] max-h-[80vh] relative">
        {/* Fixed toolbar at the top */}
        <div className="sticky top-0 z-50 bg-white dark:bg-slate-900 border-b border-muted shadow-sm w-full">
          <ContractToolbar
            onCommand={handleToolbarCommand}
            canUndo={!!editor?.can().undo()}
            canRedo={!!editor?.can().redo()}
            onSave={saveDraft}
            onLoad={loadDraft}
          />
        </div>

        {/* Scrollable content area with padding */}
        <div className="overflow-y-auto p-4 max-h-[calc(80vh-48px)]">
          <div className="mx-auto max-w-4xl">
            <div style={{
              position: 'relative',
              backgroundColor: 'white',
              padding: '2.5rem 1rem 1rem',
              boxShadow: '0 4px 24px rgba(0, 0, 0, 0.08)',
              border: '1px solid #e5e7eb',
              margin: '2rem auto',
              maxWidth: '100%',
              minHeight: '11in',
              fontFamily: '"Libre Baskerville", "Times New Roman", serif'
            }}>
              {/* Watermark for draft */}
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%) rotate(-45deg)',
                fontSize: '6rem',
                color: 'rgba(229, 231, 235, 0.3)',
                pointerEvents: 'none',
                zIndex: 0,
                fontFamily: '"EB Garamond", serif',
                whiteSpace: 'nowrap'
              }}>DRAFT</div>

              {/* Document Number */}
              <div style={{
                position: 'absolute',
                top: '0.75rem',
                right: '1rem',
                fontSize: '0.75rem',
                color: '#6b7280'
              }}>Contract No.: CONT-{Math.floor(10000 + Math.random() * 90000)}-{new Date().getFullYear()}</div>

              <EditorContent
                editor={editor}
                className="prose dark:prose-invert prose-sm sm:prose-base max-w-none prose-headings:font-semibold prose-h2:text-center prose-h2:text-lg sm:prose-h2:text-xl prose-h3:text-base prose-p:my-1.5 mt-2"
                style={{
                  fontFamily: '"Libre Baskerville", "Times New Roman", serif',
                  lineHeight: 1.5,
                  color: '#111827',
                  textRendering: 'optimizeLegibility'
                }}
              />

              {/* Page Number */}
              <div style={{
                position: 'absolute',
                bottom: '0.5rem',
                right: '1rem',
                fontSize: '0.75rem',
                color: '#6b7280'
              }}>Page 1 of 1</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractEditor;