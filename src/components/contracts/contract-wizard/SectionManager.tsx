import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Move, 
  Plus, 
  Trash2, 
  Edit3, 
  Save, 
  X, 
  ChevronUp, 
  ChevronDown,
  Eye,
  EyeOff,
  Copy,
  Settings
} from 'lucide-react';

interface ContractSection {
  id: string;
  title: string;
  content: string;
  level: number; // 1-6 for h1-h6
  order: number;
  isVisible: boolean;
  isEditable: boolean;
  type: 'standard' | 'custom' | 'generated';
  tags?: string[];
}

interface SectionManagerProps {
  editor: any; // TipTap editor instance
  onSectionChange?: (sections: ContractSection[]) => void;
  className?: string;
}

/**
 * Advanced Section Manager for Contract Documents
 * - Drag and drop reordering
 * - Section visibility controls
 * - Custom section creation
 * - Section templates and presets
 * - Hierarchical section management
 */
const SectionManager: React.FC<SectionManagerProps> = ({
  editor,
  onSectionChange,
  className = ''
}) => {
  const [sections, setSections] = useState<ContractSection[]>([]);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [newSectionTitle, setNewSectionTitle] = useState('');
  const [newSectionLevel, setNewSectionLevel] = useState<number>(2);
  const [draggedSection, setDraggedSection] = useState<string | null>(null);
  const [showHidden, setShowHidden] = useState(false);

  // Extract sections from editor content
  const extractSections = useCallback(() => {
    if (!editor) return [];

    const content = editor.getHTML();
    const sectionRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/g;
    const extractedSections: ContractSection[] = [];
    let match;
    let order = 0;

    while ((match = sectionRegex.exec(content)) !== null) {
      const level = parseInt(match[1]);
      const title = match[2].replace(/<[^>]*>/g, '').trim();
      
      if (title) {
        extractedSections.push({
          id: `section-${order}`,
          title,
          content: match[0],
          level,
          order,
          isVisible: true,
          isEditable: true,
          type: 'generated',
          tags: []
        });
        order++;
      }
    }

    return extractedSections;
  }, [editor]);

  // Update sections when editor content changes
  useEffect(() => {
    if (editor) {
      const handleUpdate = () => {
        const newSections = extractSections();
        setSections(newSections);
        onSectionChange?.(newSections);
      };

      editor.on('update', handleUpdate);
      handleUpdate(); // Initial extraction

      return () => {
        editor.off('update', handleUpdate);
      };
    }
  }, [editor, extractSections, onSectionChange]);

  // Add new section
  const addSection = () => {
    if (!newSectionTitle.trim() || !editor) return;

    const newSection: ContractSection = {
      id: `custom-${Date.now()}`,
      title: newSectionTitle,
      content: `<h${newSectionLevel}>${newSectionTitle}</h${newSectionLevel}>`,
      level: newSectionLevel,
      order: sections.length,
      isVisible: true,
      isEditable: true,
      type: 'custom',
      tags: ['custom']
    };

    // Insert at cursor position or at the end
    const headingHTML = `<h${newSectionLevel}>${newSectionTitle}</h${newSectionLevel}><p></p>`;
    editor.chain().focus().insertContent(headingHTML).run();

    setNewSectionTitle('');
  };

  // Delete section
  const deleteSection = (sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section || !editor) return;

    // Find and remove the section from editor
    const content = editor.getHTML();
    const updatedContent = content.replace(section.content, '');
    editor.commands.setContent(updatedContent);
  };

  // Toggle section visibility
  const toggleSectionVisibility = (sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section || !editor) return;

    const content = editor.getHTML();
    if (section.isVisible) {
      // Hide section by wrapping in hidden div
      const hiddenContent = content.replace(
        section.content,
        `<div style="display: none;" data-section-id="${sectionId}">${section.content}</div>`
      );
      editor.commands.setContent(hiddenContent);
    } else {
      // Show section by removing hidden wrapper
      const visibleContent = content.replace(
        `<div style="display: none;" data-section-id="${sectionId}">${section.content}</div>`,
        section.content
      );
      editor.commands.setContent(visibleContent);
    }

    setSections(prev => prev.map(s => 
      s.id === sectionId ? { ...s, isVisible: !s.isVisible } : s
    ));
  };

  // Duplicate section
  const duplicateSection = (sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section || !editor) return;

    const duplicatedContent = section.content.replace(
      section.title,
      `${section.title} (Copy)`
    );

    editor.chain().focus().insertContent(duplicatedContent + '<p></p>').run();
  };

  // Move section up/down
  const moveSection = (sectionId: string, direction: 'up' | 'down') => {
    const sectionIndex = sections.findIndex(s => s.id === sectionId);
    if (sectionIndex === -1) return;

    const targetIndex = direction === 'up' ? sectionIndex - 1 : sectionIndex + 1;
    if (targetIndex < 0 || targetIndex >= sections.length) return;

    // Reorder sections in editor
    reorderSections(sectionIndex, targetIndex);
  };

  // Reorder sections
  const reorderSections = (fromIndex: number, toIndex: number) => {
    if (!editor || fromIndex === toIndex) return;

    const content = editor.getHTML();
    const sectionContents = sections.map(s => s.content);
    
    // Swap sections
    const temp = sectionContents[fromIndex];
    sectionContents[fromIndex] = sectionContents[toIndex];
    sectionContents[toIndex] = temp;

    // Rebuild content
    let newContent = content;
    sections.forEach((section, index) => {
      newContent = newContent.replace(section.content, `__PLACEHOLDER_${index}__`);
    });

    sectionContents.forEach((content, index) => {
      newContent = newContent.replace(`__PLACEHOLDER_${index}__`, content);
    });

    editor.commands.setContent(newContent);
  };

  // Handle drag and drop
  const handleDragStart = (sectionId: string) => {
    setDraggedSection(sectionId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetSectionId: string) => {
    e.preventDefault();
    
    if (!draggedSection || draggedSection === targetSectionId) return;

    const fromIndex = sections.findIndex(s => s.id === draggedSection);
    const toIndex = sections.findIndex(s => s.id === targetSectionId);

    if (fromIndex !== -1 && toIndex !== -1) {
      reorderSections(fromIndex, toIndex);
    }

    setDraggedSection(null);
  };

  // Edit section title
  const editSectionTitle = (sectionId: string, newTitle: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section || !editor) return;

    const content = editor.getHTML();
    const updatedContent = content.replace(
      section.content,
      section.content.replace(section.title, newTitle)
    );

    editor.commands.setContent(updatedContent);
    setEditingSection(null);
  };

  const visibleSections = showHidden ? sections : sections.filter(s => s.isVisible);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Section Manager</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHidden(!showHidden)}
            >
              {showHidden ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
            <Badge variant="outline">{sections.length} sections</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add New Section */}
        <div className="space-y-2 p-3 border rounded-lg bg-muted/50">
          <Label className="text-sm font-medium">Add New Section</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Section title..."
              value={newSectionTitle}
              onChange={(e) => setNewSectionTitle(e.target.value)}
              className="flex-1"
            />
            <Select
              value={newSectionLevel.toString()}
              onValueChange={(value) => setNewSectionLevel(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">H1</SelectItem>
                <SelectItem value="2">H2</SelectItem>
                <SelectItem value="3">H3</SelectItem>
                <SelectItem value="4">H4</SelectItem>
                <SelectItem value="5">H5</SelectItem>
                <SelectItem value="6">H6</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={addSection} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Section List */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {visibleSections.map((section, index) => (
            <div
              key={section.id}
              className={`p-3 border rounded-lg cursor-move hover:bg-muted/50 transition-colors ${
                draggedSection === section.id ? 'opacity-50' : ''
              } ${!section.isVisible ? 'opacity-60 border-dashed' : ''}`}
              draggable
              onDragStart={() => handleDragStart(section.id)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, section.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1">
                  <Move className="h-4 w-4 text-muted-foreground" />
                  <Badge variant="outline" className="text-xs">
                    H{section.level}
                  </Badge>
                  
                  {editingSection === section.id ? (
                    <div className="flex items-center gap-2 flex-1">
                      <Input
                        defaultValue={section.title}
                        className="h-8"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            editSectionTitle(section.id, e.currentTarget.value);
                          } else if (e.key === 'Escape') {
                            setEditingSection(null);
                          }
                        }}
                        autoFocus
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingSection(null)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <span 
                      className="font-medium truncate flex-1 cursor-pointer"
                      onClick={() => setEditingSection(section.id)}
                    >
                      {section.title}
                    </span>
                  )}

                  {section.type === 'custom' && (
                    <Badge variant="secondary" className="text-xs">Custom</Badge>
                  )}
                </div>

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => moveSection(section.id, 'up')}
                    disabled={index === 0}
                  >
                    <ChevronUp className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => moveSection(section.id, 'down')}
                    disabled={index === visibleSections.length - 1}
                  >
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleSectionVisibility(section.id)}
                  >
                    {section.isVisible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => duplicateSection(section.id)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteSection(section.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))}

          {visibleSections.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No sections found</p>
              <p className="text-sm">Add headings to your document to manage sections</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SectionManager;
