import React, { createContext, useContext, useState, useEffect } from 'react';
import { contractTemplates, ContractTemplate } from './contractTemplates';
import { useApi } from '@/lib/api';
import { ContractService, TemplateService } from '@/services/api-services';
import type { ContractCreate, Template as ApiTemplate } from '@/services/api-types';
import { useToast } from '@/components/ui/use-toast';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';

// Define the shape of the contract data
export interface Party {
  type: string;
  name: string;
  address: string;
  representative: string;
  title: string;
  role?: string;
  [key: string]: any; // Allow for dynamic fields based on party type
}

export interface ContractData {
  jurisdiction: string;
  contractType: string;
  title: string;
  effectiveDate: string;
  duration: string;
  description: string;
  parties: Party[];
  scope: string;
  paymentTerms: string;
  contractValue: string;
  currency: string;
  paymentMethod: string;
  deliverables: any[];
  standardClauses: string[];
  customClauses: any[];
  libraryClauses: { title: string; content: string }[];
  attachments: any[];
  approvers: any[];
  approvalProcess?: 'sequential' | 'parallel';
  // Added for optional clauses step compatibility
  optionalClauses: string[];
  customOptionalClauses: string[];
  customFields?: any[]; // Added for user-defined custom fields
  industry?: string; // Added for industry-specific adaptability
  // For imported document content
  importedContent?: string;
  // Additional properties for dynamic fields
  [key: string]: any;
}

const defaultData: ContractData = {
  jurisdiction: '',
  contractType: '',
  title: '',
  effectiveDate: '',
  duration: '',
  description: '',
  parties: [
    { type: 'individual', name: '', address: '', representative: '', title: '', role: '' },
    { type: 'company', name: '', address: '', representative: '', title: '', role: '' },
  ],
  scope: '',
  paymentTerms: '',
  contractValue: '',
  currency: 'USD',
  paymentMethod: '',
  deliverables: [],
  standardClauses: [],
  customClauses: [],
  libraryClauses: [],
  attachments: [],
  approvers: [],
  approvalProcess: 'sequential',
  // Added for optional clauses step compatibility
  optionalClauses: [],
  customOptionalClauses: [],
  customFields: [], // Added for user-defined custom fields
  industry: '', // Added for industry-specific adaptability
};

interface ContractWizardContextType {
  data: ContractData;
  setData: React.Dispatch<React.SetStateAction<ContractData>>;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  totalSteps: number;
  saveDraft: () => void;
  loadDraft: () => void;
  clearDraft: () => void;
  selectedTemplate: ContractTemplate | null;
  setSelectedTemplate: React.Dispatch<React.SetStateAction<ContractTemplate | null>>;
  selectTemplateById: (templateId: string) => void;
  saveContract: () => Promise<string | null>;
  isLoading: boolean;
  error: string | null;
}

const ContractWizardContext = createContext<ContractWizardContextType | undefined>(undefined);

const DRAFT_KEY = 'contract-wizard-draft';

export const ContractWizardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { fetch, fetchArray } = useApi();
  const { toast } = useToast();
  const { currentWorkspace } = useClerkWorkspace();

  const [data, setData] = useState<ContractData>(defaultData);
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState<ContractTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiTemplates, setApiTemplates] = useState<ApiTemplate[]>([]);

  const totalSteps = 7;

  // Fetch templates from API
  useEffect(() => {
    const fetchTemplates = async () => {
      if (!currentWorkspace?.id) return;

      try {
        const result = await fetchArray(
          () => TemplateService.getTemplates({ workspace_id: currentWorkspace.id }),
          "Loading templates...",
          "Failed to load templates"
        );

        if (result && result.length > 0) {
          setApiTemplates(result);
        }
      } catch (err) {
        console.error("Error fetching templates:", err);
      }
    };

    fetchTemplates();
  }, [currentWorkspace?.id, fetch]);

  const nextStep = () => setCurrentStep((s) => Math.min(s + 1, totalSteps - 1));
  const prevStep = () => setCurrentStep((s) => Math.max(s - 1, 0));
  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setCurrentStep(step);
    }
  };

  // Select template by id
  const selectTemplateById = (templateId: string) => {
    // First check API templates
    const apiTemplate = apiTemplates.find(t => t.id === templateId);
    if (apiTemplate) {
      // Map API template to local template format
      const mappedTemplate: ContractTemplate = {
        id: apiTemplate.id,
        label: apiTemplate.title,
        description: apiTemplate.description,
        industry: apiTemplate.industry ? [apiTemplate.industry] : undefined,
        fields: [], // Would need to map from apiTemplate.content
        parties: {
          min: 2,
          max: 5,
          types: ['individual', 'company']
        },
        clauses: [] // Would need to map from apiTemplate.content
      };

      setSelectedTemplate(mappedTemplate);

      // Update data based on template
      setData(prevData => ({
        ...prevData,
        title: apiTemplate.title,
        description: apiTemplate.description,
        contractType: apiTemplate.type,
        industry: apiTemplate.industry || '',
        // Other fields would be populated from apiTemplate.content
      }));

      return;
    }

    // Fall back to local templates if not found in API
    const localTemplate = contractTemplates.find(t => t.id === templateId);
    setSelectedTemplate(localTemplate || null);

    // Update data fields based on template
    if (localTemplate) {
      setData(prevData => ({
        ...prevData,
        title: localTemplate.label,
        description: localTemplate.description,
        industry: localTemplate.industry?.[0] || '',
        // Other fields would be populated based on the template
      }));
    }
  };

  // Save contract to API
  const saveContract = async (): Promise<string | null> => {
    if (!currentWorkspace?.id) {
      toast({
        title: "Error",
        description: "No workspace selected. Please select a workspace first.",
        variant: "destructive",
      });
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Map local data to API contract format
      const contractData: ContractCreate = {
        title: data.title,
        type: data.contractType,
        jurisdiction: data.jurisdiction,
        effective_date: data.effectiveDate,
        expiry_date: data.duration ? new Date(new Date(data.effectiveDate).getTime() + parseInt(data.duration) * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
        description: data.description,
        value: data.contractValue,
        currency: data.currency,
        status: 'draft',
        workspace_id: currentWorkspace.id,
        parties: data.parties.map(party => ({
          type: party.type,
          name: party.name,
          address: party.address,
          representative: party.representative,
          title: party.title,
          role: party.role
        })),
        clauses: [
          ...data.standardClauses.map(clause => ({ title: clause, content: '' })),
          ...data.customClauses.map(clause => ({ title: clause, content: '' })),
          ...data.libraryClauses
        ],
        approvers: data.approvers.map(approver => ({
          user_id: approver.id,
          name: approver.name,
          email: approver.email,
          status: 'pending'
        })),
        approval_process: data.approvalProcess,
        tags: data.industry ? [data.industry] : undefined,
        custom_fields: data.customFields?.length ? data.customFields.reduce((acc, field) => {
          acc[field.name] = field.value;
          return acc;
        }, {} as Record<string, any>) : undefined
      };

      const result = await fetch(
        () => ContractService.createContract(contractData),
        "Saving contract...",
        "Failed to save contract"
      );

      if (result) {
        toast({
          title: "Success",
          description: "Contract saved successfully.",
        });

        // Clear draft after successful save
        clearDraft();

        return result.id;
      }

      return null;
    } catch (err) {
      console.error("Error saving contract:", err);
      setError("Failed to save contract. Please try again.");

      toast({
        title: "Error",
        description: "Failed to save contract. Please try again.",
        variant: "destructive",
      });

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Save draft to localStorage
  const saveDraft = () => {
    localStorage.setItem(DRAFT_KEY, JSON.stringify({
      data,
      currentStep,
      selectedTemplateId: selectedTemplate?.id,
      workspaceId: currentWorkspace?.id
    }));

    toast({
      title: "Draft Saved",
      description: "Your contract draft has been saved locally.",
    });
  };

  // Load draft from localStorage
  const loadDraft = () => {
    const draft = localStorage.getItem(DRAFT_KEY);
    if (draft) {
      try {
        const parsed = JSON.parse(draft);
        if (parsed.data) setData(parsed.data);
        if (typeof parsed.currentStep === 'number') setCurrentStep(parsed.currentStep);
        if (parsed.selectedTemplateId) selectTemplateById(parsed.selectedTemplateId);

        toast({
          title: "Draft Loaded",
          description: "Your saved draft has been loaded.",
        });
      } catch (err) {
        console.error("Error loading draft:", err);
        toast({
          title: "Error",
          description: "Failed to load draft. The saved data may be corrupted.",
          variant: "destructive",
        });
      }
    }
  };

  // Clear draft from localStorage
  const clearDraft = () => {
    localStorage.removeItem(DRAFT_KEY);

    toast({
      title: "Draft Cleared",
      description: "Your saved draft has been cleared.",
    });
  };

  return (
    <ContractWizardContext.Provider value={{
      data,
      setData,
      currentStep,
      setCurrentStep,
      nextStep,
      prevStep,
      goToStep,
      totalSteps,
      saveDraft,
      loadDraft,
      clearDraft,
      selectedTemplate,
      setSelectedTemplate,
      selectTemplateById,
      saveContract,
      isLoading,
      error
    }}>
      {children}
    </ContractWizardContext.Provider>
  );
};

export function useContractWizard() {
  const ctx = useContext(ContractWizardContext);
  if (!ctx) throw new Error('useContractWizard must be used within ContractWizardProvider');
  return ctx;
}