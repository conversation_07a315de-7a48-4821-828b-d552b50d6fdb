import React from 'react';
import { useContractWizardValidation } from './useContractWizardValidation';
import { cn } from '@/lib/utils';

// Step names for better UX
const stepNames = [
  'Jurisdiction',
  'Parties',
  'Terms',
  'Clauses',
  'Industry',
  'Attachments',
  'Review & Approval'
];

const WizardStepper: React.FC = () => {
  const { currentStep, totalSteps, setCurrentStep, isStepComplete } = useContractWizardValidation();

  // Create an array of step numbers
  const steps = Array.from({ length: totalSteps }, (_, i) => i);

  // Handle step click
  const handleStepClick = (step: number) => {
    setCurrentStep(step);
  };

  return (
    <div className="mb-4">
      {/* Horizontal stepper based on the screenshot */}
      <div className="bg-slate-100 dark:bg-slate-800/50 rounded-md py-2.5 px-4">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium text-slate-600 dark:text-slate-300">
            Step {currentStep + 1} of {totalSteps}
          </span>
          <span className="text-xs font-medium text-slate-600 dark:text-slate-300">
            {stepNames[currentStep]}
          </span>
        </div>

        <div className="flex items-center space-x-1 mt-1.5">
          {steps.map((step) => (
            <div
              key={step}
              onClick={() => handleStepClick(step)}
              className={cn(
                "relative h-1.5 rounded-full transition-all flex-1 cursor-pointer hover:opacity-80 group",
                step === currentStep
                  ? "bg-slate-800 dark:bg-slate-200"
                  : step < currentStep && isStepComplete(step)
                    ? "bg-green-600 dark:bg-green-500"
                    : "bg-slate-300 dark:bg-slate-700"
              )}
              role="button"
              aria-label={`Go to step ${step + 1}: ${stepNames[step]}`}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  handleStepClick(step);
                }
              }}
            >
              <div className="absolute opacity-0 group-hover:opacity-100 bottom-full mb-1.5 left-1/2 transform -translate-x-1/2 bg-slate-800 dark:bg-slate-700 text-white text-[10px] px-1.5 py-0.5 rounded whitespace-nowrap transition-opacity">
                {stepNames[step]}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WizardStepper;