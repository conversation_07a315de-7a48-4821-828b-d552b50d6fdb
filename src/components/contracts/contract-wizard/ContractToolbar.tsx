import React from 'react';
import { Bold, Italic, Underline, List, ListOrdered, Undo, Redo, AlignLeft, AlignCenter, AlignRight, Save, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ContractToolbarProps {
  onCommand: (command: string, value?: any) => void;
  canUndo: boolean;
  canRedo: boolean;
  onSave: () => void;
  onLoad: () => void;
}

const ContractToolbar: React.FC<ContractToolbarProps> = ({ onCommand, canUndo, canRedo, onSave, onLoad }) => {
  return (
    <div className="flex flex-wrap gap-1 items-center border-b bg-muted px-2 py-1 rounded-t-md">
      <Button size="icon" variant="ghost" aria-label="Bold" onClick={() => onCommand('bold')}>
        <Bold className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Italic" onClick={() => onCommand('italic')}>
        <Italic className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Underline" onClick={() => onCommand('underline')}>
        <Underline className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Bulleted List" onClick={() => onCommand('bulletList')}>
        <List className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Numbered List" onClick={() => onCommand('orderedList')}>
        <ListOrdered className="w-4 h-4" />
      </Button>
      <span className="mx-2 border-l h-5" />
      <Button size="icon" variant="ghost" aria-label="Align Left" onClick={() => onCommand('alignLeft')}>
        <AlignLeft className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Align Center" onClick={() => onCommand('alignCenter')}>
        <AlignCenter className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Align Right" onClick={() => onCommand('alignRight')}>
        <AlignRight className="w-4 h-4" />
      </Button>
      <span className="mx-2 border-l h-5" />
      <Button size="icon" variant="ghost" aria-label="Undo" disabled={!canUndo} onClick={() => onCommand('undo')}>
        <Undo className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Redo" disabled={!canRedo} onClick={() => onCommand('redo')}>
        <Redo className="w-4 h-4" />
      </Button>
      <span className="mx-2 border-l h-5" />
      <Button size="icon" variant="ghost" aria-label="Save" onClick={onSave}>
        <Save className="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" aria-label="Load" onClick={onLoad}>
        <Upload className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default ContractToolbar;
