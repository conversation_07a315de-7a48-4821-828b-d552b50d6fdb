import React from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Plus, Trash2 } from 'lucide-react';
import DynamicFieldRenderer from './DynamicFieldRenderer';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const Step3ContractTerms: React.FC = () => {
  const { data, setData, nextStep, prevStep, selectedTemplate } = useContractWizard();
  const [touched, setTouched] = React.useState(false);
  const [customFieldModal, setCustomFieldModal] = React.useState(false);
  const [newField, setNewField] = React.useState({ label: '', type: 'text', placeholder: '' });

  // Only show fields defined in the selected template for this step
  // Filter out general info fields already present in other steps
  const forbiddenKeys = ['title', 'contractTitle', 'effectiveDate', 'jurisdiction', 'duration'];
  const fields = (selectedTemplate?.fields || []).filter(f => !forbiddenKeys.includes(f.key));
  const customFields = data.customFields || [];

  // Validate required fields from both template and custom fields
  const allFields = [...fields, ...customFields];
  const isValid = allFields.every(field => {
    if (!field.required) return true;
    const val = data[field.key];
    return typeof val === 'string' ? val.trim() !== '' : val !== undefined && val !== null;
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);
    if (isValid) nextStep();
  };

  const handleAddCustomField = () => {
    if (!newField.label.trim() || !newField.type) return;
    const key = `custom_${newField.label.replace(/\s+/g, '_').toLowerCase()}_${Date.now()}`;
    const field = { ...newField, key };
    setData(prev => ({
      ...prev,
      customFields: [...(prev.customFields || []), field],
      [key]: '',
    }));
    setNewField({ label: '', type: 'text', placeholder: '' });
    setCustomFieldModal(false);
  };

  const handleRemoveCustomField = (key: string) => {
    setData(prev => {
      const updatedFields = (prev.customFields || []).filter((f: any) => f.key !== key);
      const newData = { ...prev };
      delete newData[key];
      return { ...newData, customFields: updatedFields };
    });
  };

  if (!selectedTemplate) {
    return <div className="text-red-600">Please select a contract type first.</div>;
  }

  return (
    <form className="space-y-3" onSubmit={handleSubmit}>
      {/* Tabbed layout for contract terms */}
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="mb-3 bg-slate-100 dark:bg-slate-800/50 p-0.5 rounded-md">
          <TabsTrigger value="general" className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:shadow-sm text-xs py-1 h-7">General Terms</TabsTrigger>
          <TabsTrigger value="custom" className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:shadow-sm text-xs py-1 h-7">Custom Terms</TabsTrigger>
        </TabsList>
        {/* General Terms Tab */}
        <TabsContent value="general">
          <DynamicFieldRenderer fields={fields} data={data} setData={setData} />
        </TabsContent>
        {/* Custom Terms Tab */}
        <TabsContent value="custom">
          <div className="max-w-xl">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-sm font-medium">Custom Fields</span>
              <Button
                type="button"
                variant="outline"
                onClick={() => setCustomFieldModal(true)}
                className="h-6 px-2 bg-white border-slate-200 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Custom Field
              </Button>
            </div>
            {customFields.length === 0 && <div className="text-xs text-slate-500 mb-3">No custom fields added yet. Add fields specific to this contract.</div>}
            <DynamicFieldRenderer fields={customFields} data={data} setData={setData} />
            {customFields.length > 0 && (
              <div className="mt-3 space-y-1 border-t border-slate-100 pt-2">
                <h3 className="text-xs font-medium text-slate-700">Added Custom Fields:</h3>
                {customFields.map((field: any) => (
                  <div key={field.key} className="flex items-center justify-between py-1.5 px-2 bg-slate-50 rounded-md">
                    <span className="text-xs text-slate-700">{field.label} <span className="text-xs text-slate-500">({field.type})</span></span>
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      className="h-5 w-5 p-0 text-destructive hover:bg-red-50 rounded-full"
                      onClick={() => handleRemoveCustomField(field.key)}
                      aria-label="Remove custom field"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
      {/* Modal for adding custom field */}
      {customFieldModal && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-4 w-[380px] max-w-full">
            <div className="mb-3">
              <h3 className="text-sm font-medium mb-0.5">Add Custom Field</h3>
              <p className="text-xs text-slate-500">Create a new field specific to this contract</p>
            </div>

            <div className="space-y-3 mb-3">
              <div className="space-y-1">
                <Label className="text-xs font-medium">Field Label <span className="text-destructive">*</span></Label>
                <Input
                  className="h-8 bg-white border-slate-200"
                  value={newField.label}
                  onChange={e => setNewField(f => ({ ...f, label: e.target.value }))}
                  placeholder="e.g. Project Timeline"
                />
              </div>

              <div className="space-y-1">
                <Label className="text-xs font-medium">Field Type <span className="text-destructive">*</span></Label>
                <Select
                  value={newField.type}
                  onValueChange={value => setNewField(f => ({ ...f, type: value }))}
                >
                  <SelectTrigger className="h-8 bg-white border-slate-200">
                    <SelectValue placeholder="Select field type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="textarea">Textarea</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="date">Date</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label className="text-xs font-medium">Placeholder Text</Label>
                <Input
                  className="h-8 bg-white border-slate-200"
                  value={newField.placeholder}
                  onChange={e => setNewField(f => ({ ...f, placeholder: e.target.value }))}
                  placeholder="e.g. Enter project timeline details"
                />
                <p className="text-xs text-slate-500 mt-0.5">Helper text shown in the empty field</p>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-2 border-t border-slate-100">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCustomFieldModal(false)}
                className="h-7 px-3 bg-white border-slate-200 text-xs"
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleAddCustomField}
                disabled={!newField.label.trim() || !newField.type}
                className="h-7 px-3 text-xs"
              >
                Add Field
              </Button>
            </div>
          </div>
        </div>
      )}
      {touched && !isValid && (
        <div className="text-xs text-destructive mt-0.5">Please fill all required fields.</div>
      )}
      <div className="sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent pt-4 pb-3 mt-4 z-10">
        <div className="flex justify-between gap-2 max-w-xl">
          <Button
            type="button"
            variant="outline"
            className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
            onClick={prevStep}
          >
            <ChevronLeft className="mr-1 h-3 w-3" />
            Back
          </Button>
          <Button
            type="submit"
            className="flex items-center h-7 px-3 shadow-sm transition-all hover:shadow text-xs"
          >
            Continue
            <ChevronRight className="ml-1 h-3 w-3" />
          </Button>
        </div>
      </div>
    </form>
  );
};

export default Step3ContractTerms;