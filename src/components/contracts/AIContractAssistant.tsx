import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Bot,
  Send,
  User,
  FileText,
  Sparkles,
  Loader2,
  Copy,
  Check,
  Download,
  RefreshCw
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface AIContractAssistantProps {
  onContractGenerated?: (content: string) => void;
}

const AIContractAssistant: React.FC<AIContractAssistantProps> = ({
  onContractGenerated
}) => {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("chat");
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: "Hello! I'm your AI contract assistant. Describe the contract you need, and I'll help you draft it. You can also try one of the sample prompts below.",
      timestamp: new Date().toISOString()
    }
  ]);
  const [generatedContract, setGeneratedContract] = useState<string>("");
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);

  // Sample prompts that users can click on
  const samplePrompts = [
    "Create a consulting agreement for a software developer working with a startup for 6 months",
    "Draft a non-disclosure agreement for sharing confidential information with a potential investor",
    "Write an employment contract for a remote marketing specialist with performance-based bonuses",
    "Create a service agreement for a web design project with milestone payments"
  ];

  const handleSendPrompt = () => {
    if (!prompt.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      role: "user",
      content: prompt,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsGenerating(true);

    // Clear the input
    setPrompt("");

    // Simulate AI response after a delay
    setTimeout(() => {
      // Generate a response based on the prompt
      const response = generateAIResponse(userMessage.content);

      // Add AI message
      const aiMessage: Message = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: "assistant",
        content: response.message,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsGenerating(false);

      // If a contract was generated, update the state
      if (response.contract) {
        setGeneratedContract(response.contract);
        setActiveTab("preview");

        if (onContractGenerated) {
          onContractGenerated(response.contract);
        }
      }
    }, 2000);
  };

  const handleSamplePromptClick = (promptText: string) => {
    setPrompt(promptText);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendPrompt();
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedContract);
    setCopiedToClipboard(true);

    toast({
      title: "Copied to clipboard",
      description: "Contract text has been copied to your clipboard."
    });

    setTimeout(() => setCopiedToClipboard(false), 2000);
  };

  const handleDownload = () => {
    const blob = new Blob([generatedContract], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'generated-contract.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Contract downloaded",
      description: "Your contract has been downloaded as a text file."
    });
  };

  const handleRegenerateContract = () => {
    setIsGenerating(true);

    // Simulate regeneration
    setTimeout(() => {
      const newContract = generateAIResponse(messages[messages.length - 2]?.content || "").contract;
      if (newContract) {
        setGeneratedContract(newContract);

        // Add AI message about regeneration
        const aiMessage: Message = {
          id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          role: "assistant",
          content: "I've regenerated the contract with some variations. You can view it in the preview tab.",
          timestamp: new Date().toISOString()
        };

        setMessages(prev => [...prev, aiMessage]);

        if (onContractGenerated) {
          onContractGenerated(newContract);
        }
      }

      setIsGenerating(false);
    }, 2000);
  };

  // Mock function to generate AI responses
  const generateAIResponse = (userPrompt: string): { message: string, contract?: string } => {
    // Check if the prompt is asking for a contract
    const isContractRequest =
      userPrompt.toLowerCase().includes("contract") ||
      userPrompt.toLowerCase().includes("agreement") ||
      userPrompt.toLowerCase().includes("draft") ||
      userPrompt.toLowerCase().includes("create");

    if (isContractRequest) {
      // Generate a simple contract based on the prompt
      const contractTitle = userPrompt.split(" ").slice(0, 5).join(" ") + "...";

      const contract = `
# ${contractTitle.toUpperCase()}

## PARTIES

This Agreement is made and entered into as of the Effective Date by and between:

**Party A**: [FIRST PARTY NAME], with its principal place of business at [ADDRESS]
**Party B**: [SECOND PARTY NAME], with its principal place of business at [ADDRESS]

## TERMS AND CONDITIONS

1. **SERVICES**
   Party A agrees to provide the following services to Party B: [DESCRIPTION OF SERVICES]

2. **TERM**
   This Agreement shall commence on the Effective Date and continue for a period of [TERM PERIOD], unless terminated earlier in accordance with this Agreement.

3. **COMPENSATION**
   Party B agrees to pay Party A [COMPENSATION AMOUNT] for the services provided under this Agreement.

4. **CONFIDENTIALITY**
   Both parties agree to maintain the confidentiality of any proprietary information disclosed during the course of this Agreement.

5. **TERMINATION**
   Either party may terminate this Agreement with [NOTICE PERIOD] written notice to the other party.

6. **GOVERNING LAW**
   This Agreement shall be governed by and construed in accordance with the laws of [JURISDICTION].

7. **ENTIRE AGREEMENT**
   This Agreement constitutes the entire understanding between the parties and supersedes all prior agreements, representations, and understandings.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the Effective Date.

Party A: ________________________
Date: __________________________

Party B: ________________________
Date: __________________________
      `;

      return {
        message: "I've drafted a contract based on your request. You can view and edit it in the preview tab.",
        contract: contract.trim()
      };
    }

    // Default response for non-contract requests
    return {
      message: "I can help you draft contracts and agreements. Could you provide more details about the type of contract you need?"
    };
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="chat">Chat</TabsTrigger>
          <TabsTrigger value="preview">Contract Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="space-y-4">
          {/* Chat messages */}
          <div className="border rounded-md h-[300px] overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`flex gap-3 max-w-[80%] ${
                    message.role === 'user'
                      ? 'flex-row-reverse'
                      : 'flex-row'
                  }`}
                >
                  <Avatar className="h-8 w-8">
                    {message.role === 'assistant' ? (
                      <>
                        <AvatarFallback className="bg-primary text-primary-foreground">AI</AvatarFallback>
                        <Bot className="h-4 w-4" />
                      </>
                    ) : (
                      <>
                        <AvatarFallback>You</AvatarFallback>
                        <User className="h-4 w-4" />
                      </>
                    )}
                  </Avatar>
                  <div
                    className={`rounded-lg p-3 text-sm ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    {message.content}
                  </div>
                </div>
              </div>
            ))}

            {isGenerating && (
              <div className="flex justify-start">
                <div className="flex gap-3 max-w-[80%]">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary text-primary-foreground">AI</AvatarFallback>
                    <Bot className="h-4 w-4" />
                  </Avatar>
                  <div className="rounded-lg p-3 text-sm bg-muted flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Generating response...
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sample prompts */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Sample Prompts:</Label>
            <div className="flex flex-wrap gap-2">
              {samplePrompts.map((samplePrompt, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => handleSamplePromptClick(samplePrompt)}
                >
                  {samplePrompt.length > 40 ? samplePrompt.substring(0, 40) + '...' : samplePrompt}
                </Badge>
              ))}
            </div>
          </div>

          {/* Input area */}
          <div className="flex gap-2">
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Describe the contract you need..."
              className="flex-1"
              disabled={isGenerating}
            />
            <Button
              onClick={handleSendPrompt}
              disabled={!prompt.trim() || isGenerating}
              className="self-end"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="preview">
          {generatedContract ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">Generated Contract</h3>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyToClipboard}
                    disabled={isGenerating}
                  >
                    {copiedToClipboard ? (
                      <>
                        <Check className="h-4 w-4 mr-1" />
                        Copied
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-1" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                    disabled={isGenerating}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRegenerateContract}
                    disabled={isGenerating}
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    ) : (
                      <RefreshCw className="h-4 w-4 mr-1" />
                    )}
                    Regenerate
                  </Button>
                </div>
              </div>

              <Card className="p-4 h-[400px] overflow-y-auto">
                <pre className="whitespace-pre-wrap font-sans text-sm">
                  {generatedContract}
                </pre>
              </Card>

              <div className="flex justify-end">
                <Button
                  onClick={() => {
                    if (onContractGenerated) {
                      onContractGenerated(generatedContract);
                    }
                  }}
                  disabled={isGenerating}
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Use This Contract
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Contract Generated Yet</h3>
              <p className="text-muted-foreground max-w-md mb-4">
                Describe the contract you need in the chat tab, and I&apos;ll generate a draft for you to review.
              </p>
              <Button variant="outline" onClick={() => setActiveTab("chat")}>
                Go to Chat
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIContractAssistant;
