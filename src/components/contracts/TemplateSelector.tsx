import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Search, FileText, BookOpen, Check } from "lucide-react";
import { contractTemplates } from "./contract-wizard/contractTemplates";

export interface Template {
  id: string;
  name: string;
  description?: string;
  category?: string;
  industry?: string[];
  popularity?: number;
}

interface TemplateSelectorProps {
  onSelectTemplate: (templateId: string) => void;
  selectedTemplateId?: string;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onSelectTemplate,
  selectedTemplateId,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedIndustry, setSelectedIndustry] = useState<string>("all");
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Convert contract templates to the Template interface
  const templates: Template[] = contractTemplates.map(template => ({
    id: template.id,
    name: template.label,
    description: template.description,
    category: getCategoryFromId(template.id),
    industry: template.industry,
    popularity: getPopularityScore(template.id)
  }));

  // Get category from template ID (simple heuristic)
  function getCategoryFromId(id: string): string {
    if (id.includes('nda') || id.includes('confidentiality')) return 'Confidentiality';
    if (id.includes('service') || id.includes('consulting')) return 'Services';
    if (id.includes('employment') || id.includes('contractor')) return 'Employment';
    if (id.includes('sale') || id.includes('purchase')) return 'Sales';
    if (id.includes('lease') || id.includes('rental')) return 'Real Estate';
    if (id.includes('license')) return 'Intellectual Property';
    return 'Other';
  }

  // Mock popularity score for sorting
  function getPopularityScore(id: string): number {
    const popularityMap: Record<string, number> = {
      'nda': 95,
      'service': 90,
      'employment': 85,
      'consulting': 80,
      'lease': 75,
      'license': 70
    };
    
    // Find the first key that matches part of the ID
    for (const key in popularityMap) {
      if (id.includes(key)) {
        return popularityMap[key];
      }
    }
    
    return 50; // Default score
  }

  // Get all unique categories
  const categories = ['all', ...new Set(templates.map(t => t.category))].filter(Boolean) as string[];
  
  // Get all unique industries
  const industries = ['all', ...new Set(templates.flatMap(t => t.industry || []))].filter(Boolean) as string[];

  // Filter templates based on search, category, and industry
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = 
      searchQuery === "" || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.description && template.description.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = 
      selectedCategory === "all" || 
      template.category === selectedCategory;
    
    const matchesIndustry = 
      selectedIndustry === "all" || 
      template.industry?.includes(selectedIndustry);
    
    return matchesSearch && matchesCategory && matchesIndustry;
  });

  // Sort templates by popularity
  const sortedTemplates = [...filteredTemplates].sort((a, b) => 
    (b.popularity || 0) - (a.popularity || 0)
  );

  // Handle template selection
  const handleSelectTemplate = (template: Template) => {
    onSelectTemplate(template.id);
  };

  // Handle template preview
  const handlePreviewTemplate = (template: Template) => {
    setPreviewTemplate(template);
    setShowPreview(true);
  };

  return (
    <div className="space-y-4">
      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select
          value={selectedCategory}
          onValueChange={setSelectedCategory}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select
          value={selectedIndustry}
          onValueChange={setSelectedIndustry}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Industry" />
          </SelectTrigger>
          <SelectContent>
            {industries.map((industry) => (
              <SelectItem key={industry} value={industry}>
                {industry === 'all' ? 'All Industries' : industry}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Templates grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sortedTemplates.map((template) => (
          <div
            key={template.id}
            className={`border rounded-lg p-4 cursor-pointer transition-all hover:border-primary ${
              selectedTemplateId === template.id ? "border-primary bg-primary/5" : ""
            }`}
            onClick={() => handleSelectTemplate(template)}
          >
            <div className="flex flex-col h-full">
              <div className="flex items-start justify-between mb-2">
                <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                {template.category && (
                  <Badge variant="outline" className="text-xs">
                    {template.category}
                  </Badge>
                )}
              </div>
              <h3 className="font-medium mb-1">{template.name}</h3>
              {template.description && (
                <p className="text-sm text-muted-foreground mb-3 flex-grow">
                  {template.description.length > 80
                    ? template.description.substring(0, 80) + "..."
                    : template.description}
                </p>
              )}
              <div className="flex justify-between items-center mt-auto">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePreviewTemplate(template);
                  }}
                >
                  <BookOpen className="h-3.5 w-3.5 mr-1" />
                  Preview
                </Button>
                {selectedTemplateId === template.id && (
                  <Badge className="bg-primary">
                    <Check className="h-3 w-3 mr-1" />
                    Selected
                  </Badge>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* No results message */}
      {sortedTemplates.length === 0 && (
        <div className="text-center py-8 border rounded-lg">
          <FileText className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
          <h3 className="text-lg font-medium mb-1">No templates found</h3>
          <p className="text-sm text-muted-foreground">
            Try adjusting your search or filters
          </p>
        </div>
      )}

      {/* Template preview dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{previewTemplate?.name}</DialogTitle>
            <DialogDescription>
              {previewTemplate?.description}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 my-4">
            {/* Template details */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Template Details</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="bg-muted p-2 rounded-md">
                  <span className="text-muted-foreground">Category:</span>{" "}
                  {previewTemplate?.category}
                </div>
                {previewTemplate?.industry && (
                  <div className="bg-muted p-2 rounded-md">
                    <span className="text-muted-foreground">Industries:</span>{" "}
                    {previewTemplate.industry.join(", ")}
                  </div>
                )}
              </div>
            </div>
            
            {/* Template preview content */}
            <div className="border rounded-md p-4 max-h-[400px] overflow-y-auto">
              <h2 className="text-lg font-bold mb-4">{previewTemplate?.name}</h2>
              <p className="mb-4 text-muted-foreground italic">
                This is a preview of the template structure. The actual content will be customized based on your inputs.
              </p>
              
              {/* Mock contract sections */}
              <div className="space-y-4">
                <section>
                  <h3 className="text-md font-bold">1. PARTIES</h3>
                  <p>This agreement is made between [Party A] and [Party B].</p>
                </section>
                
                <section>
                  <h3 className="text-md font-bold">2. DEFINITIONS</h3>
                  <p>In this Agreement, the following terms shall have the following meanings:</p>
                  <p>"Confidential Information" means [...]</p>
                  <p>"Effective Date" means [...]</p>
                </section>
                
                <section>
                  <h3 className="text-md font-bold">3. TERM</h3>
                  <p>This Agreement shall commence on the Effective Date and shall continue [...]</p>
                </section>
                
                {/* More sections would be shown here based on the template */}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              Close
            </Button>
            <Button onClick={() => {
              handleSelectTemplate(previewTemplate as Template);
              setShowPreview(false);
            }}>
              Use This Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TemplateSelector;
