import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Upload, X } from "lucide-react";
import { useNavigate } from "react-router-dom";
import DraftManager from "./DraftManager";
import AttachmentsManager, { Attachment } from "./AttachmentsManager";
import AIContractAssistant from "./AIContractAssistant";
import TemplateSelector from "./TemplateSelector";
import ContractWizardPreview from "./ContractWizardPreview";

interface CreateContractFormProps {
  onClose: () => void;
  onContractCreated: (contractId: string) => void;
}

const CreateContractForm = ({
  onClose,
  onContractCreated,
}: CreateContractFormProps) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("details");
  const [contractType, setContractType] = useState("");
  const [contractTemplate, setContractTemplate] = useState("");
  const [attachments, setAttachments] = useState<Attachment[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would create a contract in the backend
    // and return the new contract ID
    onContractCreated("new-contract-id");
  };

  const handleAddAttachment = (attachment: Attachment) => {
    setAttachments(prev => [...prev, attachment]);
  };

  const handleRemoveAttachment = (attachmentId: string) => {
    setAttachments(prev => prev.filter(a => a.id !== attachmentId));
  };

  const handleStartNewContract = () => {
    navigate('/app/contracts/wizard');
  };

  const handleSelectDraft = (draftId: string) => {
    navigate('/app/contracts/wizard', { state: { draftId } });
  };

  // Use contract templates from the imported module

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={onClose} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Create New Contract</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
  <TabsList className="grid grid-cols-4 w-full max-w-2xl mb-6">
    <TabsTrigger value="template">Use Template</TabsTrigger>
    <TabsTrigger value="scratch">Start from Scratch</TabsTrigger>
    <TabsTrigger value="ai">AI-Assisted</TabsTrigger>
    <TabsTrigger value="attachments">Attachments</TabsTrigger>
  </TabsList>

  {/* Use Template Tab */}
  <TabsContent value="template" className="mt-0">
    <Card>
      <CardHeader>
        <CardTitle>Use a Contract Template</CardTitle>
        <CardDescription>
          Search, filter, and preview templates. Select one to start your contract quickly.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <TemplateSelector
          onSelectTemplate={(templateId) => setContractTemplate(templateId)}
          selectedTemplateId={contractTemplate}
        />
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button
          type="button"
          disabled={!contractTemplate}
          onClick={() => {
            if (contractTemplate) {
              navigate(`/app/contracts/wizard?template=${contractTemplate}`);
            } else {
              setActiveTab("scratch");
            }
          }}
        >
          Start with this Template
        </Button>
      </CardFooter>
    </Card>
  </TabsContent>

  {/* Start from Scratch Tab */}
  <TabsContent value="scratch" className="mt-0">
    <Card>
      <CardHeader>
        <CardTitle>Start from Scratch</CardTitle>
        <CardDescription>
          Build your contract step by step using our guided wizard, or jump straight into editing.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ContractWizardPreview
          onStartNew={handleStartNewContract}
          onSelectDraft={handleSelectDraft}
        />
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button type="button" onClick={() => setActiveTab("ai")}>Next: AI-Assisted</Button>
      </CardFooter>
    </Card>
  </TabsContent>

  {/* AI-Assisted Tab */}
  <TabsContent value="ai" className="mt-0">
    <Card>
      <CardHeader>
        <CardTitle>AI-Assisted Contract Creation</CardTitle>
        <CardDescription>
          Describe the contract you want to create, and our AI will draft it for you.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AIContractAssistant
          onContractGenerated={(content) => {
            console.log("Contract generated:", content.substring(0, 100) + "...");
            // In a real app, this would save the generated contract to state or backend
          }}
        />
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button type="button" onClick={() => setActiveTab("attachments")}>Next: Attachments</Button>
      </CardFooter>
    </Card>
  </TabsContent>

  {/* Attachments Tab */}
  <TabsContent value="attachments" className="mt-0">
    <Card>
      <CardHeader>
        <CardTitle>Attachments</CardTitle>
        <CardDescription>
          Upload any supporting documents or attachments.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentsManager
          attachments={attachments}
          onAttachmentAdd={handleAddAttachment}
          onAttachmentRemove={handleRemoveAttachment}
          maxFileSize={20}
          allowedFileTypes={['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.txt']}
        />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" type="button" onClick={onClose}>Cancel</Button>
        <Button type="submit" onClick={handleSubmit}>Create Contract</Button>
      </CardFooter>
    </Card>
  </TabsContent>
</Tabs>
    </div>
  );
};

export default CreateContractForm;
