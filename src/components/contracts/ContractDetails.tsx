import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  ArrowLeft,
  Download,
  Edit,
  Share2,
  Trash2,
  <PERSON>,
  <PERSON><PERSON>ex<PERSON>,
  Clock,
} from "lucide-react";
import ContractAIAnalysis from "./ContractAIAnalysis";
import ContractVersionHistory from "./ContractVersionHistory";
import ContractComments from "./ContractComments";
import ContractReviewPreview from "./ContractReviewPreview";
import ContractVersionComparison, { ContractVersion } from "./ContractVersionComparison";


interface ContractDetailsProps {
  contractId: string;
  onBack: () => void;
}

const ContractDetails = ({ contractId, onBack }: ContractDetailsProps) => {
  const [activeTab, setActiveTab] = useState("details");
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [shareEmail, setShareEmail] = useState("");

  // Mock data for contract versions
  const contractVersions: ContractVersion[] = [
    {
      id: "version-1",
      version: 1,
      date: "2023-06-15T10:30:00Z",
      user: {
        name: "Jane Smith",
        initials: "JS",
      },
      content: "This is the original contract content...",
      changes: ["Initial draft created"],
      status: "previous",
    },
    {
      id: "version-2",
      version: 2,
      date: "2023-06-20T14:45:00Z",
      user: {
        name: "John Doe",
        initials: "JD",
      },
      content: "This is the updated contract content with some changes...",
      changes: ["Updated payment terms", "Added liability clause"],
      status: "current",
    },
  ];

  // Mock contract data
  const contract = {
    id: contractId,
    title: "Service Agreement with Acme Corp",
    type: "Service Agreement",
    status: "active",
    createdBy: {
      name: "Jane Smith",
      initials: "JS",
    },
    createdDate: "2023-06-15",
    expiryDate: "2024-06-15",
    counterparty: "Acme Corporation",
    counterpartyContact: "John Williams, CEO",
    counterpartyEmail: "<EMAIL>",
    value: "$50,000",
    description:
      "This service agreement outlines the terms and conditions for providing IT consulting services to Acme Corporation for a period of 12 months.",
    terms: [
      { title: "Payment Terms", content: "Net 30 days from invoice date" },
      { title: "Service Level", content: "99.9% uptime guarantee" },
      { title: "Termination", content: "30 days written notice required" },
      {
        title: "Intellectual Property",
        content: "All IP created during the engagement belongs to Acme Corp",
      },
    ],
    attachments: [
      {
        name: "Acme_Corp_Service_Agreement.pdf",
        size: "2.4 MB",
        date: "2023-06-15",
      },
      { name: "Statement_of_Work.docx", size: "1.1 MB", date: "2023-06-15" },
      {
        name: "Service_Level_Agreement.pdf",
        size: "0.8 MB",
        date: "2023-06-15",
      },
    ],
    riskScore: 82,
    complianceStatus: "Compliant",
    lastModified: "2023-06-20",
    lastModifiedBy: "John Doe",
    versions: 3,
    comments: 8,
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "review":
        return <Badge variant="secondary">In Review</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "expired":
        return <Badge variant="destructive">Expired</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  // Handle download contract
  const handleDownload = () => {
    console.log("Downloading contract:", contractId);
    alert("Contract download started");
  };

  // Handle edit contract
  const handleEdit = () => {
    console.log("Editing contract:", contractId);
    setActiveTab("document");
  };

  // Handle share contract
  const handleShare = () => {
    setShowShareDialog(true);
  };

  // Handle share submit
  const handleShareSubmit = () => {
    console.log("Sharing contract with:", shareEmail);
    alert(`Contract shared with ${shareEmail}`);
    setShareEmail("");
    setShowShareDialog(false);
  };

  // Handle delete contract
  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  // Handle delete confirm
  const handleDeleteConfirm = () => {
    console.log("Deleting contract:", contractId);
    alert("Contract deleted");
    onBack();
  };

  return (
    <div className="w-full">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={onBack} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold">{contract.title}</h1>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline">{contract.type}</Badge>
            {getStatusBadge(contract.status)}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" size="sm" onClick={() => setActiveTab("ai-analysis")}>
            <Brain className="mr-2 h-4 w-4" />
            AI Analysis
          </Button>
          <Button variant="outline" size="sm" onClick={handleDelete} className="text-red-500 hover:text-red-700">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>

        {/* Share Dialog */}
        <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Share Contract</DialogTitle>
              <DialogDescription>
                Enter the email address of the person you want to share this contract with.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <Input
                  id="email"
                  value={shareEmail}
                  onChange={(e) => setShareEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowShareDialog(false)}>Cancel</Button>
              <Button onClick={handleShareSubmit}>Share</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the contract
                and remove it from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm} className="bg-red-500 hover:bg-red-700">
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-6 w-full max-w-3xl mb-6">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="document">
            <FileText className="h-4 w-4 mr-2" />
            Document
          </TabsTrigger>
          <TabsTrigger value="versions">
            <Clock className="h-4 w-4 mr-2" />
            Versions
          </TabsTrigger>
          <TabsTrigger value="ai-analysis">AI Analysis</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contract Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Description
                      </h3>
                      <p className="mt-1">{contract.description}</p>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">
                        Key Terms
                      </h3>
                      <div className="space-y-2">
                        {contract.terms.map((term, index) => (
                          <div key={index} className="grid grid-cols-3 gap-4">
                            <div className="font-medium">{term.title}</div>
                            <div className="col-span-2">{term.content}</div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">
                        Attachments
                      </h3>
                      <div className="space-y-2">
                        {contract.attachments.map((attachment, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 border rounded-md"
                          >
                            <div className="flex items-center">
                              <FileIcon className="h-5 w-5 mr-2 text-blue-500" />
                              <span>{attachment.name}</span>
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <span className="mr-4">{attachment.size}</span>
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contract Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Status
                      </div>
                      <div>{getStatusBadge(contract.status)}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Created
                      </div>
                      <div>{formatDate(contract.createdDate)}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Expiry
                      </div>
                      <div>{formatDate(contract.expiryDate)}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Value
                      </div>
                      <div>{contract.value}</div>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Created By
                      </div>
                      <div className="flex items-center">
                        <div className="h-5 w-5 rounded-full bg-muted flex items-center justify-center text-xs mr-2">
                          {contract.createdBy.initials}
                        </div>
                        <span>{contract.createdBy.name}</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Last Modified
                      </div>
                      <div>{contract.lastModified}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Modified By
                      </div>
                      <div>{contract.lastModifiedBy}</div>
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Counterparty
                      </div>
                      <div>{contract.counterparty}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Contact
                      </div>
                      <div>{contract.counterpartyContact}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Email
                      </div>
                      <div className="truncate">
                        {contract.counterpartyEmail}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Compliance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Risk Score
                      </div>
                      <div className="flex items-center">
                        <span className="font-medium text-amber-500">
                          {contract.riskScore}
                        </span>
                        <span className="text-xs text-muted-foreground ml-1">
                          /100
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-muted-foreground">
                        Status
                      </div>
                      <div>
                        <Badge
                          variant="outline"
                          className="bg-green-50 text-green-700 border-green-200"
                        >
                          {contract.complianceStatus}
                        </Badge>
                      </div>
                    </div>

                    <div className="mt-2">
                      <Button variant="outline" size="sm" className="w-full">
                        View Full Compliance Report
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="document" className="mt-0">
          <div className="h-[800px]">
            <ContractReviewPreview
              contractId={contractId}
              contractTitle={contract.title}
              readOnly={false}
              onSave={(content) => console.log("Saving content:", content)}
            />
          </div>
        </TabsContent>

        <TabsContent value="versions" className="mt-0">
          <ContractVersionComparison
            contractId={contractId}
            contractTitle={contract.title}
            versions={contractVersions}
            onViewVersion={(versionId) => console.log("View version:", versionId)}
            onRestoreVersion={(versionId) => console.log("Restore version:", versionId)}
            onDownloadVersion={(versionId) => console.log("Download version:", versionId)}
          />
        </TabsContent>

        <TabsContent value="ai-analysis" className="mt-0">
          <ContractAIAnalysis contractId={contractId} />
        </TabsContent>

        <TabsContent value="history" className="mt-0">
          <ContractVersionHistory contractId={contractId} />
        </TabsContent>

        <TabsContent value="comments" className="mt-0">
          <ContractComments contractId={contractId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Simple file icon component
const FileIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
      <polyline points="14 2 14 8 20 8" />
    </svg>
  );
};

export default ContractDetails;
