import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";
import { <PERSON><PERSON> } from "../ui/button";
import { Progress } from "../ui/progress";
import { Badge } from "../ui/badge";
import { AlertCircle, CheckCircle, Clock, File, FileText, Info, Loader2, RefreshCw, Shield, Tag } from "lucide-react";
import { cn } from "@/lib/utils";
import { useApi } from "@/lib/api";
import { AIAnalysisService } from "@/services/api-services";
import type { AIAnalysisResult } from "@/services/api-types";
import { useToast } from "@/components/ui/use-toast";

// Mock data for the component
interface Clause {
  id: string;
  name: string;
  category: string;
  riskLevel: "high" | "medium" | "low" | "none";
  description: string;
  recommendation?: string;
}

interface Obligation {
  id: string;
  title: string;
  dueDate: string;
  status: "pending" | "completed" | "overdue";
  assignedTo?: string;
  description: string;
}

interface ComplianceItem {
  id: string;
  regulation: string;
  status: "compliant" | "non-compliant" | "warning";
  details: string;
}

interface ContractAIAnalysisDashboardProps {
  contractId?: string;
  contractName?: string;
  riskScore?: number;
  clauses?: Clause[];
  obligations?: Obligation[];
  complianceItems?: ComplianceItem[];
  isFetching?: boolean;
}

const ContractAIAnalysisDashboard: React.FC<ContractAIAnalysisDashboardProps> = ({
  contractId,
  contractName,
  isFetching: propIsFetching = false,
}) => {
  const { fetch } = useApi();
  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState("overview");
  const [analysis, setAnalysis] = useState<AIAnalysisResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRunningAnalysis, setIsRunningAnalysis] = useState(false);

  // Derived data for UI
  const riskScore = analysis?.risk_score || 0;

  // Map API data to component data structures
  const clauses: Clause[] = analysis?.extracted_clauses.map(clause => ({
    id: clause.id,
    name: clause.title,
    category: clause.category || 'general',
    riskLevel: (clause.risk_level as any) || 'none',
    description: clause.content,
    recommendation: analysis?.key_risks.find(risk =>
      risk.location === clause.location
    )?.recommendation
  })) || [];

  const obligations: Obligation[] = analysis?.obligations.map(obl => ({
    id: obl.id,
    title: obl.title,
    dueDate: obl.due_date,
    status: obl.status,
    assignedTo: obl.assigned_to,
    description: obl.description
  })) || [];

  const complianceItems: ComplianceItem[] = analysis?.compliance_issues.map(issue => ({
    id: issue.id,
    regulation: issue.regulation || 'General',
    status: issue.status,
    details: issue.details
  })) || [];

  // Fetch analysis data when component mounts
  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!contractId) return;

      setLoading(true);
      setError(null);

      try {
        const result = await fetch(
          () => AIAnalysisService.getAnalysis(contractId),
          "Loading analysis...",
          "Failed to load analysis"
        );

        if (result) {
          setAnalysis(result);
        }
      } catch (err) {
        console.error("Error fetching analysis:", err);
        setError("Failed to load analysis. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();
  }, [contractId, fetch]);

  // Handle run fresh analysis
  const handleRunAnalysis = async () => {
    if (!contractId) return;

    setIsRunningAnalysis(true);

    try {
      const result = await fetch(
        () => AIAnalysisService.runAnalysis({ contract_id: contractId }),
        "Running analysis...",
        "Failed to run analysis"
      );

      if (result) {
        setAnalysis(result);
        toast({
          title: "Analysis complete",
          description: "Fresh AI analysis has been completed successfully.",
        });
      }
    } catch (error) {
      console.error("Error running analysis:", error);
      toast({
        title: "Error",
        description: "Failed to run analysis. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRunningAnalysis(false);
    }
  };

  // Handle export analysis
  const handleExportAnalysis = async () => {
    if (!contractId) return;

    try {
      const result = await fetch(
        () => AIAnalysisService.exportAnalysis(contractId),
        "Exporting analysis...",
        "Failed to export analysis"
      );

      if (result && result.url) {
        // Open the export URL in a new tab
        window.open(result.url, '_blank');

        toast({
          title: "Export successful",
          description: "Analysis has been exported successfully.",
        });
      }
    } catch (error) {
      console.error("Error exporting analysis:", error);
      toast({
        title: "Error",
        description: "Failed to export analysis. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getRiskColor = (riskScore: number) => {
    if (riskScore < 30) return "text-green-600";
    if (riskScore < 70) return "text-amber-600";
    return "text-red-600";
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-amber-100 text-amber-800";
      case "low": return "bg-blue-100 text-blue-800";
      default: return "bg-green-100 text-green-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "overdue": return "bg-red-100 text-red-800";
      case "pending": return "bg-amber-100 text-amber-800";
      case "completed": return "bg-green-100 text-green-800";
      case "compliant": return "bg-green-100 text-green-800";
      case "non-compliant": return "bg-red-100 text-red-800";
      case "warning": return "bg-amber-100 text-amber-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  // If loading, show loading state
  if (loading || propIsFetching) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-12 text-center">
        <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
        <h3 className="text-lg font-medium">Loading analysis...</h3>
        <p className="text-muted-foreground mt-2">
          Please wait while we fetch the AI analysis
        </p>
      </div>
    );
  }

  // If error, show error state
  if (error) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-12 text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading analysis</h3>
        <p className="text-muted-foreground mt-2">{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => {
            setLoading(true);
            setError(null);
            // Refetch analysis
            setTimeout(() => {
              setLoading(false);
            }, 1000);
          }}
        >
          Try Again
        </Button>
      </div>
    );
  }

  // If no analysis data, show empty state
  if (!analysis) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-12 text-center">
        <Shield className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No analysis available</h3>
        <p className="text-muted-foreground mt-2">
          Run an analysis to get AI insights for this contract
        </p>
        <Button
          variant="default"
          className="mt-4"
          onClick={handleRunAnalysis}
          disabled={isRunningAnalysis}
        >
          {isRunningAnalysis ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Run Analysis
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">{contractName}</h3>
          <p className="text-muted-foreground">AI Analysis & Insights</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportAnalysis}
          >
            <FileText className="mr-2 h-4 w-4" />
            Export Analysis
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleRunAnalysis}
            disabled={isRunningAnalysis}
          >
            {isRunningAnalysis ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Shield className="mr-2 h-4 w-4" />
                Run Fresh Analysis
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="clauses">Clauses</TabsTrigger>
          <TabsTrigger value="obligations">Obligations</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Risk Score</CardTitle>
                <CardDescription>Overall contract risk assessment</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center flex-col">
                  <div className={`text-5xl font-bold ${getRiskColor(riskScore)}`}>
                    {riskScore}%
                  </div>
                  <Progress
                    value={riskScore}
                    max={100}
                    className={cn("h-2 mt-2",
                      riskScore < 30
                        ? "bg-green-600/20"
                        : riskScore < 70
                          ? "bg-amber-600/20"
                          : "bg-red-600/20"
                    )}
                  />
                  <p className="text-sm text-muted-foreground mt-2">
                    {riskScore < 30
                      ? "Low risk contract"
                      : riskScore < 70
                        ? "Medium risk - review recommended"
                        : "High risk - thorough review required"}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Key Issues</CardTitle>
                <CardDescription>Critical items requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {clauses
                    .filter(clause => clause.riskLevel === "high")
                    .map(clause => (
                      <li key={clause.id} className="flex items-start gap-2">
                        <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="font-medium">{clause.name}</p>
                          <p className="text-sm text-muted-foreground">{clause.recommendation}</p>
                        </div>
                      </li>
                    ))}
                  {obligations
                    .filter(obl => obl.status === "overdue")
                    .map(obl => (
                      <li key={obl.id} className="flex items-start gap-2">
                        <Clock className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="font-medium">{obl.title}</p>
                          <p className="text-sm text-muted-foreground">Overdue: {obl.dueDate}</p>
                        </div>
                      </li>
                    ))}
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Compliance Status</CardTitle>
                <CardDescription>Regulatory compliance overview</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {complianceItems.map(item => (
                    <li key={item.id} className="flex items-start gap-2">
                      {item.status === "compliant" ? (
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      ) : item.status === "warning" ? (
                        <Info className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                      )}
                      <div>
                        <p className="font-medium">{item.regulation}</p>
                        <p className="text-sm text-muted-foreground">{item.details}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="clauses" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Clause Analysis</CardTitle>
              <CardDescription>AI-identified clauses and risk assessment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {clauses.map((clause) => (
                  <div key={clause.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <File className="h-5 w-5 text-muted-foreground" />
                        <h3 className="font-medium">{clause.name}</h3>
                      </div>
                      <Badge variant="outline" className={getRiskLevelColor(clause.riskLevel)}>
                        {clause.riskLevel.charAt(0).toUpperCase() + clause.riskLevel.slice(1)} Risk
                      </Badge>
                    </div>
                    <div>
                      <Badge variant="outline" className="mb-2">
                        <Tag className="mr-1 h-3 w-3" />
                        {clause.category.charAt(0).toUpperCase() + clause.category.slice(1)}
                      </Badge>
                      <p className="text-sm text-muted-foreground mt-2">{clause.description}</p>
                      {clause.recommendation && (
                        <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded text-sm">
                          <span className="font-medium">Recommendation:</span> {clause.recommendation}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="obligations" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Obligation Tracking</CardTitle>
              <CardDescription>Contract obligations and deadlines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {obligations.map((obligation) => (
                  <div key={obligation.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{obligation.title}</h3>
                      <Badge className={getStatusColor(obligation.status)}>
                        {obligation.status.charAt(0).toUpperCase() + obligation.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                      <div>
                        <span className="text-muted-foreground">Due Date:</span> {obligation.dueDate}
                      </div>
                      {obligation.assignedTo && (
                        <div>
                          <span className="text-muted-foreground">Assigned To:</span> {obligation.assignedTo}
                        </div>
                      )}
                    </div>
                    <p className="text-sm">{obligation.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Checking</CardTitle>
              <CardDescription>Regulatory compliance assessment</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {complianceItems.map((item) => (
                  <div key={item.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{item.regulation}</h3>
                      <Badge className={getStatusColor(item.status)}>
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </Badge>
                    </div>
                    <p className="text-sm">{item.details}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContractAIAnalysisDashboard;