import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { FileText, ArrowRight, Trash2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

export interface Draft {
  id: string;
  title: string;
  lastModified: string;
  type: string;
  progress: number;
}

interface DraftManagerProps {
  onSelectDraft?: (draftId: string) => void;
  onDeleteDraft?: (draftId: string) => void;
}

const DraftManager: React.FC<DraftManagerProps> = ({
  onSelectDraft,
  onDeleteDraft,
}) => {
  const navigate = useNavigate();
  const [drafts, setDrafts] = useState<Draft[]>([]);

  // Load drafts from localStorage on component mount
  useEffect(() => {
    const loadDrafts = () => {
      // Get all keys from localStorage that start with 'contract-wizard-draft'
      const draftKeys = Object.keys(localStorage).filter(key => 
        key === 'contract-wizard-draft' || key.startsWith('contract-wizard-draft-')
      );
      
      const loadedDrafts: Draft[] = [];
      
      draftKeys.forEach(key => {
        try {
          const draftData = JSON.parse(localStorage.getItem(key) || '');
          if (draftData && draftData.data) {
            // Extract relevant information for the draft list
            loadedDrafts.push({
              id: key,
              title: draftData.data.title || 'Untitled Contract',
              type: draftData.data.contractType || 'Draft Contract',
              lastModified: draftData.timestamp || new Date().toISOString(),
              progress: calculateProgress(draftData.data),
            });
          }
        } catch (error) {
          console.error(`Error parsing draft ${key}:`, error);
        }
      });
      
      // Sort drafts by last modified date (newest first)
      loadedDrafts.sort((a, b) => 
        new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
      );
      
      setDrafts(loadedDrafts);
    };
    
    loadDrafts();
  }, []);

  // Calculate completion progress based on filled fields
  const calculateProgress = (data: any): number => {
    if (!data) return 0;
    
    // Define key fields to check for completion
    const keyFields = [
      'title', 'jurisdiction', 'contractType', 'effectiveDate', 
      'parties', 'scope', 'paymentTerms'
    ];
    
    let filledFields = 0;
    
    keyFields.forEach(field => {
      if (field === 'parties') {
        // Check if at least one party has a name
        if (data.parties && data.parties.some((p: any) => p.name)) {
          filledFields++;
        }
      } else if (data[field]) {
        filledFields++;
      }
    });
    
    return Math.round((filledFields / keyFields.length) * 100);
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Handle draft selection
  const handleSelectDraft = (draftId: string) => {
    if (onSelectDraft) {
      onSelectDraft(draftId);
    } else {
      // Default behavior: navigate to wizard with draft ID
      navigate('/contracts/wizard', { state: { draftId } });
    }
  };

  // Handle draft deletion
  const handleDeleteDraft = (e: React.MouseEvent, draftId: string) => {
    e.stopPropagation(); // Prevent triggering the parent click handler
    
    // Remove from localStorage
    localStorage.removeItem(draftId);
    
    // Update state
    setDrafts(drafts.filter(draft => draft.id !== draftId));
    
    // Call callback if provided
    if (onDeleteDraft) {
      onDeleteDraft(draftId);
    }
  };

  if (drafts.length === 0) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        <p>No saved drafts found.</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {drafts.map((draft) => (
        <Card 
          key={draft.id}
          className="p-3 cursor-pointer hover:bg-muted/50 transition-colors"
          onClick={() => handleSelectDraft(draft.id)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                <FileText className="h-5 w-5 text-primary" />
              </div>
              <div>
                <div className="font-medium">{draft.title}</div>
                <div className="text-xs text-muted-foreground">
                  {draft.type} • Last edited {formatDate(draft.lastModified)}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-xs text-muted-foreground mr-2">
                {draft.progress}% complete
              </div>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8 text-muted-foreground hover:text-destructive"
                onClick={(e) => handleDeleteDraft(e, draft.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default DraftManager;
