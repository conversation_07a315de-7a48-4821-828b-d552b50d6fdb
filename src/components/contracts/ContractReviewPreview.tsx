import React, { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Save,
  Undo,
  Redo,
  Edit,
  Eye,
  Download
} from "lucide-react";

interface ContractReviewPreviewProps {
  contractId?: string;
  contractTitle?: string;
  contractContent?: string;
  readOnly?: boolean;
  onSave?: (content: string) => void;
}

const ContractReviewPreview: React.FC<ContractReviewPreviewProps> = ({
  contractId = "contract-123",
  contractTitle = "Service Agreement",
  contractContent,
  readOnly = false,
  onSave,
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);

  const defaultContent = contractContent || `
    <!-- Document Header/Letterhead -->
    <div style="text-align: center; padding: 0.75in 0 0.5in 0; border-bottom: 2px solid #1f2937; margin-bottom: 1.5em; position: relative;">
      <div style="position: absolute; top: 0; left: 0; right: 0; height: 0.75in; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-bottom: 1px solid #e2e8f0; z-index: -1;"></div>
      <h1 style="font-family: 'Times New Roman', serif; font-size: 18pt; font-weight: bold; text-transform: uppercase; letter-spacing: 0.1em; margin-bottom: 0.5em; color: #1f2937;">SERVICE AGREEMENT</h1>
      <div style="font-size: 14pt; font-weight: 500; color: #4b5563; margin-bottom: 0.25em;">Professional Services Contract</div>
      <div style="font-size: 11pt; color: #6b7280; font-style: italic;">
        Contract No.: CONT-${Math.floor(10000 + Math.random() * 90000)}<br/>
        Effective Date: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
      </div>
    </div>

    <!-- Page Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 10pt; color: #6b7280; border-bottom: 1px solid #e5e7eb; padding-bottom: 0.125in; margin-bottom: 1.5em;">
      <span>Service Agreement</span>
      <span>Page 1</span>
    </div>

    <!-- Parties Section -->
    <div style="margin-bottom: 2em;">
      <p style="font-weight: 600; margin-bottom: 1.2em; text-align: justify; font-size: 12pt; line-height: 1.6;">
        THIS AGREEMENT is made and entered into as of <strong>${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</strong>, by and between the following parties:
      </p>

      <div style="display: flex; flex-direction: column; gap: 1.5em; margin-bottom: 1.5em;">
        <div style="border: 1px solid #d1d5db; padding: 1em 1.25em; background-color: #f9fafb; box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);">
          <div style="font-weight: bold; text-transform: uppercase; font-size: 11pt; letter-spacing: 0.05em; margin-bottom: 0.5em; padding-bottom: 0.25em; border-bottom: 1px solid #d1d5db; color: #1f2937;">FIRST PARTY:</div>
          <div style="padding-left: 1em; font-size: 12pt; line-height: 1.6;">
            <strong>Acme Corporation</strong>, a business entity having its principal place of business at 123 Business Ave, New York, NY 10001 ("Client")
          </div>
        </div>
        <div style="text-align: center; font-weight: 600; margin: 0.5em 0; font-size: 14pt;">AND</div>
        <div style="border: 1px solid #d1d5db; padding: 1em 1.25em; background-color: #f9fafb; box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);">
          <div style="font-weight: bold; text-transform: uppercase; font-size: 11pt; letter-spacing: 0.05em; margin-bottom: 0.5em; padding-bottom: 0.25em; border-bottom: 1px solid #d1d5db; color: #1f2937;">SECOND PARTY:</div>
          <div style="padding-left: 1em; font-size: 12pt; line-height: 1.6;">
            <strong>Tech Solutions Inc.</strong>, a business entity having its principal place of business at 456 Tech Blvd, San Francisco, CA 94105 ("Service Provider")
          </div>
        </div>
      </div>

      <p style="margin: 1.2em 0 0.7em 0; text-align: justify; font-style: italic; font-size: 12pt; line-height: 1.6;">
        The First Party and Second Party may be referred to individually as a "Party" and collectively as the "Parties" in this Agreement.
      </p>
    </div>

    <p style="text-align: center; margin-bottom: 24px;"><strong>EFFECTIVE DATE: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</strong></p>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">1. SERVICES</h2>
    <p style="margin-bottom: 24px;">The Service Provider agrees to provide the following services to the Client:</p>
    <ul style="margin-bottom: 24px;">
      <li>Software development and implementation</li>
      <li>Technical support and maintenance</li>
      <li>User training and documentation</li>
      <li>System integration services</li>
    </ul>

    <!-- Recitals Section -->
    <div style="font-family: 'Times New Roman', serif; font-size: 14pt; font-weight: bold; text-transform: uppercase; letter-spacing: 0.05em; text-align: center; margin: 2em 0 1em 0; padding: 0.5em 0; border-top: 1px solid #d1d5db; border-bottom: 1px solid #d1d5db; color: #1f2937;">1. RECITALS</div>
    <div style="margin: 2em 0; padding: 1em 1.5em; background-color: #f9fafb; border: 1px solid #e5e7eb; font-style: italic; text-align: justify;">
      <p style="margin-bottom: 0.8em; font-size: 12pt; line-height: 1.6;"><strong>WHEREAS,</strong> the Client requires professional technology services;</p>
      <p style="margin-bottom: 0.8em; font-size: 12pt; line-height: 1.6;"><strong>WHEREAS,</strong> the Service Provider has the expertise and capability to provide such services;</p>
      <p style="margin-bottom: 0.8em; font-size: 12pt; line-height: 1.6;"><strong>NOW, THEREFORE,</strong> in consideration of the mutual covenants and agreements contained herein, and other good and valuable consideration, the receipt and sufficiency of which is hereby acknowledged, the Parties agree as follows:</p>
    </div>

      <!-- Terms and Conditions Section -->
      <div class="contract-section-header">2. TERMS AND CONDITIONS</div>
      <ol class="contract-list" style="padding-left: 2.2em; margin-bottom: 0;">
        <li style="margin-bottom: 2em; line-height: 1.7;">
          <div class="contract-subsection-header">2.1 Term and Termination</div>
          <div class="contract-clause">
            <p style="margin-bottom: 0.8em;">This Agreement shall commence on the Effective Date and continue for a period of twelve (12) months, unless earlier terminated in accordance with the provisions set forth herein.</p>
            <p style="margin-bottom: 0.8em;">Either Party may terminate this Agreement for cause in the event of a material breach by the other Party, if such breach remains uncured for thirty (30) days following written notice to the breaching Party.</p>
          </div>
        </li>

        <li style="margin-bottom: 2em; line-height: 1.7;">
          <div class="contract-subsection-header">2.2 Compensation</div>
          <div class="contract-clause">
            <p style="margin-bottom: 0.8em;">The Client agrees to pay the Service Provider a fee of Ten Thousand Dollars ($10,000) per month for the Services described herein.</p>
            <p style="margin-bottom: 0.8em;">Payment shall be made within thirty (30) days of receipt of an invoice from the Service Provider. Late payments may incur interest charges at the rate of 1.5% per month.</p>
          </div>
        </li>
      </ol>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">4. CONFIDENTIALITY</h2>
    <p style="margin-bottom: 24px;">Both parties agree to maintain the confidentiality of any proprietary information disclosed during the course of this Agreement.</p>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">5. INTELLECTUAL PROPERTY</h2>
    <p style="margin-bottom: 24px;">All intellectual property created by the Service Provider in the course of providing the Services shall be owned by the Client upon full payment of all fees due under this Agreement.</p>

      <!-- Signature Section -->
      <div class="contract-signature-section">
        <div class="contract-witness-section">IN WITNESS WHEREOF</div>
        <p style="margin: 2em 0; text-align: justify; line-height: 1.6;">
          The Parties hereto have executed this Agreement as of the Effective Date first written above, by their duly authorized representatives.
        </p>

        <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 3em; gap: 3em;">
          <div class="contract-signature-block" style="flex: 1; min-width: 280px;">
            <div class="contract-party-label" style="margin-bottom: 1em;">FIRST PARTY</div>
            <div class="signature-line"></div>
            <table style="width: 100%; margin-top: 2em; border-collapse: collapse;">
              <tr>
                <td style="font-weight: 600; padding: 0.5em 0; width: 30%;">Name:</td>
                <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">John Smith</td>
              </tr>
              <tr>
                <td style="font-weight: 600; padding: 0.5em 0;">Title:</td>
                <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">Chief Executive Officer</td>
              </tr>
              <tr>
                <td style="font-weight: 600; padding: 0.5em 0;">Date:</td>
                <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">____________________</td>
              </tr>
            </table>
          </div>

          <div class="contract-signature-block" style="flex: 1; min-width: 280px;">
            <div class="contract-party-label" style="margin-bottom: 1em;">SECOND PARTY</div>
            <div class="signature-line"></div>
            <table style="width: 100%; margin-top: 2em; border-collapse: collapse;">
              <tr>
                <td style="font-weight: 600; padding: 0.5em 0; width: 30%;">Name:</td>
                <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">Jane Doe</td>
              </tr>
              <tr>
                <td style="font-weight: 600; padding: 0.5em 0;">Title:</td>
                <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">Chief Executive Officer</td>
              </tr>
              <tr>
                <td style="font-weight: 600; padding: 0.5em 0;">Date:</td>
                <td style="padding: 0.5em 0; border-bottom: 1px solid #d1d5db;">____________________</td>
              </tr>
            </table>
          </div>
        </div>
      </div>

      <!-- Page Footer -->
      <div class="contract-page-footer">
        <span>Contract executed on ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</span>
      </div>
    </div>
  `;

  // Initialize TipTap editor with professional document styling
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
    ],
    content: defaultContent,
    editable: isEditing,
    editorProps: {
      attributes: {
        class: 'focus:outline-none',
        style: 'font-family: "Times New Roman", serif; font-size: 12pt; line-height: 1.6; color: #1a1a1a; min-height: 100%; padding: 0 1in 1in 1in; margin: 0; background: white; max-width: 8.5in; box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);'
      },
    },
  });

  const previewRef = useRef<HTMLDivElement>(null);

  // Update editor content when contractContent changes
  useEffect(() => {
    if (editor && contractContent) {
      editor.commands.setContent(contractContent);
    }
  }, [editor, contractContent]);

  // Update editor editable state when editing mode changes
  useEffect(() => {
    if (editor) {
      editor.setEditable(isEditing);
    }
  }, [editor, isEditing]);

  // Handle save
  const handleSave = () => {
    if (editor) {
      const newContent = editor.getHTML();
      if (onSave) {
        onSave(newContent);
      }
      setIsEditing(false);
    }
  };

  // Handle editor commands using TipTap
  const handleCommand = (command: string, value?: string) => {
    if (!editor) return;

    switch (command) {
      case 'bold':
        editor.chain().focus().toggleBold().run();
        break;
      case 'italic':
        editor.chain().focus().toggleItalic().run();
        break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run();
        break;
      case 'justifyLeft':
        editor.chain().focus().setTextAlign('left').run();
        break;
      case 'justifyCenter':
        editor.chain().focus().setTextAlign('center').run();
        break;
      case 'justifyRight':
        editor.chain().focus().setTextAlign('right').run();
        break;
      case 'insertUnorderedList':
        editor.chain().focus().toggleBulletList().run();
        break;
      case 'insertOrderedList':
        editor.chain().focus().toggleOrderedList().run();
        break;
      case 'formatBlock':
        if (value === '<h1>') {
          editor.chain().focus().toggleHeading({ level: 1 }).run();
        } else if (value === '<h2>') {
          editor.chain().focus().toggleHeading({ level: 2 }).run();
        }
        break;
      case 'undo':
        editor.chain().focus().undo().run();
        break;
      case 'redo':
        editor.chain().focus().redo().run();
        break;
      default:
        console.warn(`Unknown command: ${command}`);
    }
  };

  // Toggle editing mode
  const toggleEditMode = () => {
    if (isEditing && editor) {
      // If we're currently editing, save the content before switching to preview
      const newContent = editor.getHTML();
      if (onSave) {
        onSave(newContent);
      }
    }
    setIsEditing(!isEditing);
  };

  // Handle PDF export with professional styling
  const handleExportPDF = () => {
    if (editor) {
      // Create a temporary container with professional print styles
      const printContainer = document.createElement('div');
      printContainer.innerHTML = editor.getHTML();
      printContainer.className = 'professional-contract-document';
      printContainer.style.cssText = `
        font-family: "Times New Roman", serif;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: white;
        padding: 1in;
        max-width: 8.5in;
        margin: 0 auto;
      `;

      // Temporarily add to document for PDF generation
      document.body.appendChild(printContainer);

      // Use window.print() for now - in production, use html2pdf
      const originalContent = document.body.innerHTML;
      document.body.innerHTML = printContainer.outerHTML;
      window.print();
      document.body.innerHTML = originalContent;

      // Clean up
      if (document.body.contains(printContainer)) {
        document.body.removeChild(printContainer);
      }
    }
  };

  return (
    <div className="w-full h-full flex flex-col">
      <Card className="flex-1 flex flex-col overflow-hidden border-slate-200">
        <div className="border-b px-6 py-2 bg-muted/30 flex items-center justify-between">
          <div className="text-sm font-medium">Document Editor</div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleEditMode}
              className="h-8"
            >
              {isEditing ? (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportPDF}
              className="h-8"
            >
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
          </div>
        </div>

        {isEditing ? (
          <div className="border-b px-6 py-2 bg-muted/30 flex items-center gap-1 overflow-x-auto">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('bold') ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('bold')}
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Bold</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('italic') ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('italic')}
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Italic</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('underline') ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('underline')}
                  >
                    <UnderlineIcon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Underline</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive({ textAlign: 'left' }) ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('justifyLeft')}
                  >
                    <AlignLeft className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Align Left</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive({ textAlign: 'center' }) ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('justifyCenter')}
                  >
                    <AlignCenter className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Align Center</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive({ textAlign: 'right' }) ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('justifyRight')}
                  >
                    <AlignRight className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Align Right</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('bulletList') ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('insertUnorderedList')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Bullet List</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('orderedList') ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('insertOrderedList')}
                  >
                    <ListOrdered className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Numbered List</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('heading', { level: 1 }) ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('formatBlock', '<h1>')}
                  >
                    <Heading1 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Heading 1</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={editor?.isActive('heading', { level: 2 }) ? 'default' : 'ghost'}
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('formatBlock', '<h2>')}
                  >
                    <Heading2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Heading 2</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('undo')}
                    disabled={!editor?.can().undo()}
                  >
                    <Undo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Undo</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleCommand('redo')}
                    disabled={!editor?.can().redo()}
                  >
                    <Redo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Redo</TooltipContent>
              </Tooltip>

              <div className="flex-1"></div>

              <Button variant="default" size="sm" onClick={handleSave} className="h-8">
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </TooltipProvider>
          </div>
        ) : null}

        <CardContent className="flex-1 p-0 overflow-auto bg-gray-50">
          <div className="min-h-full flex justify-center">
            <EditorContent
              editor={editor}
              className="min-h-full focus:outline-none"
            />
            {/* Hidden preview div for PDF export */}
            <div
              className="hidden"
              ref={previewRef}
              dangerouslySetInnerHTML={{ __html: editor?.getHTML() || defaultContent }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContractReviewPreview;
