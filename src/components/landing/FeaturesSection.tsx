import {
  Brain,
  FileText,
  Users,
  Shield,
  Building,
  Workflow,
  Sparkles
} from 'lucide-react';
import { useState } from 'react';

const FeaturesSection = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description: "Automatically analyze contracts for risks, compliance issues, and optimization opportunities with advanced machine learning."
    },
    {
      icon: FileText,
      title: "Smart Contract Generation",
      description: "Create professional contracts in minutes with intelligent templates and guided workflows tailored to your industry."
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Work together seamlessly with multi-workspace support, real-time editing, and role-based permissions."
    },
    {
      icon: Workflow,
      title: "Approval Workflows",
      description: "Streamline approvals with configurable workflows, automated notifications, and comprehensive audit trails."
    },
    {
      icon: Building,
      title: "Document Repository",
      description: "Centralized storage with advanced search, version control, folder management, and secure cloud backup."
    },
    {
      icon: Shield,
      title: "Electronic Signatures",
      description: "Legally binding e-signatures with comprehensive tracking, certificate generation, and compliance verification."
    }
  ];

  return (
    <section id="features" className="py-16 sm:py-24 lg:py-32 bg-gradient-to-b from-muted/20 to-background relative overflow-hidden">
      {/* Enhanced background decoration - adjusted for mobile */}
      <div className="absolute top-1/3 right-1/4 w-64 sm:w-80 lg:w-96 h-64 sm:h-80 lg:h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/3 left-1/4 w-48 sm:w-64 lg:w-80 h-48 sm:h-64 lg:h-80 bg-blue-500/5 rounded-full blur-3xl"></div>

      <div className="relative px-4 sm:px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 lg:mb-24">
            <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-primary/10 rounded-full text-xs sm:text-sm font-medium text-primary mb-4 sm:mb-6">
              <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
              Platform Features
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 sm:mb-8 lg:mb-10 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-[1.15] sm:leading-[1.15] md:leading-[1.15] lg:leading-[1.15]">
              Everything you need for contract management
            </h2>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed px-4 sm:px-0">
              From creation to signature, our comprehensive platform handles every aspect of your contract lifecycle with AI-powered precision.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              const isHovered = hoveredIndex === index;
              return (
                <div
                  key={index}
                  className={`group p-6 sm:p-8 rounded-xl sm:rounded-2xl border border-border bg-card transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                    isHovered ? 'shadow-xl scale-105' : 'shadow-sm'
                  }`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  <div className={`w-12 sm:w-16 h-12 sm:h-16 rounded-xl sm:rounded-2xl flex items-center justify-center mb-4 sm:mb-6 transition-all duration-300 ${
                    isHovered ? 'scale-110 shadow-lg' : ''
                  } bg-muted`}>
                    <IconComponent className="w-6 sm:w-8 h-6 sm:h-8 text-foreground" />
                  </div>
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-3 sm:mb-4 text-foreground leading-[1.3]">{feature.title}</h3>
                  <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
