import { But<PERSON> } from '@/components/ui/button';
import { Brain, FileText, Users, CheckCircle, Sparkles, Edit3, Eye, Download, Share2, Clock, ArrowRight, Search, Filter, FolderOpen, History, AlertTriangle, Shield, Zap, BookOpen, Plus, Settings, Bell, Mail, Calendar, BarChart3 } from 'lucide-react';
import { useState } from 'react';

const ProductShowcase = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // Define the workflow steps
  const steps = [
    {
      id: 'template',
      title: 'Choose Template',
      navItem: 'Create Contract',
      url: '/app/contracts/new'
    },
    {
      id: 'details',
      title: 'Contract Details',
      navItem: 'Create Contract',
      url: '/app/contracts/wizard'
    },
    {
      id: 'preview',
      title: 'Document Preview',
      navItem: 'Document Editor',
      url: '/app/contracts/preview/demo'
    },
    {
      id: 'review',
      title: 'Team Review',
      navItem: 'Team Review',
      url: '/app/approvals/workflow/demo'
    }
  ];

  // Manual step navigation
  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  const handleNextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handlePrevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  return (
    <section className="py-32 bg-gradient-to-b from-background to-muted/20 relative overflow-hidden">
      {/* Enhanced background decoration */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>

      <div className="relative px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
              <Sparkles className="w-4 h-4" />
              Live Demo
            </div>
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Complete Contract Management Platform
            </h2>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Experience the full LegalAI platform: AI-powered templates, clause library, document editor, approval workflows, and repository management. Click through each step to explore comprehensive contract lifecycle management.
            </p>
          </div>

          {/* Main Demo Interface */}
          <div
            className="relative bg-background rounded-3xl shadow-2xl border overflow-hidden max-w-6xl mx-auto transition-all duration-500 hover:shadow-3xl group"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            {/* Browser header */}
            <div className="flex items-center gap-2 px-6 py-4 border-b bg-gradient-to-r from-muted/50 to-muted/30">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-muted-foreground rounded-full transition-all duration-300 group-hover:scale-110"></div>
                <div className="w-3 h-3 bg-muted-foreground rounded-full transition-all duration-300 group-hover:scale-110" style={{transitionDelay: '50ms'}}></div>
                <div className="w-3 h-3 bg-muted-foreground rounded-full transition-all duration-300 group-hover:scale-110" style={{transitionDelay: '100ms'}}></div>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <div className="bg-background rounded-lg px-4 py-2 text-sm text-muted-foreground border shadow-sm">
                  <span className="text-green-600">🔒</span> {steps[currentStep].url}
                </div>
              </div>
            </div>

            {/* App Navigation */}
            <div className="flex items-center gap-6 px-6 py-4 border-b bg-gradient-to-r from-muted/10 to-transparent">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <FileText className="w-5 h-5 text-primary-foreground" />
                </div>
                <span className="font-bold text-lg">LegalAI</span>
              </div>
              <nav className="flex items-center gap-8 text-sm">
                {['Create Contract', 'Document Editor', 'Team Review', 'Repository'].map((item) => (
                  <span
                    key={item}
                    className={`transition-all duration-300 cursor-pointer ${
                      item === steps[currentStep].navItem
                        ? 'text-primary font-semibold border-b-2 border-primary pb-2 px-1'
                        : 'text-muted-foreground/50 hover:text-muted-foreground'
                    }`}
                  >
                    {item}
                  </span>
                ))}
              </nav>
            </div>

            <div className="p-8">
              {/* Step Indicator - Clickable */}
              <div className="flex items-center justify-center mb-8">
                <div className="flex items-center gap-4">
                  {steps.map((step, index) => (
                    <div key={step.id} className="flex items-center">
                      <button
                        onClick={() => handleStepClick(index)}
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 hover:scale-110 ${
                          index === currentStep
                            ? 'bg-primary text-primary-foreground shadow-lg'
                            : index < currentStep
                              ? 'bg-green-500 text-white hover:bg-green-600'
                              : 'bg-muted text-muted-foreground hover:bg-muted/80'
                        }`}
                        title={step.title}
                      >
                        {index < currentStep ? <CheckCircle className="w-4 h-4" /> : index + 1}
                      </button>
                      {index < steps.length - 1 && (
                        <div className={`w-12 h-0.5 mx-2 transition-all duration-300 ${
                          index < currentStep ? 'bg-green-500' : 'bg-muted'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Step Title and Description */}
              <div className="text-center mb-8">
                <h3 className="text-2xl md:text-3xl font-bold mb-2">
                  {steps[currentStep].title}
                </h3>
                <p className="text-muted-foreground text-lg">
                  Step {currentStep + 1} of {steps.length}
                </p>
              </div>

              {/* Dynamic Content Based on Current Step */}
              {currentStep === 0 && (
                <div className="grid lg:grid-cols-3 gap-6">
                  {/* Template Selection - Left Column */}
                  <div>
                    <div className="mb-6">
                      <h3 className="text-xl font-bold mb-2">Choose a Template</h3>
                      <p className="text-muted-foreground text-sm">Select from 100+ industry-specific templates</p>
                    </div>

                    <div className="space-y-3">
                      {/* Service Agreement Template - Selected */}
                      <div className="group p-4 bg-gradient-to-r from-primary/10 to-primary/5 border-2 border-primary rounded-xl relative">
                        <div className="absolute top-3 right-3">
                          <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                            <CheckCircle className="w-3 h-3 text-primary-foreground" />
                          </div>
                        </div>
                        <div className="flex items-center gap-3 mb-2">
                          <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                            <FileText className="w-4 h-4 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-semibold">Service Agreement</h4>
                            <p className="text-xs text-muted-foreground">Professional services</p>
                          </div>
                        </div>
                        <div className="text-xs bg-primary/20 text-primary px-2 py-1 rounded-full font-medium inline-block">
                          ✨ AI Recommended
                        </div>
                      </div>

                      {/* Other Templates */}
                      <div className="group p-4 bg-background border border-border rounded-xl hover:border-primary/50 cursor-pointer">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
                            <Users className="w-4 h-4 text-muted-foreground" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-sm">Employment Contract</h4>
                            <p className="text-xs text-muted-foreground">Standard employment</p>
                          </div>
                        </div>
                      </div>

                      <div className="group p-4 bg-background border border-border rounded-xl hover:border-primary/50 cursor-pointer">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center">
                            <Shield className="w-4 h-4 text-muted-foreground" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-sm">NDA Template</h4>
                            <p className="text-xs text-muted-foreground">Non-disclosure</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Clause Library - Middle Column */}
                  <div>
                    <div className="mb-6">
                      <h3 className="text-xl font-bold mb-2">Clause Library</h3>
                      <p className="text-muted-foreground text-sm">Pre-approved clauses ready to use</p>
                    </div>

                    {/* Search and Filter */}
                    <div className="flex gap-2 mb-4">
                      <div className="flex-1 relative">
                        <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                        <input
                          type="text"
                          placeholder="Search clauses..."
                          className="w-full pl-10 pr-4 py-2 text-sm border border-border rounded-lg bg-background"
                        />
                      </div>
                      <Button size="sm" variant="outline">
                        <Filter className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Clause Categories */}
                    <div className="space-y-3">
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <BookOpen className="w-4 h-4 text-blue-600" />
                          <span className="font-medium text-blue-900 text-sm">Payment Terms</span>
                          <span className="text-xs bg-blue-200 text-blue-800 px-2 py-0.5 rounded-full">12 clauses</span>
                        </div>
                        <p className="text-xs text-blue-700">Standard payment schedules and terms</p>
                      </div>

                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="font-medium text-green-900 text-sm">Liability & Risk</span>
                          <span className="text-xs bg-green-200 text-green-800 px-2 py-0.5 rounded-full">8 clauses</span>
                        </div>
                        <p className="text-xs text-green-700">Limitation of liability and indemnification</p>
                      </div>

                      <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <AlertTriangle className="w-4 h-4 text-purple-600" />
                          <span className="font-medium text-purple-900 text-sm">Termination</span>
                          <span className="text-xs bg-purple-200 text-purple-800 px-2 py-0.5 rounded-full">6 clauses</span>
                        </div>
                        <p className="text-xs text-purple-700">Contract termination conditions</p>
                      </div>
                    </div>
                  </div>

                  {/* Template Preview & AI Insights - Right Column */}
                  <div>
                    <div className="mb-6">
                      <h3 className="text-xl font-bold mb-2">Template Preview</h3>
                      <p className="text-muted-foreground text-sm">AI-optimized template with smart clauses</p>
                    </div>

                    <div className="space-y-4">
                      {/* Template Overview */}
                      <div className="bg-muted/30 border border-border rounded-lg p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
                            <FileText className="w-4 h-4 text-primary" />
                          </div>
                          <span className="font-semibold text-sm">Service Agreement Template</span>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1 mb-3">
                          <p>• Professional services contract</p>
                          <p>• Includes payment terms and deliverables</p>
                          <p>• Built-in compliance checks</p>
                          <p>• AI-optimized clauses</p>
                        </div>
                        <div className="flex gap-2">
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">95% Compliant</span>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">12 Clauses</span>
                        </div>
                      </div>

                      {/* AI Recommendations */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Brain className="w-4 h-4 text-blue-600" />
                          <span className="font-medium text-blue-900 text-sm">AI Recommendations</span>
                        </div>
                        <div className="space-y-2 text-xs">
                          <div className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5"></div>
                            <p className="text-blue-700">Add milestone-based payment terms for better cash flow</p>
                          </div>
                          <div className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5"></div>
                            <p className="text-blue-700">Include intellectual property ownership clause</p>
                          </div>
                          <div className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5"></div>
                            <p className="text-blue-700">Consider adding force majeure provisions</p>
                          </div>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="space-y-2">
                        <Button className="w-full text-sm">
                          Continue with Template
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                        <Button variant="outline" className="w-full text-sm">
                          <Plus className="w-4 h-4 mr-2" />
                          Customize Template
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Contract Details */}
              {currentStep === 1 && (
                <div className="grid lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-2xl font-bold mb-6">Contract Details</h3>
                    <div className="space-y-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Contract Title</label>
                        <div className="p-3 bg-muted/50 border border-border rounded-lg">
                          <span className="text-foreground">Service Agreement - TechCorp Inc.</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Client</label>
                          <div className="p-3 bg-muted/50 border border-border rounded-lg">
                            <span className="text-foreground">TechCorp Inc.</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Value</label>
                          <div className="p-3 bg-muted/50 border border-border rounded-lg">
                            <span className="text-foreground">$50,000</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Project Description</label>
                        <div className="p-3 bg-muted/50 border border-border rounded-lg min-h-[80px]">
                          <span className="text-foreground text-sm">
                            Consulting services for digital transformation project including system analysis, implementation planning, and staff training.
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold mb-6">AI Suggestions</h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Brain className="w-4 h-4 text-blue-600" />
                          <span className="font-medium text-blue-900">Smart Recommendation</span>
                        </div>
                        <p className="text-sm text-blue-700">Consider adding milestone-based payments for better risk management.</p>
                      </div>
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="font-medium text-green-900">Compliance Check</span>
                        </div>
                        <p className="text-sm text-green-700">All required clauses for your jurisdiction are included.</p>
                      </div>
                      <Button className="w-full">
                        Generate Document
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Enhanced Document Preview/Editor */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-2xl font-bold">AI-Powered Document Editor</h3>
                      <p className="text-muted-foreground">Real-time analysis and optimization</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1 text-green-600">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-sm">AI Analyzing...</span>
                      </div>
                      <div className="flex items-center gap-1 text-blue-600">
                        <BarChart3 className="w-4 h-4" />
                        <span className="text-sm font-medium">Risk Score: 92%</span>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Editor Toolbar */}
                  <div className="flex items-center justify-between p-3 bg-muted/50 border border-border rounded-lg">
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <Edit3 className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <div className="w-px h-6 bg-border mx-2"></div>
                      <Button size="sm" variant="outline">Bold</Button>
                      <Button size="sm" variant="outline">Italic</Button>
                      <div className="w-px h-6 bg-border mx-2"></div>
                      <Button size="sm" variant="outline">
                        <BookOpen className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Brain className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <Download className="w-4 h-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Share2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Enhanced Document Content */}
                  <div className="grid lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2 space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold">Live Document with AI Analysis</h4>
                        <div className="flex items-center gap-2 text-xs">
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">Auto-save enabled</span>
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">AI monitoring</span>
                        </div>
                      </div>
                      <div className="bg-background border border-border rounded-lg p-6 min-h-[500px] space-y-4">
                        <div className="text-center mb-6">
                          <h5 className="text-lg font-bold">SERVICE AGREEMENT</h5>
                          <p className="text-sm text-muted-foreground">Between TechCorp Inc. and Service Provider</p>
                        </div>
                        <div className="space-y-4 text-sm">
                          <div>
                            <p><strong>1. SCOPE OF SERVICES</strong></p>
                            <div className="relative">
                              <p className="text-muted-foreground leading-relaxed bg-green-50 p-2 rounded border-l-4 border-green-400">
                                The Service Provider agrees to provide consulting services for digital transformation project including system analysis, implementation planning, and staff training as detailed in Exhibit A.
                              </p>
                              <div className="absolute -right-2 top-2">
                                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                  <CheckCircle className="w-2.5 h-2.5 text-white" />
                                </div>
                              </div>
                            </div>
                            <div className="bg-blue-50 border-l-4 border-blue-400 p-3 my-3">
                              <div className="flex items-center gap-2 text-blue-700">
                                <Brain className="w-4 h-4" />
                                <span className="text-xs font-medium">AI Enhancement Applied</span>
                              </div>
                              <p className="text-xs text-blue-600 mt-1">Added specific deliverables reference for legal clarity</p>
                            </div>
                          </div>

                          <div>
                            <p><strong>2. COMPENSATION</strong></p>
                            <div className="relative">
                              <p className="text-muted-foreground leading-relaxed bg-yellow-50 p-2 rounded border-l-4 border-yellow-400">
                                Total compensation of $50,000 payable in milestone-based installments as follows: 25% upon project initiation, 50% upon completion of analysis phase, 25% upon final delivery.
                              </p>
                              <div className="absolute -right-2 top-2">
                                <div className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                                  <AlertTriangle className="w-2.5 h-2.5 text-white" />
                                </div>
                              </div>
                            </div>
                            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 my-3">
                              <div className="flex items-center gap-2 text-yellow-700">
                                <Zap className="w-4 h-4" />
                                <span className="text-xs font-medium">AI Optimization Suggestion</span>
                              </div>
                              <p className="text-xs text-yellow-600 mt-1">Consider adding late payment penalties for better protection</p>
                            </div>
                          </div>

                          <div>
                            <p><strong>3. INTELLECTUAL PROPERTY</strong></p>
                            <div className="relative">
                              <p className="text-muted-foreground leading-relaxed bg-red-50 p-2 rounded border-l-4 border-red-400">
                                All intellectual property created during the engagement shall remain the property of the Client, except for pre-existing IP of the Service Provider.
                              </p>
                              <div className="absolute -right-2 top-2">
                                <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                  <AlertTriangle className="w-2.5 h-2.5 text-white" />
                                </div>
                              </div>
                            </div>
                            <div className="bg-red-50 border-l-4 border-red-400 p-3 my-3">
                              <div className="flex items-center gap-2 text-red-700">
                                <Shield className="w-4 h-4" />
                                <span className="text-xs font-medium">Risk Detected</span>
                              </div>
                              <p className="text-xs text-red-600 mt-1">Consider defining "pre-existing IP" more specifically</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h4 className="font-semibold">Comprehensive AI Analysis</h4>
                      <div className="space-y-3">
                        {/* Risk Assessment */}
                        <div className="p-4 bg-gradient-to-r from-green-50 to-green-25 border border-green-200 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <Shield className="w-4 h-4 text-green-600" />
                              <span className="font-medium text-green-900 text-sm">Risk Assessment</span>
                            </div>
                            <span className="text-lg font-bold text-green-600">92%</span>
                          </div>
                          <div className="w-full bg-green-200 rounded-full h-2 mb-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{width: '92%'}}></div>
                          </div>
                          <p className="text-xs text-green-700">Low risk - Well-structured contract</p>
                        </div>

                        {/* Compliance Check */}
                        <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-25 border border-blue-200 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-blue-600" />
                              <span className="font-medium text-blue-900 text-sm">Compliance Score</span>
                            </div>
                            <span className="text-lg font-bold text-blue-600">95%</span>
                          </div>
                          <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{width: '95%'}}></div>
                          </div>
                          <p className="text-xs text-blue-700">Meets regulatory requirements</p>
                        </div>

                        {/* AI Suggestions */}
                        <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-25 border border-purple-200 rounded-lg">
                          <div className="flex items-center gap-2 mb-3">
                            <Brain className="w-4 h-4 text-purple-600" />
                            <span className="font-medium text-purple-900 text-sm">AI Recommendations</span>
                          </div>
                          <div className="space-y-2 text-xs">
                            <div className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5"></div>
                              <p className="text-purple-700">Add force majeure clause for risk mitigation</p>
                            </div>
                            <div className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5"></div>
                              <p className="text-purple-700">Include dispute resolution mechanism</p>
                            </div>
                            <div className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-1.5"></div>
                              <p className="text-purple-700">Consider confidentiality provisions</p>
                            </div>
                          </div>
                        </div>

                        {/* Performance Metrics */}
                        <div className="p-4 bg-gradient-to-r from-orange-50 to-orange-25 border border-orange-200 rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <BarChart3 className="w-4 h-4 text-orange-600" />
                            <span className="font-medium text-orange-900 text-sm">Performance</span>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <p className="text-orange-700">Analysis Time</p>
                              <p className="font-semibold text-orange-900">2.3s</p>
                            </div>
                            <div>
                              <p className="text-orange-700">Efficiency Gain</p>
                              <p className="font-semibold text-orange-900">40x faster</p>
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="space-y-2">
                          <Button className="w-full text-sm">
                            <Brain className="w-4 h-4 mr-2" />
                            Apply AI Suggestions
                          </Button>
                          <Button variant="outline" className="w-full text-sm">
                            Send for Review
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 4: Enhanced Team Review & Approval Workflow */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-2xl font-bold">Advanced Approval Workflow</h3>
                      <p className="text-muted-foreground">Configurable review process with audit trails</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <Settings className="w-4 h-4 mr-2" />
                        Configure Workflow
                      </Button>
                    </div>
                  </div>

                  <div className="grid lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2 space-y-6">
                      {/* Workflow Progress */}
                      <div className="bg-muted/30 border border-border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold">Sequential Approval Progress</h4>
                          <span className="text-sm bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full">
                            Step 2 of 3 • 67% Complete
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mb-4">
                          <div className="flex-1 bg-green-200 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{width: '67%'}}></div>
                          </div>
                          <span className="text-xs text-muted-foreground">2/3 approved</span>
                        </div>
                        <div className="flex items-center gap-4 text-xs">
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-3 h-3 text-green-600" />
                            <span>Legal Review</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <CheckCircle className="w-3 h-3 text-green-600" />
                            <span>Finance Review</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3 text-yellow-600" />
                            <span>Project Review</span>
                          </div>
                        </div>
                      </div>

                      {/* Detailed Review Status */}
                      <div className="space-y-3">
                        <h4 className="font-semibold">Review Details & Comments</h4>

                        {/* Approved Reviews */}
                        <div className="flex items-start gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
                          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <p className="font-medium">Sarah Johnson - Legal Counsel</p>
                              <div className="flex items-center gap-2">
                                <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">Approved</span>
                                <span className="text-xs text-green-600">2 hours ago</span>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">"Compliance review complete. All regulatory requirements met. IP clause needs minor clarification but acceptable as-is."</p>
                            <div className="flex items-center gap-4 text-xs">
                              <span className="flex items-center gap-1">
                                <Mail className="w-3 h-3" />
                                Email sent
                              </span>
                              <span className="flex items-center gap-1">
                                <Bell className="w-3 h-3" />
                                Notifications enabled
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-start gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
                          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <p className="font-medium">Mike Chen - Finance Director</p>
                              <div className="flex items-center gap-2">
                                <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">Approved</span>
                                <span className="text-xs text-green-600">1 hour ago</span>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">"Payment terms are acceptable. Milestone structure provides good cash flow protection. Budget approved."</p>
                            <div className="flex items-center gap-4 text-xs">
                              <span className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                Scheduled review
                              </span>
                              <span className="flex items-center gap-1">
                                <BarChart3 className="w-3 h-3" />
                                Budget verified
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Pending Review */}
                        <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                            <Clock className="w-5 h-5 text-yellow-600" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <p className="font-medium">Alex Rivera - Project Manager</p>
                              <div className="flex items-center gap-2">
                                <span className="text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded-full">Pending Review</span>
                                <span className="text-xs text-yellow-600">Due in 6 hours</span>
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">Awaiting project scope and deliverables review. Auto-reminder sent 30 minutes ago.</p>
                            <div className="flex items-center gap-4 text-xs">
                              <span className="flex items-center gap-1">
                                <Bell className="w-3 h-3" />
                                Reminder sent
                              </span>
                              <span className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                Delegation available
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-6">
                      {/* Workflow Management */}
                      <div>
                        <h4 className="font-semibold mb-3">Workflow Management</h4>
                        <div className="space-y-3">
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <Settings className="w-4 h-4 text-blue-600" />
                              <span className="text-sm font-medium text-blue-900">Workflow Type</span>
                            </div>
                            <p className="text-xs text-blue-700">Sequential Approval</p>
                            <p className="text-xs text-blue-600 mt-1">Each approver must complete before next</p>
                          </div>

                          <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <Bell className="w-4 h-4 text-purple-600" />
                              <span className="text-sm font-medium text-purple-900">Auto-Reminders</span>
                            </div>
                            <p className="text-xs text-purple-700">Every 4 hours until completion</p>
                            <p className="text-xs text-purple-600 mt-1">Escalation after 24 hours</p>
                          </div>
                        </div>
                      </div>

                      {/* Next Steps */}
                      <div>
                        <h4 className="font-semibold mb-3">Next Steps</h4>
                        <div className="space-y-3">
                          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div className="flex items-center justify-between mb-1">
                              <p className="text-sm font-medium">1. Final Approval</p>
                              <span className="text-xs bg-yellow-200 text-yellow-800 px-2 py-0.5 rounded-full">Active</span>
                            </div>
                            <p className="text-xs text-muted-foreground mb-2">Waiting for Alex Rivera</p>
                            <div className="flex gap-1">
                              <Button size="sm" variant="outline" className="text-xs h-6">
                                <Bell className="w-3 h-3 mr-1" />
                                Send Reminder
                              </Button>
                              <Button size="sm" variant="outline" className="text-xs h-6">
                                <Users className="w-3 h-3 mr-1" />
                                Delegate
                              </Button>
                            </div>
                          </div>

                          <div className="p-3 bg-muted/30 border border-border rounded-lg opacity-60">
                            <div className="flex items-center justify-between mb-1">
                              <p className="text-sm font-medium">2. Digital Signature</p>
                              <span className="text-xs bg-muted text-muted-foreground px-2 py-0.5 rounded-full">Pending</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Send to client for signing</p>
                          </div>

                          <div className="p-3 bg-muted/30 border border-border rounded-lg opacity-60">
                            <div className="flex items-center justify-between mb-1">
                              <p className="text-sm font-medium">3. Contract Execution</p>
                              <span className="text-xs bg-muted text-muted-foreground px-2 py-0.5 rounded-full">Pending</span>
                            </div>
                            <p className="text-xs text-muted-foreground">Archive and track</p>
                          </div>
                        </div>
                      </div>

                      {/* Audit Trail */}
                      <div>
                        <h4 className="font-semibold mb-3">Audit Trail</h4>
                        <div className="space-y-2 text-xs">
                          <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>Document created</span>
                            <span className="text-muted-foreground ml-auto">3h ago</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>Sent for legal review</span>
                            <span className="text-muted-foreground ml-auto">2h ago</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>Legal approval received</span>
                            <span className="text-muted-foreground ml-auto">2h ago</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span>Finance approval received</span>
                            <span className="text-muted-foreground ml-auto">1h ago</span>
                          </div>
                        </div>
                      </div>

                      {/* Repository Integration */}
                      <div>
                        <h4 className="font-semibold mb-3">Repository Actions</h4>
                        <div className="space-y-2">
                          <Button size="sm" variant="outline" className="w-full text-xs">
                            <FolderOpen className="w-3 h-3 mr-2" />
                            Save to Repository
                          </Button>
                          <Button size="sm" variant="outline" className="w-full text-xs">
                            <History className="w-3 h-3 mr-2" />
                            Version History
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Controls */}
            <div className="flex justify-center gap-4 mt-12">
              <Button
                variant="outline"
                onClick={handlePrevStep}
                disabled={currentStep === 0}
                className="px-6"
              >
                Previous Step
              </Button>
              <Button
                onClick={handleNextStep}
                disabled={currentStep === steps.length - 1}
                className="px-6"
              >
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>

          {/* Feature highlights below demo */}
          <div className="grid md:grid-cols-3 gap-8 mt-20">
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-primary/15 to-primary/5 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300 group-hover:shadow-lg">
                <Brain className="w-10 h-10 text-primary" />
              </div>
              <h3 className="text-2xl font-bold mb-4">AI-Powered Analysis</h3>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Advanced AI analyzes contracts in seconds, identifying risks and compliance issues automatically.
              </p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-50 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300 group-hover:shadow-lg">
                <Users className="w-10 h-10 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold mb-4">Team Collaboration</h3>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Work together seamlessly with real-time collaboration and role-based permissions.
              </p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-green-100 to-green-50 rounded-3xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300 group-hover:shadow-lg">
                <CheckCircle className="w-10 h-10 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold mb-4">Compliance Assurance</h3>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Ensure all contracts meet legal standards with automated compliance checking.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductShowcase;
