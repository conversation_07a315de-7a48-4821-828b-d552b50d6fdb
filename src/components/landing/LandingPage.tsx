import { useNavigate, Navigate } from 'react-router-dom';
import { useAuth } from '@clerk/clerk-react';
import { LandingThemeProvider } from '@/lib/landing-theme-provider';
import LandingHeader from './LandingHeader';
import HeroSection from './HeroSection';
import ProductShowcase from './ProductShowcase';

import FeaturesSection from './FeaturesSection';
import BenefitsSection from './BenefitsSection';
import TestimonialsSection from './TestimonialsSection';
import PricingSection from './PricingSection';
import CTASection from './CTASection';
import LandingFooter from './LandingFooter';

const LandingPageContent = () => {
  const navigate = useNavigate();
  const { isSignedIn } = useAuth();

  // Redirect authenticated users to the app
  if (isSignedIn) {
    return <Navigate to="/app/dashboard" replace />;
  }

  const handleGetStarted = () => {
    navigate('/sign-up');
  };

  const handleSignIn = () => {
    navigate('/sign-in');
  };

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      <LandingHeader
        onSignIn={handleSignIn}
        onGetStarted={handleGetStarted}
        onScrollToSection={scrollToSection}
      />
      <HeroSection onGetStarted={handleGetStarted} />
      <ProductShowcase />
      <FeaturesSection />
      <BenefitsSection />
      <TestimonialsSection />
      <PricingSection onGetStarted={handleGetStarted} />
      <CTASection onGetStarted={handleGetStarted} />
      <LandingFooter onScrollToSection={scrollToSection} />
    </div>
  );
};

const LandingPage = () => {
  return (
    <LandingThemeProvider>
      <LandingPageContent />
    </LandingThemeProvider>
  );
};

export default LandingPage;