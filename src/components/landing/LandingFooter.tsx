import { FileText, Twitter, Linkedin, Github, Mail } from 'lucide-react';

interface LandingFooterProps {
  onScrollToSection: (sectionId: string) => void;
}

const LandingFooter = ({ onScrollToSection }: LandingFooterProps) => {
  return (
    <footer className="py-12 sm:py-16 lg:py-20 px-4 sm:px-6 lg:px-12 bg-gradient-to-b from-background to-muted/30 border-t border-border/50 relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10 lg:gap-12 mb-8 sm:mb-10 lg:mb-12">
          {/* Brand */}
          <div className="sm:col-span-2 lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4 sm:mb-6">
              <div className="w-10 sm:w-12 h-10 sm:h-12 bg-primary rounded-xl sm:rounded-2xl flex items-center justify-center">
                <FileText className="w-6 sm:w-7 h-6 sm:h-7 text-primary-foreground" />
              </div>
              <span className="text-2xl sm:text-3xl font-bold">LegalAI</span>
            </div>
            <p className="text-sm sm:text-base lg:text-lg text-muted-foreground leading-relaxed max-w-md mb-4 sm:mb-6">
              Transforming legal contract workflows with AI-powered insights and seamless collaboration.
              Trusted by thousands of legal professionals worldwide.
            </p>
            <div className="flex items-center space-x-3 sm:space-x-4">
              <a href="#" className="w-9 sm:w-10 h-9 sm:h-10 bg-muted/50 hover:bg-primary hover:text-primary-foreground rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                <Twitter className="w-4 sm:w-5 h-4 sm:h-5" />
              </a>
              <a href="#" className="w-9 sm:w-10 h-9 sm:h-10 bg-muted/50 hover:bg-primary hover:text-primary-foreground rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                <Linkedin className="w-4 sm:w-5 h-4 sm:h-5" />
              </a>
              <a href="#" className="w-9 sm:w-10 h-9 sm:h-10 bg-muted/50 hover:bg-primary hover:text-primary-foreground rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                <Github className="w-4 sm:w-5 h-4 sm:h-5" />
              </a>
              <a href="#" className="w-9 sm:w-10 h-9 sm:h-10 bg-muted/50 hover:bg-primary hover:text-primary-foreground rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-110">
                <Mail className="w-4 sm:w-5 h-4 sm:h-5" />
              </a>
            </div>
          </div>

          {/* Product */}
          <div>
            <h4 className="text-base sm:text-lg font-bold mb-4 sm:mb-6 leading-[1.3]">Product</h4>
            <ul className="space-y-3 sm:space-y-4">
              <li>
                <button
                  onClick={() => onScrollToSection('features')}
                  className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium text-left"
                >
                  Features
                </button>
              </li>
              <li>
                <button
                  onClick={() => onScrollToSection('pricing')}
                  className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium text-left"
                >
                  Pricing
                </button>
              </li>
              <li>
                <a href="#" className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium">
                  Security
                </a>
              </li>
              <li>
                <a href="#" className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium">
                  Integrations
                </a>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="text-base sm:text-lg font-bold mb-4 sm:mb-6 leading-[1.3]">Company</h4>
            <ul className="space-y-3 sm:space-y-4">
              <li>
                <a href="#" className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium">
                  About
                </a>
              </li>
              <li>
                <a href="#" className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium">
                  Blog
                </a>
              </li>
              <li>
                <a href="#" className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium">
                  Careers
                </a>
              </li>
              <li>
                <a href="#" className="text-sm sm:text-base text-muted-foreground hover:text-foreground transition-colors font-medium">
                  Contact
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-border/50 pt-6 sm:pt-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <p className="text-sm sm:text-base text-muted-foreground text-center sm:text-left">
              &copy; 2025 LegalAI. All rights reserved.
            </p>
            <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6 text-xs sm:text-sm">
              <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default LandingFooter;
