import { Button } from '@/components/ui/button';
import { FileText, Menu, X } from 'lucide-react';
import { useState } from 'react';

interface LandingHeaderProps {
  onSignIn: () => void;
  onGetStarted: () => void;
  onScrollToSection: (sectionId: string) => void;
}

const LandingHeader = ({ onSignIn, onGetStarted, onScrollToSection }: LandingHeaderProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleNavClick = (sectionId: string) => {
    onScrollToSection(sectionId);
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="border-b border-border/50 sticky top-0 z-50 bg-background/95 backdrop-blur-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="h-16 flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
              <FileText className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-semibold">LegalAI</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <button
              className="text-sm font-medium hover:text-primary transition-colors py-2 px-1"
              onClick={() => onScrollToSection('features')}
            >
              Features
            </button>
            <button
              className="text-sm font-medium hover:text-primary transition-colors py-2 px-1"
              onClick={() => onScrollToSection('pricing')}
            >
              Pricing
            </button>
          </nav>

          {/* Desktop Action Buttons */}
          <div className="hidden md:flex items-center space-x-3">
            <Button variant="ghost" size="sm" onClick={onSignIn} className="font-medium">
              Sign in
            </Button>
            <Button size="sm" onClick={onGetStarted} className="font-medium">
              Get started
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 rounded-md hover:bg-muted transition-colors"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-border/50 bg-background/95 backdrop-blur-sm">
            <div className="px-4 py-6 space-y-4">
              {/* Mobile Navigation */}
              <nav className="space-y-4">
                <button
                  className="block w-full text-left text-base font-medium hover:text-primary transition-colors py-3 px-2 rounded-md hover:bg-muted"
                  onClick={() => handleNavClick('features')}
                >
                  Features
                </button>
                <button
                  className="block w-full text-left text-base font-medium hover:text-primary transition-colors py-3 px-2 rounded-md hover:bg-muted"
                  onClick={() => handleNavClick('pricing')}
                >
                  Pricing
                </button>
              </nav>

              {/* Mobile Action Buttons */}
              <div className="pt-4 space-y-3 border-t border-border/50">
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={onSignIn}
                  className="w-full justify-center font-medium h-12"
                >
                  Sign in
                </Button>
                <Button
                  size="lg"
                  onClick={onGetStarted}
                  className="w-full justify-center font-medium h-12"
                >
                  Get started
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default LandingHeader;
