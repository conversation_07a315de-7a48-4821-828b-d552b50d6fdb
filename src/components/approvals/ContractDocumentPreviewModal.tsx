import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ThumbsUp, ThumbsDown, CheckCircle, X } from 'lucide-react';
import ContractReviewPreview from '../contracts/ContractReviewPreview';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ContractDocumentPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contractId: string;
  contractTitle: string;
  // In a real application, we would fetch the contract content based on the ID
  contractContent?: string;
  showApprovalActions?: boolean;
  onApprove?: (contractId: string, comments?: string) => void;
  onReject?: (contractId: string, reason: string) => void;
}

const ContractDocumentPreviewModal: React.FC<ContractDocumentPreviewModalProps> = ({
  open,
  onOpenChange,
  contractId,
  contractTitle,
  contractContent,
  showApprovalActions = false,
  onApprove,
  onReject
}) => {
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState<boolean>(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState<boolean>(false);
  const [approvalComments, setApprovalComments] = useState<string>("");
  const [rejectionReason, setRejectionReason] = useState<string>("");

  // Handle approve action
  const handleApprove = () => {
    if (onApprove) {
      onApprove(contractId, approvalComments);
    }
    setApprovalComments("");
    setIsApproveDialogOpen(false);
    onOpenChange(false);
  };

  // Handle reject action
  const handleReject = () => {
    if (onReject && rejectionReason.trim()) {
      onReject(contractId, rejectionReason);
    }
    setRejectionReason("");
    setIsRejectDialogOpen(false);
    onOpenChange(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-5xl h-[90vh] p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle>Contract Document</DialogTitle>
          </DialogHeader>
          <div className={showApprovalActions ? "h-[calc(90vh-10rem)]" : "h-[calc(90vh-4rem)]"}>
            <ContractReviewPreview
              contractId={contractId}
              contractTitle={contractTitle}
              contractContent={contractContent}
              readOnly={true}
            />
          </div>

          {showApprovalActions && (
            <DialogFooter className="px-6 py-4 border-t">
              <div className="flex w-full justify-between items-center">
                <div className="text-sm text-muted-foreground">
                  Review the contract carefully before making a decision
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsRejectDialogOpen(true)}
                  >
                    <ThumbsDown className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                  <Button
                    variant="default"
                    onClick={() => setIsApproveDialogOpen(true)}
                  >
                    <ThumbsUp className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                </div>
              </div>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* Approve Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Contract</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <p className="text-sm text-muted-foreground">
              Add optional comments with your approval for &quot;{contractTitle}&quot;.
            </p>
            <Textarea
              placeholder="Add your comments (optional)"
              value={approvalComments}
              onChange={(e) => setApprovalComments(e.target.value)}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Contract</AlertDialogTitle>
            <AlertDialogDescription>
              Please provide a reason for rejecting &quot;{contractTitle}&quot;.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4 py-2">
            <Textarea
              placeholder="Reason for rejection"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={4}
              className={!rejectionReason.trim() ? "border-destructive" : ""}
            />
            {!rejectionReason.trim() && (
              <p className="text-xs text-destructive">A reason is required for rejection</p>
            )}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsRejectDialogOpen(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleReject}
              disabled={!rejectionReason.trim()}
            >
              <X className="mr-2 h-4 w-4" />
              Reject
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ContractDocumentPreviewModal;
