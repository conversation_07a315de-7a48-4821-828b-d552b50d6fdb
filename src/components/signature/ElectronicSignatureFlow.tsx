import React, { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../ui/card";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "../ui/tabs";
import { Textarea } from "../ui/textarea";
import { Badge } from "../ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Progress } from "../ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import {
  CheckCircle2,
  Clock,
  Download,
  Edit2,
  FileText,
  Info,
  Loader2,
  Mail,
  PenTool,
  RefreshCw,
  Send,
  Upload,
  UserCircle,
  X
} from "lucide-react";
import { useApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { useUser } from "@clerk/clerk-react";

// Types for our component
interface Signer {
  id: string;
  name: string;
  email: string;
  role: string;
  status: "pending" | "completed" | "declined" | "current";
  order: number;
  avatar?: string;
  completedAt?: string;
}

interface DocumentWithSigners {
  id: string;
  title: string;
  filename: string;
  dateCreated: string;
  status: "draft" | "in_progress" | "completed" | "declined";
  createdBy: {
    name: string;
    email: string;
    avatar?: string;
  };
  signers: Signer[];
  expiresAt?: string;
}

// Default empty document
const emptyDocument: DocumentWithSigners = {
  id: "",
  title: "",
  filename: "",
  dateCreated: "",
  status: "draft",
  createdBy: {
    name: "",
    email: "",
    avatar: undefined,
  },
  signers: [],
  expiresAt: undefined,
};

interface ElectronicSignatureFlowProps {
  documentId?: string;
  onComplete?: (documentId: string) => void;
  onDecline?: (documentId: string, reason: string) => void;
  onCancel?: () => void;
}

const ElectronicSignatureFlow: React.FC<ElectronicSignatureFlowProps> = ({
  documentId,
  onComplete,
  onDecline,
  onCancel,
}) => {
  // API and user hooks
  const { fetch, api } = useApi();
  const { toast } = useToast();
  const { user } = useUser();

  // State
  const [document, setDocument] = useState<DocumentWithSigners>(emptyDocument);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [signatureMethod, setSignatureMethod] = useState<string>("type");
  const [typedSignature, setTypedSignature] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [isSignatureComplete, setIsSignatureComplete] = useState(false);
  const [isDeclineDialogOpen, setIsDeclineDialogOpen] = useState(false);
  const [declineReason, setDeclineReason] = useState("");
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
  const [isSigning, setIsSigning] = useState(false);

  // Canvas signature
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasSignatureCreated, setCanvasSignatureCreated] = useState(false);

  // Fetch document data
  useEffect(() => {
    const fetchDocument = async () => {
      if (!documentId) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch document data from API
        const result = await fetch(
          () => api.get(`/documents/${documentId}`),
          "Loading document...",
          "Failed to load document"
        );

        if (result) {
          setDocument(result as DocumentWithSigners);
        } else {
          // If the endpoint doesn't exist yet or returns no data, use mock data
          setDocument(emptyDocument);
          setError("Could not load document data. Using sample data instead.");

          // Create mock data for development
          const mockDocument: DocumentWithSigners = {
            id: documentId || "doc-123",
            title: "Service Agreement - Acme Corp",
            filename: "acme-service-agreement.pdf",
            dateCreated: new Date().toISOString().split('T')[0],
            status: "in_progress",
            createdBy: {
              name: "Sarah Johnson",
              email: "<EMAIL>",
              avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=sarah",
            },
            signers: [
              {
                id: "signer-1",
                name: "Sarah Johnson",
                email: "<EMAIL>",
                role: "Contract Manager",
                status: "completed",
                order: 1,
                avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=sarah",
                completedAt: new Date().toISOString(),
              },
              {
                id: "signer-2",
                name: user?.fullName || "Current User",
                email: user?.primaryEmailAddress?.emailAddress || "<EMAIL>",
                role: "Legal Counsel",
                status: "current",
                order: 2,
                avatar: user?.imageUrl,
              },
              {
                id: "signer-3",
                name: "Michael Chen",
                email: "<EMAIL>",
                role: "CFO",
                status: "pending",
                order: 3,
                avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=michael",
              },
            ],
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          };

          setDocument(mockDocument);
        }
      } catch (err) {
        console.error("Error fetching document:", err);
        setError("Failed to load document. Please try again later.");

        // Use empty document when there's an error
        setDocument(emptyDocument);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocument();
  }, [documentId, fetch, api, user]);

  // Get the current signer (assumed to be the current user)
  const currentSigner = document.signers.find(signer => signer.status === "current");

  // Determine if the current user is the current signer
  const isCurrentUserSigner = currentSigner?.email === user?.primaryEmailAddress?.emailAddress;

  // Progress calculation
  const totalSigners = document.signers.length;
  const completedSigners = document.signers.filter(s => s.status === "completed").length;
  const progressPercentage = totalSigners > 0 ? Math.round((completedSigners / totalSigners) * 100) : 0;

  // Canvas drawing functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    setIsDrawing(true);
    ctx.beginPath();
    const rect = canvas.getBoundingClientRect();
    ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.stroke();
    setCanvasSignatureCreated(true);
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setCanvasSignatureCreated(false);
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    // Simulate upload process
    setTimeout(() => {
      setIsUploading(false);
      // In a real app, we would upload the file to a server
      console.log("Uploaded signature file:", file.name);
    }, 1500);
  };

  // Complete signature
  const handleCompleteSignature = async () => {
    if (signatureMethod === "type" && !typedSignature) {
      toast({
        title: "Signature Required",
        description: "Please type your signature before proceeding.",
        variant: "destructive",
      });
      return;
    }

    if (signatureMethod === "draw" && !canvasSignatureCreated) {
      toast({
        title: "Signature Required",
        description: "Please draw your signature before proceeding.",
        variant: "destructive",
      });
      return;
    }

    if (!currentSigner) {
      toast({
        title: "Error",
        description: "No current signer found.",
        variant: "destructive",
      });
      return;
    }

    setIsSigning(true);

    try {
      // Prepare signature data
      let signatureData;

      if (signatureMethod === "type") {
        signatureData = {
          type: "typed",
          value: typedSignature,
        };
      } else if (signatureMethod === "draw") {
        // Get canvas data URL
        const canvas = canvasRef.current;
        if (!canvas) {
          throw new Error("Canvas not found");
        }
        signatureData = {
          type: "drawn",
          value: canvas.toDataURL("image/png"),
        };
      } else {
        signatureData = {
          type: "uploaded",
          value: "signature-file-url", // In a real app, this would be the uploaded file URL
        };
      }

      // Send signature to API
      const result = await fetch(
        () => api.post(`/documents/${document.id}/sign`, {
          signerId: currentSigner.id,
          signature: signatureData,
        }),
        "Submitting signature...",
        "Failed to submit signature"
      );

      if (result) {
        // If API call was successful, update local state with the returned document
        setDocument(result as DocumentWithSigners);
      } else {
        // Fallback to client-side update if API doesn't return updated document
        const updatedSigners = document.signers.map(signer => {
          if (signer.id === currentSigner.id) {
            return {
              ...signer,
              status: "completed" as const,
              completedAt: new Date().toISOString(),
            };
          }

          // Find the next signer in order
          if (signer.status === "pending" && currentSigner) {
            const nextSignerIndex = document.signers.findIndex(s => s.id === currentSigner.id) + 1;
            if (document.signers[nextSignerIndex]?.id === signer.id) {
              return {
                ...signer,
                status: "current" as const,
              };
            }
          }

          return signer;
        });

        const allCompleted = updatedSigners.every(signer => signer.status === "completed");

        const updatedDocument = {
          ...document,
          signers: updatedSigners,
          status: allCompleted ? "completed" as const : "in_progress" as const,
        };

        setDocument(updatedDocument);
      }

      // Show success message
      setIsSignatureComplete(true);
      setIsSuccessDialogOpen(true);

      // Call the onComplete callback
      if (onComplete) {
        onComplete(document.id);
      }
    } catch (err) {
      console.error("Error completing signature:", err);
      toast({
        title: "Error",
        description: "Failed to complete signature. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSigning(false);
    }
  };

  // Decline to sign
  const handleDeclineToSign = async () => {
    if (!declineReason) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for declining to sign.",
        variant: "destructive",
      });
      return;
    }

    if (!currentSigner) {
      toast({
        title: "Error",
        description: "No current signer found.",
        variant: "destructive",
      });
      return;
    }

    setIsSigning(true);

    try {
      // Send decline request to API
      const result = await fetch(
        () => api.post(`/documents/${document.id}/decline`, {
          signerId: currentSigner.id,
          reason: declineReason,
        }),
        "Declining signature...",
        "Failed to decline signature"
      );

      if (result) {
        // If API call was successful, update local state with the returned document
        setDocument(result as DocumentWithSigners);
      } else {
        // Fallback to client-side update if API doesn't return updated document
        const updatedDocument = {
          ...document,
          status: "declined" as const,
          signers: document.signers.map(signer => {
            if (signer.id === currentSigner.id) {
              return {
                ...signer,
                status: "declined" as const,
              };
            }
            return signer;
          }),
        };

        setDocument(updatedDocument);
      }

      // Close the decline dialog
      setIsDeclineDialogOpen(false);

      // Show success message
      toast({
        title: "Document Declined",
        description: "You have declined to sign this document.",
      });

      // Call the onDecline callback
      if (onDecline && document.id) {
        onDecline(document.id, declineReason);
      }
    } catch (err) {
      console.error("Error declining signature:", err);
      toast({
        title: "Error",
        description: "Failed to decline signature. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSigning(false);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="shadow-md">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
            <p className="text-muted-foreground">Loading document...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-12 text-center px-4">
            <X className="h-8 w-8 text-destructive mb-4" />
            <p className="text-muted-foreground mb-2">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : (
          <>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{document.title}</CardTitle>
                  <CardDescription className="mt-1">
                    {document.filename} • Created on {document.dateCreated}
                  </CardDescription>
                </div>
                <Badge
                  className={`
                    ${document.status === "completed" ? "bg-green-600" : ""}
                    ${document.status === "declined" ? "bg-red-600" : ""}
                    ${document.status === "in_progress" ? "bg-blue-600" : ""}
                    ${document.status === "draft" ? "bg-gray-600" : ""}
                  `}
                >
                  {document.status.replace("_", " ").charAt(0).toUpperCase() + document.status.replace("_", " ").slice(1)}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Document signing progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Signature Progress</span>
                  <span>{completedSigners} of {totalSigners} completed</span>
                </div>
                <Progress value={progressPercentage} />
              </div>

              {/* Signers list */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Signers</h3>

                <div className="space-y-2">
                  {document.signers.length === 0 ? (
                    <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg text-center">
                      <UserCircle className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground mb-4">No signers found</p>
                    </div>
                  ) : (
                    document.signers.map((signer) => (
                      <div
                        key={signer.id}
                        className={`
                          flex items-center justify-between p-3 rounded-lg border
                          ${signer.status === "current" ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20" : ""}
                        `}
                      >
                        <div className="flex items-center gap-3">
                          {signer.avatar ? (
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={signer.avatar} alt={signer.name} />
                              <AvatarFallback>{signer.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                            </Avatar>
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                              {signer.name.split(' ').map(n => n[0]).join('')}
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{signer.name}</div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {signer.email}
                            </div>
                            <div className="text-xs text-muted-foreground mt-0.5">{signer.role}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className={`
                              ${signer.status === "completed" ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400" : ""}
                              ${signer.status === "declined" ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400" : ""}
                              ${signer.status === "pending" ? "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-400" : ""}
                              ${signer.status === "current" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400" : ""}
                            `}
                          >
                            {signer.status === "completed" && (
                              <CheckCircle2 className="mr-1 h-3 w-3" />
                            )}
                            {signer.status === "current" && (
                              <PenTool className="mr-1 h-3 w-3" />
                            )}
                            {signer.status === "pending" && (
                              <Clock className="mr-1 h-3 w-3" />
                            )}
                            {signer.status === "declined" && (
                              <X className="mr-1 h-3 w-3" />
                            )}
                            {signer.status.charAt(0).toUpperCase() + signer.status.slice(1)}
                          </Badge>

                          {signer.completedAt && (
                            <span className="text-xs text-muted-foreground">
                              Signed on {new Date(signer.completedAt).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Document expiration */}
              {document.expiresAt && (
                <div className="flex items-center gap-2 text-sm text-amber-600 dark:text-amber-400">
                  <Info className="h-4 w-4" />
                  <span>This document will expire on {new Date(document.expiresAt).toLocaleDateString()}</span>
                </div>
              )}
            </CardContent>
          </>
        )}

        {/* Signature area - only show if current user is the current signer and not loading */}
        {!isLoading && !error && isCurrentUserSigner && !isSignatureComplete && (
          <div className="bg-muted/30 rounded-lg p-4 border mt-6 mx-4 mb-4">
            <h3 className="text-lg font-semibold mb-4">Sign Document</h3>

            <Tabs defaultValue="type" onValueChange={setSignatureMethod}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="type" disabled={isSigning}>Type</TabsTrigger>
                <TabsTrigger value="draw" disabled={isSigning}>Draw</TabsTrigger>
                <TabsTrigger value="upload" disabled={isSigning}>Upload</TabsTrigger>
              </TabsList>

              <TabsContent value="type" className="mt-4 space-y-4">
                <div className="space-y-2">
                  <label htmlFor="type-signature" className="text-sm font-medium">
                    Type your full name to sign
                  </label>
                  <Input
                    id="type-signature"
                    placeholder="Type your full name"
                    className="font-handwriting text-lg"
                    value={typedSignature}
                    onChange={(e) => setTypedSignature(e.target.value)}
                    disabled={isSigning}
                  />
                </div>

                {typedSignature && (
                  <div className="p-4 border rounded-md bg-white">
                    <p className="font-handwriting text-2xl text-center">{typedSignature}</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="draw" className="mt-4 space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      Draw your signature below
                    </label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearCanvas}
                      disabled={isSigning}
                    >
                      Clear
                    </Button>
                  </div>

                  <div className="border rounded-md bg-white p-0">
                    <canvas
                      ref={canvasRef}
                      width={568}
                      height={200}
                      className={`w-full ${isSigning ? 'cursor-not-allowed' : 'cursor-crosshair'}`}
                      onMouseDown={isSigning ? undefined : startDrawing}
                      onMouseMove={isSigning ? undefined : draw}
                      onMouseUp={isSigning ? undefined : endDrawing}
                      onMouseLeave={isSigning ? undefined : endDrawing}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="upload" className="mt-4 space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Upload your signature image
                  </label>

                  <div className="border rounded-md bg-white p-8 flex flex-col items-center justify-center">
                    {isUploading || isSigning ? (
                      <div className="text-center">
                        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">
                          {isUploading ? "Uploading signature..." : "Processing signature..."}
                        </p>
                      </div>
                    ) : (
                      <>
                        <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground mb-2">
                          Drag and drop a signature image, or click to browse
                        </p>
                        <Input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          id="signature-upload"
                          onChange={handleFileUpload}
                          disabled={isSigning}
                        />
                        <Button variant="outline" size="sm" asChild disabled={isSigning}>
                          <label htmlFor="signature-upload" className={isSigning ? 'cursor-not-allowed' : 'cursor-pointer'}>
                            <Upload className="mr-2 h-4 w-4" />
                            Select File
                          </label>
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        <CardFooter className="flex-col sm:flex-row gap-2 sm:justify-between">
          <div className="flex items-center gap-2">
            <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
              {document.createdBy.name.split(' ').map(n => n[0]).join('')}
            </div>
            <span className="text-sm text-muted-foreground">
              Created by {document.createdBy.name}
            </span>
          </div>

          <div className="flex gap-2">
            {!isLoading && !error && isCurrentUserSigner && !isSignatureComplete && (
              <>
                <Button
                  variant="outline"
                  onClick={() => setIsDeclineDialogOpen(true)}
                  disabled={isSigning}
                >
                  {isSigning ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Decline to Sign"
                  )}
                </Button>
                <Button
                  onClick={handleCompleteSignature}
                  disabled={isSigning || (signatureMethod === "type" && !typedSignature) || (signatureMethod === "draw" && !canvasSignatureCreated)}
                >
                  {isSigning ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Complete Signature"
                  )}
                </Button>
              </>
            )}

            {!isLoading && !error && !isCurrentUserSigner && (
              <>
                <Button variant="outline" onClick={onCancel}>
                  Close
                </Button>
                <Button>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </>
            )}

            {!isLoading && !error && isSignatureComplete && (
              <>
                <Button variant="outline" onClick={onCancel}>
                  Close
                </Button>
                <Button>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </>
            )}
          </div>
        </CardFooter>
      </Card>

      {/* Decline dialog */}
      <AlertDialog open={isDeclineDialogOpen} onOpenChange={setIsDeclineDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Decline to Sign</AlertDialogTitle>
            <AlertDialogDescription>
              Please provide a reason for declining to sign this document. This will be shared with all signers.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <Textarea
            placeholder="Enter your reason for declining..."
            value={declineReason}
            onChange={(e) => setDeclineReason(e.target.value)}
            className="min-h-[120px]"
            disabled={isSigning}
          />

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSigning}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={handleDeclineToSign}
              disabled={!declineReason || isSigning}
            >
              {isSigning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Decline to Sign"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success dialog */}
      <Dialog open={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Signature Complete</DialogTitle>
            <DialogDescription>
              You have successfully signed the document. A copy has been sent to your email.
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center justify-center py-6">
            <div className="rounded-full bg-green-100 p-3">
              <CheckCircle2 className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Document:</span>
              <span className="font-medium">{document.title}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Completed:</span>
              <span className="font-medium">{new Date().toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Status:</span>
              <span className="font-medium">
                {completedSigners === totalSigners ? "All signatures complete" : "Waiting for additional signatures"}
              </span>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setIsSuccessDialogOpen(false)}>
              Close
            </Button>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Download Copy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ElectronicSignatureFlow;