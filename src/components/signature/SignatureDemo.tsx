import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import ElectronicSignatureFlow from "./ElectronicSignatureFlow";
import { FileText, PenTool, CheckCircle2 } from "lucide-react";

/**
 * A demo component that showcases the electronic signature flow.
 * It provides a simple interface to start the signature process and view the results.
 */
const SignatureDemo: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("start");
  const [documentId, setDocumentId] = useState<string>("doc-123");
  const [isComplete, setIsComplete] = useState<boolean>(false);
  
  // <PERSON>le starting the signature process
  const handleStartSignature = () => {
    setActiveTab("sign");
  };
  
  // <PERSON>le completing the signature process
  const handleCompleteSignature = (docId: string) => {
    setIsComplete(true);
    setActiveTab("complete");
    
    toast({
      title: "Signature Complete",
      description: `Document ${docId} has been signed successfully.`,
    });
  };
  
  // Handle declining to sign
  const handleDeclineSignature = (docId: string, reason: string) => {
    setActiveTab("complete");
    
    toast({
      title: "Signature Declined",
      description: `You declined to sign document ${docId}. Reason: ${reason}`,
      variant: "destructive",
    });
  };
  
  // Handle canceling the signature process
  const handleCancelSignature = () => {
    setActiveTab("start");
  };
  
  // Reset the demo
  const handleReset = () => {
    setActiveTab("start");
    setIsComplete(false);
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Electronic Signature Demo</CardTitle>
        <CardDescription>
          This demo showcases the electronic signature flow for documents.
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="start" disabled={activeTab === "sign"}>
              Start
            </TabsTrigger>
            <TabsTrigger value="sign" disabled={activeTab === "start" || activeTab === "complete"}>
              Sign
            </TabsTrigger>
            <TabsTrigger value="complete" disabled={!isComplete && activeTab !== "complete"}>
              Complete
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="start" className="py-4">
            <div className="flex flex-col items-center justify-center space-y-6 py-8">
              <div className="rounded-full bg-primary/10 p-6">
                <FileText className="h-12 w-12 text-primary" />
              </div>
              
              <div className="text-center space-y-2">
                <h3 className="text-xl font-semibold">Ready to Sign</h3>
                <p className="text-muted-foreground">
                  Click the button below to start the electronic signature process.
                </p>
              </div>
              
              <Button size="lg" onClick={handleStartSignature}>
                Start Signing
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="sign" className="py-4">
            <ElectronicSignatureFlow
              documentId={documentId}
              onComplete={handleCompleteSignature}
              onDecline={handleDeclineSignature}
              onCancel={handleCancelSignature}
            />
          </TabsContent>
          
          <TabsContent value="complete" className="py-4">
            <div className="flex flex-col items-center justify-center space-y-6 py-8">
              <div className="rounded-full bg-green-100 p-6">
                <CheckCircle2 className="h-12 w-12 text-green-600" />
              </div>
              
              <div className="text-center space-y-2">
                <h3 className="text-xl font-semibold">Process Complete</h3>
                <p className="text-muted-foreground">
                  The signature process has been completed. You can reset the demo to try again.
                </p>
              </div>
              
              <Button variant="outline" size="lg" onClick={handleReset}>
                Reset Demo
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default SignatureDemo;
