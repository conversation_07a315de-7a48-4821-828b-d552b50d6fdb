import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  Bell,
  Calendar,
  Check,
  CheckCheck,
  Clock,
  FileText,
  MoreHorizontal,
  Settings,
  Trash,
  User,
  X,
} from "lucide-react";

// Types
interface Notification {
  id: string;
  title: string;
  message: string;
  type: "approval" | "contract" | "system" | "mention";
  status: "unread" | "read";
  timestamp: string;
  actionUrl?: string;
  sender?: {
    name: string;
    avatar?: string;
    initials: string;
  };
  entityId?: string;
  entityType?: string;
}

interface NotificationsCenterProps {
  notifications?: Notification[];
  onMarkAsRead?: (notificationId: string) => void;
  onMarkAllAsRead?: () => void;
  onDeleteNotification?: (notificationId: string) => void;
  onDeleteAllNotifications?: () => void;
  onSettingsClick?: () => void;
}

const NotificationsCenter: React.FC<NotificationsCenterProps> = ({
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onDeleteAllNotifications,
  onSettingsClick,
}) => {
  // State
  const [activeTab, setActiveTab] = useState<string>("all");
  const [isDeleteAllDialogOpen, setIsDeleteAllDialogOpen] = useState<boolean>(false);

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === "all") return true;
    if (activeTab === "unread") return notification.status === "unread";
    return notification.type === activeTab;
  });

  // Count unread notifications
  const unreadCount = notifications.filter(notification => notification.status === "unread").length;

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? "minute" : "minutes"} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? "hour" : "hours"} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} ${diffInDays === 1 ? "day" : "days"} ago`;
    } else {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  };

  // Get notification icon
  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "approval":
        return <Check className="h-4 w-4 text-green-500" />;
      case "contract":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "system":
        return <Bell className="h-4 w-4 text-amber-500" />;
      case "mention":
        return <User className="h-4 w-4 text-purple-500" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between p-3 border-b">
        <div className="flex items-center gap-2">
          <h2 className="text-sm font-medium">Notifications</h2>
          {unreadCount > 0 && (
            <Badge variant="default" className="bg-blue-500 text-xs">
              {unreadCount} new
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-7 text-xs"
            onClick={onMarkAllAsRead}
            disabled={unreadCount === 0}
          >
            <CheckCheck className="h-3 w-3 mr-1" />
            Mark All Read
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={() => setIsDeleteAllDialogOpen(true)}
            disabled={notifications.length === 0}
          >
            <Trash className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={onSettingsClick}
          >
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 h-8">
          <TabsTrigger value="all" className="text-xs py-1">
            All
            {notifications.length > 0 && (
              <Badge variant="outline" className="ml-1 text-[10px] px-1 py-0">
                {notifications.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="text-xs py-1">
            Unread
            {unreadCount > 0 && (
              <Badge variant="outline" className="ml-1 text-[10px] px-1 py-0">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="approval" className="text-xs py-1">Approvals</TabsTrigger>
          <TabsTrigger value="contract" className="text-xs py-1">Contracts</TabsTrigger>
          <TabsTrigger value="mention" className="text-xs py-1">Mentions</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-2">
          <Card className="border-0 shadow-none">
            <CardContent className="p-0">
              {filteredNotifications.length > 0 ? (
                <ScrollArea className="h-[400px]">
                  <div className="divide-y">
                    {filteredNotifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-3 hover:bg-muted/50 ${notification.status === "unread" ? "bg-blue-50/50 dark:bg-blue-950/20" : ""}`}
                      >
                        <div className="flex items-start gap-4">
                          <div className="mt-0.5">
                            {notification.sender ? (
                              <Avatar className="h-7 w-7">
                                {notification.sender.avatar ? (
                                  <AvatarImage src={notification.sender.avatar} alt={notification.sender.name} />
                                ) : (
                                  <AvatarFallback className="text-xs">
                                    {notification.sender.initials}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                            ) : (
                              <div className="h-7 w-7 rounded-full bg-muted flex items-center justify-center">
                                {getNotificationIcon(notification.type)}
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-medium text-sm">{notification.title}</h3>
                                <p className="text-xs text-muted-foreground mt-0.5">
                                  {notification.message}
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="text-xs text-muted-foreground flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {formatDate(notification.timestamp)}
                                </div>
                                <div className="flex items-center">
                                  {notification.status === "unread" && (
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-7 w-7"
                                      onClick={() => onMarkAsRead && onMarkAsRead(notification.id)}
                                    >
                                      <Check className="h-4 w-4" />
                                    </Button>
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-7 w-7 text-destructive"
                                    onClick={() => onDeleteNotification && onDeleteNotification(notification.id)}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                            {notification.actionUrl && (
                              <div className="mt-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 text-xs px-2 py-0"
                                >
                                  View Details
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                  <h3 className="text-sm font-medium mb-1">No notifications</h3>
                  <p className="text-xs text-muted-foreground">
                    {activeTab === "all"
                      ? "You don't have any notifications yet"
                      : activeTab === "unread"
                        ? "You don't have any unread notifications"
                        : `You don't have any ${activeTab} notifications`}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete All Confirmation Dialog */}
      <AlertDialog open={isDeleteAllDialogOpen} onOpenChange={setIsDeleteAllDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Clear All Notifications</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to clear all notifications? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteAllDialogOpen(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (onDeleteAllNotifications) onDeleteAllNotifications();
                setIsDeleteAllDialogOpen(false);
              }}
              className="bg-destructive text-destructive-foreground"
            >
              <Trash className="h-4 w-4 mr-2" />
              Clear All
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export type { Notification };
export default NotificationsCenter;
