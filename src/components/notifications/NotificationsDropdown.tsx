import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Bell } from "lucide-react";
import NotificationsCenter, { Notification } from "./NotificationsCenter";

interface NotificationsDropdownProps {
  notifications: Notification[];
  unreadCount: number;
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (notificationId: string) => void;
  onDeleteAllNotifications: () => void;
  onSettingsClick: () => void;
}

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({
  notifications,
  unreadCount,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onDeleteAllNotifications,
  onSettingsClick,
}) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-muted/50 transition-colors"
        >
          <Bell className="h-5 w-5 opacity-70 hover:opacity-100 transition-opacity" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-destructive text-[10px] font-medium text-destructive-foreground flex items-center justify-center shadow-sm">
              {unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[380px] p-0 mr-4"
        align="end"
        sideOffset={16}
      >
        <div className="max-h-[80vh] overflow-hidden">
          <NotificationsCenter
            notifications={notifications}
            onMarkAsRead={onMarkAsRead}
            onMarkAllAsRead={onMarkAllAsRead}
            onDeleteNotification={onDeleteNotification}
            onDeleteAllNotifications={onDeleteAllNotifications}
            onSettingsClick={onSettingsClick}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default NotificationsDropdown;
