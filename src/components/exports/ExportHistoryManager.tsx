import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@clerk/clerk-react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import {
  Download,
  Calendar,
  FileText,
  User,
  Clock,
  Search,
  Filter,
  Refresh<PERSON>w,
  ExternalLink,
  Trash2,
  Archive,
  Eye,
} from 'lucide-react';

interface ExportRecord {
  id: string;
  contract_id: string;
  contract_title: string;
  format: string;
  file_size: number;
  download_url: string;
  exported_by: {
    id: string;
    name: string;
  };
  exported_at: string;
  expires_at?: string;
  download_count: number;
  status: 'active' | 'expired' | 'archived';
  template_used?: string;
  branding_settings?: any;
}

interface ExportHistoryManagerProps {
  contractId?: string; // If provided, show history for specific contract
}

const ExportHistoryManager: React.FC<ExportHistoryManagerProps> = ({
  contractId,
}) => {
  const [exports, setExports] = useState<ExportRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [formatFilter, setFormatFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('30days');
  const [selectedExport, setSelectedExport] = useState<ExportRecord | null>(null);
  const { toast } = useToast();
  const { getToken } = useAuth();
  const { currentWorkspace } = useClerkWorkspace();

  useEffect(() => {
    if (currentWorkspace) {
      loadExportHistory();
    }
  }, [currentWorkspace, contractId, formatFilter, statusFilter, dateRange]);

  const loadExportHistory = async () => {
    if (!currentWorkspace) return;

    setIsLoading(true);
    try {
      // In a real implementation, this would call an API endpoint
      // For now, we'll simulate the data
      const mockExports: ExportRecord[] = [
        {
          id: 'export-1',
          contract_id: 'contract-123',
          contract_title: 'Service Agreement - TechCorp',
          format: 'PDF',
          file_size: 245760,
          download_url: 'https://example.com/exports/contract-123.pdf',
          exported_by: { id: 'user-1', name: 'John Doe' },
          exported_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
          download_count: 3,
          status: 'active',
          template_used: 'Professional Template',
        },
        {
          id: 'export-2',
          contract_id: 'contract-456',
          contract_title: 'Employment Contract - Jane Smith',
          format: 'DOCX',
          file_size: 156432,
          download_url: 'https://example.com/exports/contract-456.docx',
          exported_by: { id: 'user-2', name: 'Sarah Wilson' },
          exported_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
          download_count: 1,
          status: 'active',
        },
        {
          id: 'export-3',
          contract_id: 'contract-789',
          contract_title: 'NDA - Confidential Project',
          format: 'PDF',
          file_size: 198765,
          download_url: 'https://example.com/exports/contract-789.pdf',
          exported_by: { id: 'user-1', name: 'John Doe' },
          exported_at: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString(),
          expires_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          download_count: 5,
          status: 'expired',
        },
      ];

      // Apply filters
      let filteredExports = mockExports;
      
      if (contractId) {
        filteredExports = filteredExports.filter(exp => exp.contract_id === contractId);
      }
      
      if (formatFilter !== 'all') {
        filteredExports = filteredExports.filter(exp => exp.format.toLowerCase() === formatFilter);
      }
      
      if (statusFilter !== 'all') {
        filteredExports = filteredExports.filter(exp => exp.status === statusFilter);
      }
      
      if (searchQuery) {
        filteredExports = filteredExports.filter(exp =>
          exp.contract_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          exp.exported_by.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // Apply date range filter
      const now = new Date();
      const dateThreshold = new Date();
      switch (dateRange) {
        case '7days':
          dateThreshold.setDate(now.getDate() - 7);
          break;
        case '30days':
          dateThreshold.setDate(now.getDate() - 30);
          break;
        case '90days':
          dateThreshold.setDate(now.getDate() - 90);
          break;
        default:
          dateThreshold.setFullYear(now.getFullYear() - 1);
      }
      
      filteredExports = filteredExports.filter(exp => 
        new Date(exp.exported_at) >= dateThreshold
      );

      setExports(filteredExports);
    } catch (error) {
      console.error('Failed to load export history:', error);
      toast({
        title: 'Error',
        description: 'Failed to load export history',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (exportRecord: ExportRecord) => {
    try {
      // In a real implementation, this would handle secure download
      window.open(exportRecord.download_url, '_blank');
      
      // Update download count
      setExports(prev => prev.map(exp => 
        exp.id === exportRecord.id 
          ? { ...exp, download_count: exp.download_count + 1 }
          : exp
      ));
      
      toast({
        title: 'Download Started',
        description: `Downloading ${exportRecord.contract_title}`,
      });
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: 'Failed to download the file',
        variant: 'destructive',
      });
    }
  };

  const handleArchive = async (exportId: string) => {
    try {
      setExports(prev => prev.map(exp => 
        exp.id === exportId 
          ? { ...exp, status: 'archived' as const }
          : exp
      ));
      
      toast({
        title: 'Export Archived',
        description: 'The export has been archived',
      });
    } catch (error) {
      toast({
        title: 'Archive Failed',
        description: 'Failed to archive the export',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async (exportId: string) => {
    try {
      setExports(prev => prev.filter(exp => exp.id !== exportId));
      
      toast({
        title: 'Export Deleted',
        description: 'The export has been permanently deleted',
      });
    } catch (error) {
      toast({
        title: 'Delete Failed',
        description: 'Failed to delete the export',
        variant: 'destructive',
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status: string, expiresAt?: string) => {
    if (status === 'expired' || (expiresAt && new Date(expiresAt) < new Date())) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    if (status === 'archived') {
      return <Badge variant="secondary">Archived</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  const getFormatIcon = (format: string) => {
    return <FileText className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {contractId ? 'Contract Export History' : 'Export History'}
          </h2>
          <p className="text-muted-foreground">
            Track and manage document exports
          </p>
        </div>
        <Button onClick={loadExportHistory} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search exports..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Format</Label>
              <Select value={formatFilter} onValueChange={setFormatFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Formats</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="docx">DOCX</SelectItem>
                  <SelectItem value="html">HTML</SelectItem>
                  <SelectItem value="txt">TXT</SelectItem>
                  <SelectItem value="markdown">Markdown</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">Last 7 days</SelectItem>
                  <SelectItem value="30days">Last 30 days</SelectItem>
                  <SelectItem value="90days">Last 90 days</SelectItem>
                  <SelectItem value="1year">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Summary</Label>
              <div className="text-sm text-muted-foreground">
                {exports.length} export{exports.length !== 1 ? 's' : ''} found
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Export History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Export Records
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading export history...</p>
            </div>
          ) : exports.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No exports found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contract</TableHead>
                  <TableHead>Format</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Exported By</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Downloads</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {exports.map((exportRecord) => (
                  <TableRow key={exportRecord.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getFormatIcon(exportRecord.format)}
                        <div>
                          <div className="font-medium">{exportRecord.contract_title}</div>
                          {exportRecord.template_used && (
                            <div className="text-sm text-muted-foreground">
                              Template: {exportRecord.template_used}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{exportRecord.format}</Badge>
                    </TableCell>
                    <TableCell>{formatFileSize(exportRecord.file_size)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {exportRecord.exported_by.name}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(exportRecord.exported_at)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Download className="h-4 w-4" />
                        {exportRecord.download_count}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(exportRecord.status, exportRecord.expires_at)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedExport(exportRecord)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Export Details</DialogTitle>
                              <DialogDescription>
                                Detailed information about this export
                              </DialogDescription>
                            </DialogHeader>
                            {selectedExport && (
                              <div className="space-y-4">
                                <div>
                                  <Label>Contract</Label>
                                  <p className="text-sm">{selectedExport.contract_title}</p>
                                </div>
                                <div>
                                  <Label>Format</Label>
                                  <p className="text-sm">{selectedExport.format}</p>
                                </div>
                                <div>
                                  <Label>Exported</Label>
                                  <p className="text-sm">{formatDateTime(selectedExport.exported_at)}</p>
                                </div>
                                {selectedExport.expires_at && (
                                  <div>
                                    <Label>Expires</Label>
                                    <p className="text-sm">{formatDateTime(selectedExport.expires_at)}</p>
                                  </div>
                                )}
                                <div>
                                  <Label>File Size</Label>
                                  <p className="text-sm">{formatFileSize(selectedExport.file_size)}</p>
                                </div>
                                <div>
                                  <Label>Download Count</Label>
                                  <p className="text-sm">{selectedExport.download_count}</p>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                        
                        {exportRecord.status === 'active' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownload(exportRecord)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                        
                        {exportRecord.status === 'active' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleArchive(exportRecord.id)}
                          >
                            <Archive className="h-4 w-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(exportRecord.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ExportHistoryManager;
