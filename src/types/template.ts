// Shared template interface for use across the application
export interface Template {
  id: string;
  title: string;
  description: string;
  type: string;
  complexity: "simple" | "medium" | "complex";
  lastUpdated: string;
  usageCount: number;
  industry?: string;
  rating?: number;
  tags?: string[];
  icon?: string;
  isUserCreated?: boolean; // Flag to identify user-created templates
  createdBy?: {
    name: string;
    id?: string;
  };
  folderId?: string; // For repository integration
}

// Template store to manage templates across the application
class TemplateStore {
  private templates: Template[] = [
    {
      id: "t1",
      title: "Service Agreement",
      description: "Standard service agreement for consulting and professional services",
      type: "Service Agreement",
      complexity: "medium",
      lastUpdated: "2023-05-15",
      usageCount: 124,
      industry: "Professional Services",
      rating: 4.8,
      tags: ["Services", "Consulting", "B2B"],
      icon: "service",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t2",
      title: "Non-Disclosure Agreement",
      description: "Confidentiality agreement to protect sensitive information",
      type: "NDA",
      complexity: "simple",
      lastUpdated: "2023-06-10",
      usageCount: 256,
      industry: "Technology",
      rating: 4.9,
      tags: ["Confidentiality", "IP Protection", "Standard"],
      icon: "nda",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t3",
      title: "Employment Contract",
      description: "Standard employment agreement with customizable terms",
      type: "Employment",
      complexity: "medium",
      lastUpdated: "2023-04-22",
      usageCount: 89,
      industry: "HR",
      rating: 4.7,
      tags: ["Employment", "HR", "Onboarding"],
      icon: "employment",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t4",
      title: "Software License Agreement",
      description: "License agreement for software products and services",
      type: "License",
      complexity: "complex",
      lastUpdated: "2023-03-18",
      usageCount: 67,
      industry: "Technology",
      rating: 4.6,
      tags: ["Software", "Licensing", "Technology"],
      icon: "software",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t5",
      title: "Lease Agreement",
      description: "Property lease agreement with standard terms and conditions",
      type: "Lease",
      complexity: "medium",
      lastUpdated: "2023-05-30",
      usageCount: 112,
      industry: "Real Estate",
      rating: 4.5,
      tags: ["Real Estate", "Leasing", "Property"],
      icon: "lease",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t6",
      title: "Manufacturing Agreement",
      description: "Agreement between a manufacturer and a client for product creation",
      type: "Manufacturing",
      complexity: "complex",
      lastUpdated: "2023-02-15",
      usageCount: 45,
      industry: "Manufacturing",
      rating: 4.3,
      tags: ["Manufacturing", "Supply Chain", "Production"],
      icon: "manufacturing",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t7",
      title: "Sales Contract",
      description: "Agreement for the sale of goods or services",
      type: "Sales",
      complexity: "simple",
      lastUpdated: "2023-04-05",
      usageCount: 135,
      industry: "Retail",
      rating: 4.7,
      tags: ["Sales", "Retail", "E-commerce"],
      icon: "sales",
      folderId: "folder-4" // Templates folder
    },
    {
      id: "t8",
      title: "Blank Contract",
      description: "Start with a blank template and build your contract from scratch",
      type: "Custom",
      complexity: "simple",
      lastUpdated: "2023-06-15",
      usageCount: 178,
      tags: ["Custom", "Blank", "Flexible"],
      icon: "blank",
      folderId: "folder-4" // Templates folder
    },
  ];

  // Get all templates
  getTemplates(): Template[] {
    return this.templates;
  }

  // Set templates (replace all)
  setTemplates(templates: Template[]): void {
    this.templates = templates;
  }

  // Get template by ID
  getTemplateById(id: string): Template | undefined {
    return this.templates.find(template => template.id === id);
  }

  // Add a new template
  addTemplate(template: Omit<Template, 'id'>): Template {
    const newTemplate: Template = {
      ...template,
      id: `t${Date.now()}`,
      lastUpdated: new Date().toISOString().split('T')[0],
      usageCount: 0,
      folderId: "folder-4" // Templates folder
    };

    this.templates.unshift(newTemplate);
    this.saveToLocalStorage();
    return newTemplate;
  }

  // Update template usage count
  incrementUsageCount(id: string): void {
    const template = this.templates.find(t => t.id === id);
    if (template) {
      template.usageCount += 1;
      this.saveToLocalStorage();
    }
  }

  // Save templates to localStorage
  saveToLocalStorage(): void {
    try {
      localStorage.setItem('templates', JSON.stringify(this.templates));
    } catch (error) {
      console.error('Error saving templates to localStorage:', error);
    }
  }

  // Load templates from localStorage
  loadFromLocalStorage(): void {
    try {
      const storedTemplates = localStorage.getItem('templates');
      if (storedTemplates) {
        this.templates = JSON.parse(storedTemplates);
      }
    } catch (error) {
      console.error('Error loading templates from localStorage:', error);
    }
  }
}

export const templateStore = new TemplateStore();
