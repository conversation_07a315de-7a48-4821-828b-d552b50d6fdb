import React from "react";
import React<PERSON><PERSON> from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import App from "./App";
import "./index.css";
import ErrorBoundary from "./components/error-boundary";
import { <PERSON><PERSON><PERSON>ider } from "./lib/clerk-provider";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <ClerkProvider>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </ClerkProvider>
    </ErrorBoundary>
  </React.StrictMode>,
);
