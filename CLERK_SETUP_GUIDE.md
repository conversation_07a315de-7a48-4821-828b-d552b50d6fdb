# 🔐 Clerk Authentication Setup Guide

## Current Status

✅ **Demo Data**: Successfully seeded with 5 users, 4 workspaces, 3 templates, and 3 contracts  
✅ **Backend API**: Running with demo endpoints accessible  
✅ **Clerk Integration**: Partially configured - needs secret key  
✅ **Frontend**: Clerk publishable key configured  
✅ **Test Page**: Created for verification  

## 🚀 Next Steps to Complete Setup

### Step 1: Get Your Clerk Secret Key

1. **Open Clerk Dashboard**: https://dashboard.clerk.com/
2. **Select your application** (with publishable key: `pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ`)
3. **Go to "API Keys"** in the sidebar
4. **Copy the Secret Key** (starts with `sk_test_`)

### Step 2: Update Backend Configuration

Replace the placeholder in `backend/.env`:

```env
# Change this line:
CLERK_SECRET_KEY=YOUR_ACTUAL_SECRET_KEY_HERE

# To your actual secret key:
CLERK_SECRET_KEY=sk_test_your_actual_secret_key_here
```

### Step 3: Restart Backend Server

```bash
cd backend
python3 -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

### Step 4: Test Authentication

1. **Open the test page**: `test_clerk_setup.html` (already opened in your browser)
2. **Click "Sign Up"** to create a test account
3. **Test API endpoints** to verify authentication is working
4. **Check backend logs** for user creation

### Step 5: Start Frontend Application

```bash
npm run dev
# or
yarn dev
```

## 🔧 What's Been Implemented

### Backend Features

1. **Real Clerk Authentication**
   - JWT token verification
   - User synchronization with database
   - Automatic user creation on first login
   - Workspace assignment for new users

2. **Demo Data Endpoints**
   - `/api/demo/users` - Get demo users
   - `/api/demo/contracts` - Get demo contracts
   - `/api/demo/templates` - Get demo templates
   - `/api/demo/all` - Get all demo data

3. **Webhook Support**
   - User creation webhook: `/api/clerk-webhooks/user-created`
   - User update webhook: `/api/clerk-webhooks/user-updated`
   - User deletion webhook: `/api/clerk-webhooks/user-deleted`

4. **User Service**
   - Unified user management
   - Workspace permissions
   - Dashboard data aggregation

### Frontend Features

1. **Clerk Provider Configuration**
   - Theme integration
   - Error handling for missing keys
   - Proper authentication flow

2. **Onboarding System**
   - 6-step interactive wizard
   - Guided tours for features
   - Context-aware help system

## 🎯 Expected Behavior After Setup

### For New Users
1. **Sign up through Clerk** → Automatically created in database
2. **Added to default workspace** → Can access demo data
3. **Onboarding flow starts** → Guided through features
4. **Full access to demo data** → Contracts, templates, workspaces

### For Demo Users
- Demo users (`*@demo.legalai.com`) remain accessible
- Can be used for testing and demonstrations
- Have realistic data and relationships

### API Access
- **Authenticated endpoints** require valid Clerk JWT
- **Demo endpoints** accessible without authentication
- **Automatic user creation** on first API access

## 🔍 Troubleshooting

### Common Issues

1. **"Not authenticated" errors**
   - Verify Clerk secret key is set correctly
   - Check that frontend has valid publishable key
   - Ensure backend server is running

2. **User not found in database**
   - Check webhook configuration
   - Verify user creation endpoint is working
   - Check backend logs for errors

3. **Frontend not showing data**
   - Verify authentication is working
   - Check API endpoints are accessible
   - Ensure CORS is configured correctly

### Debug Steps

1. **Check backend health**: `curl http://127.0.0.1:8000/api/health`
2. **Test demo endpoints**: `curl http://127.0.0.1:8000/api/demo/users`
3. **Verify Clerk config**: Check test page for initialization errors
4. **Check logs**: Monitor backend console for authentication errors

## 📁 Files Modified/Created

### Backend
- `backend/.env` - Updated with Clerk configuration
- `backend/app/core/auth.py` - Enhanced authentication
- `backend/app/services/user_service.py` - User management service
- `backend/app/api/api_v1/endpoints/clerk_webhooks.py` - Webhook handlers
- `backend/app/api/api_v1/endpoints/demo.py` - Demo data endpoints

### Frontend
- `.env` - Clerk publishable key configured
- `src/lib/clerk-provider.tsx` - Clerk integration
- `src/components/onboarding/` - Complete onboarding system

### Testing
- `test_clerk_setup.html` - Authentication test page
- `CLERK_SETUP_GUIDE.md` - This guide

## 🎉 Once Complete

After completing the setup, you'll have:

- ✅ **Real Clerk authentication** with JWT verification
- ✅ **Automatic user synchronization** between Clerk and database
- ✅ **Demo data accessible** to authenticated users
- ✅ **Comprehensive onboarding** for new users
- ✅ **Workspace-based permissions** and data access
- ✅ **Production-ready authentication** system

Your LegalAI V4 application will be fully functional with both demo data for testing and real authentication for production use!

## 🔗 Useful Links

- **Clerk Dashboard**: https://dashboard.clerk.com/
- **Clerk Documentation**: https://clerk.com/docs
- **Test Page**: `file:///Users/<USER>/Coding%20/LegalAIV4/test_clerk_setup.html`
- **Backend API Docs**: http://127.0.0.1:8000/api/docs (when running)
- **Frontend App**: http://localhost:5173 (when running)
