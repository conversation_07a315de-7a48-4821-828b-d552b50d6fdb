<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimized Workspace Switching Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            color: #718096;
            font-size: 1.1rem;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .test-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .test-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .status-success {
            background: #48bb78;
        }
        .status-warning {
            background: #ed8936;
        }
        .status-info {
            background: #4299e1;
        }
        .demo-section {
            background: #1a202c;
            color: white;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
        }
        .demo-section h3 {
            color: #e2e8f0;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }
        .workspace-simulator {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            background: #2d3748;
            border-radius: 6px;
            overflow: hidden;
            min-height: 400px;
        }
        .sidebar-sim {
            background: #1a202c;
            padding: 20px;
            border-right: 1px solid #4a5568;
        }
        .workspace-dropdown {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }
        .workspace-dropdown:hover {
            background: #4a5568;
        }
        .workspace-dropdown.loading {
            opacity: 0.7;
            cursor: wait;
        }
        .workspace-dropdown.loading::after {
            content: '';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #4a5568;
            border-top: 2px solid #63b3ed;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }
        .workspace-list {
            max-height: 200px;
            overflow-y: auto;
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 6px;
            display: none;
        }
        .workspace-list.show {
            display: block;
        }
        .workspace-item {
            padding: 10px 12px;
            cursor: pointer;
            transition: all 0.15s ease;
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .workspace-item:hover {
            background: #4a5568;
        }
        .workspace-item.active {
            background: #3182ce;
        }
        .workspace-item:last-child {
            border-bottom: none;
        }
        .workspace-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #63b3ed;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #1a202c;
        }
        .content-sim {
            padding: 20px;
            transition: all 0.3s ease;
        }
        .content-sim.loading {
            opacity: 0.6;
            transform: scale(0.98);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric-card {
            background: #4a5568;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #63b3ed;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #a0aec0;
        }
        .performance-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }
        .perf-excellent {
            background: #48bb78;
            color: white;
        }
        .perf-good {
            background: #38b2ac;
            color: white;
        }
        .perf-fair {
            background: #ed8936;
            color: white;
        }
        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 5px;
        }
        .btn:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }
        .btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        .log-output {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success {
            color: #68d391;
        }
        .log-error {
            color: #fc8181;
        }
        .log-info {
            color: #63b3ed;
        }
        .log-warning {
            color: #f6ad55;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Optimized Workspace Switching</h1>
            <p>Performance-enhanced workspace switching with caching, optimistic updates, and smooth transitions</p>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🎯 Performance Optimizations</h3>
                <ul class="feature-list">
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Optimistic UI updates
                        <span class="performance-indicator perf-excellent">&lt;50ms</span>
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Intelligent data caching
                        <span class="performance-indicator perf-excellent">5min TTL</span>
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Debounced rapid switching
                        <span class="performance-indicator perf-good">200ms</span>
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Background data preloading
                        <span class="performance-indicator perf-good">Auto</span>
                    </li>
                </ul>
            </div>

            <div class="test-card">
                <h3>✨ UX Improvements</h3>
                <ul class="feature-list">
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Instant visual feedback
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Smooth transition animations
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Non-blocking loading states
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Graceful error handling
                    </li>
                </ul>
            </div>

            <div class="test-card">
                <h3>🔧 Technical Enhancements</h3>
                <ul class="feature-list">
                    <li>
                        <span class="status-icon status-success">✓</span>
                        LRU cache with memory limits
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Request cancellation
                    </li>
                    <li>
                        <span class="status-icon status-success">✓</span>
                        Automatic rollback on errors
                    </li>
                    <li>
                        <span class="status-icon status-info">i</span>
                        Parallel data fetching
                    </li>
                </ul>
            </div>

            <div class="test-card">
                <h3>📊 Cache Statistics</h3>
                <ul class="feature-list">
                    <li>
                        <span class="status-icon status-info">📦</span>
                        Max cache size: 10 workspaces
                    </li>
                    <li>
                        <span class="status-icon status-info">⏱️</span>
                        Cache duration: 5 minutes
                    </li>
                    <li>
                        <span class="status-icon status-info">🔄</span>
                        Preload delay: 100ms
                    </li>
                    <li>
                        <span class="status-icon status-info">🎯</span>
                        Hit rate: ~85% (estimated)
                    </li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎮 Interactive Demo</h3>
            <p style="color: #a0aec0; margin-bottom: 20px;">
                Experience the optimized workspace switching in action. Notice the instant UI updates and smooth transitions.
            </p>
            
            <div class="workspace-simulator">
                <div class="sidebar-sim">
                    <div class="workspace-dropdown" id="workspace-trigger" onclick="toggleWorkspaceList()">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div class="workspace-avatar" id="current-avatar">L</div>
                            <span id="current-workspace">Legal Department</span>
                        </div>
                    </div>
                    
                    <div class="workspace-list" id="workspace-list">
                        <!-- Populated by JavaScript -->
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn" onclick="simulateRapidSwitching()">Test Rapid Switching</button>
                        <button class="btn" onclick="simulateErrorScenario()">Test Error Handling</button>
                        <button class="btn" onclick="clearCache()">Clear Cache</button>
                    </div>
                </div>
                
                <div class="content-sim" id="content-area">
                    <h4 style="color: #e2e8f0; margin-bottom: 15px;">Workspace Content</h4>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value" id="contracts-count">24</div>
                            <div class="metric-label">Contracts</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="templates-count">8</div>
                            <div class="metric-label">Templates</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="members-count">12</div>
                            <div class="metric-label">Members</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="switch-time">0ms</div>
                            <div class="metric-label">Switch Time</div>
                        </div>
                    </div>
                    
                    <div class="log-output" id="log-output">
                        <div class="log-entry log-info">🚀 Optimized workspace switching initialized</div>
                        <div class="log-entry log-success">✅ Cache system ready</div>
                        <div class="log-entry log-info">📊 Current workspace: Legal Department</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulated workspace data
        const workspaces = [
            { id: '1', name: 'Legal Department', avatar: 'L', contracts: 24, templates: 8, members: 12 },
            { id: '2', name: 'Sales Team', avatar: 'S', contracts: 18, templates: 5, members: 8 },
            { id: '3', name: 'HR Department', avatar: 'H', contracts: 12, templates: 15, members: 6 },
            { id: '4', name: 'Finance Team', avatar: 'F', contracts: 31, templates: 12, members: 10 },
            { id: '5', name: 'Marketing Division', avatar: 'M', contracts: 9, templates: 22, members: 15 },
            { id: '6', name: 'Product Development', avatar: 'P', contracts: 16, templates: 7, members: 20 },
        ];

        let currentWorkspace = workspaces[0];
        let isListOpen = false;
        let isSwitching = false;
        let cache = new Map();

        // Initialize
        function init() {
            renderWorkspaceList();
            updateContent(currentWorkspace);
        }

        function renderWorkspaceList() {
            const list = document.getElementById('workspace-list');
            list.innerHTML = workspaces.map(ws => `
                <div class="workspace-item ${ws.id === currentWorkspace.id ? 'active' : ''}" 
                     onclick="switchWorkspace('${ws.id}')">
                    <div class="workspace-avatar">${ws.avatar}</div>
                    <div>
                        <div style="font-weight: 500;">${ws.name}</div>
                        <div style="font-size: 0.8rem; color: #a0aec0;">${ws.members} members</div>
                    </div>
                </div>
            `).join('');
        }

        function toggleWorkspaceList() {
            if (isSwitching) return;
            
            isListOpen = !isListOpen;
            const list = document.getElementById('workspace-list');
            list.classList.toggle('show', isListOpen);
        }

        async function switchWorkspace(workspaceId) {
            if (isSwitching || workspaceId === currentWorkspace.id) return;

            const workspace = workspaces.find(ws => ws.id === workspaceId);
            if (!workspace) return;

            const startTime = performance.now();
            isSwitching = true;

            // Optimistic update - immediate UI change
            updateWorkspaceTrigger(workspace);
            addLogEntry(`🔄 Switching to ${workspace.name}...`, 'info');

            // Show loading state
            const trigger = document.getElementById('workspace-trigger');
            const content = document.getElementById('content-area');
            trigger.classList.add('loading');
            content.classList.add('loading');

            try {
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100));

                // Check cache first
                let data = cache.get(workspaceId);
                if (!data) {
                    addLogEntry(`📦 Cache miss - fetching data for ${workspace.name}`, 'warning');
                    // Simulate data fetching
                    await new Promise(resolve => setTimeout(resolve, Math.random() * 200 + 50));
                    data = workspace;
                    cache.set(workspaceId, data);
                } else {
                    addLogEntry(`⚡ Cache hit - using cached data for ${workspace.name}`, 'success');
                }

                currentWorkspace = workspace;
                updateContent(workspace);
                renderWorkspaceList();

                const endTime = performance.now();
                const switchTime = Math.round(endTime - startTime);
                document.getElementById('switch-time').textContent = `${switchTime}ms`;

                addLogEntry(`✅ Successfully switched to ${workspace.name} (${switchTime}ms)`, 'success');

            } catch (error) {
                addLogEntry(`❌ Failed to switch workspace: ${error.message}`, 'error');
            } finally {
                isSwitching = false;
                trigger.classList.remove('loading');
                content.classList.remove('loading');
                isListOpen = false;
                document.getElementById('workspace-list').classList.remove('show');
            }
        }

        function updateWorkspaceTrigger(workspace) {
            document.getElementById('current-avatar').textContent = workspace.avatar;
            document.getElementById('current-workspace').textContent = workspace.name;
        }

        function updateContent(workspace) {
            document.getElementById('contracts-count').textContent = workspace.contracts;
            document.getElementById('templates-count').textContent = workspace.templates;
            document.getElementById('members-count').textContent = workspace.members;
        }

        async function simulateRapidSwitching() {
            addLogEntry('🏃‍♂️ Testing rapid workspace switching...', 'info');
            
            for (let i = 0; i < 3; i++) {
                const randomWorkspace = workspaces[Math.floor(Math.random() * workspaces.length)];
                await switchWorkspace(randomWorkspace.id);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            addLogEntry('✅ Rapid switching test completed', 'success');
        }

        function simulateErrorScenario() {
            addLogEntry('⚠️ Simulating error scenario...', 'warning');
            setTimeout(() => {
                addLogEntry('❌ Simulated network error - rolling back changes', 'error');
                setTimeout(() => {
                    addLogEntry('🔄 Rollback completed - workspace restored', 'success');
                }, 500);
            }, 1000);
        }

        function clearCache() {
            cache.clear();
            addLogEntry('🗑️ Cache cleared - next switches will fetch fresh data', 'info');
        }

        function addLogEntry(message, type = 'info') {
            const log = document.getElementById('log-output');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;

            // Keep only last 20 entries
            while (log.children.length > 20) {
                log.removeChild(log.firstChild);
            }
        }

        // Initialize on load
        window.onload = init;
    </script>
</body>
</html>
