# LegalAI V4 Demo Data Setup Guide

This guide explains how to set up realistic demo data for LegalAI V4, including sample contracts, templates, workspaces, and users.

## Overview

The demo data seeding system creates:

- **5 Demo Users** with different roles and responsibilities
- **4 Demo Workspaces** for different industries (Tech, Legal, HR, Consulting)
- **3 Contract Templates** (NDA, Service Agreement, Employment Contract)
- **3 Sample Contracts** with realistic data and relationships
- **Workspace Memberships** with appropriate role assignments

## Quick Start

### Option 1: Using the Shell Script (Recommended)

```bash
# Make sure you're in the project root directory
cd /path/to/LegalAIV4

# Run the seeding script
./scripts/seed_demo_data.sh

# Or clear existing demo data first
./scripts/seed_demo_data.sh --clear
```

### Option 2: Using Python Directly

```bash
# Navigate to backend directory
cd backend

# Install dependencies
pip install -r requirements.txt

# Run seeding script
python seed_demo_data.py

# Or with options
python seed_demo_data.py --clear --env-file .env
```

## Prerequisites

### Environment Variables

Set the following environment variables or create a `.env` file in the `backend` directory:

```env
SUPABASE_URL=your-supabase-project-url
SUPABASE_KEY=your-supabase-service-key
```

### Python Dependencies

The seeding script requires:
- `supabase` - Supabase Python client
- `python-dotenv` - Environment variable loading (optional)

Install with:
```bash
pip install supabase python-dotenv
```

## Demo Data Details

### Demo Users

| Email | Name | Role | Company | Workspaces |
|-------|------|------|---------|------------|
| <EMAIL> | Sarah Johnson | Legal Operations Manager | TechCorp Inc. | Tech (Admin), Legal (Editor), HR (Editor) |
| <EMAIL> | Michael Chen | Senior Legal Counsel | TechCorp Inc. | Legal (Admin), Tech (Editor), HR (Editor) |
| <EMAIL> | Emily Rodriguez | Business Development Manager | TechCorp Inc. | Tech (Editor), Consulting (Viewer) |
| <EMAIL> | David Kim | Independent Consultant | Kim Consulting LLC | Consulting (Admin), Tech (Viewer) |
| <EMAIL> | Lisa Thompson | HR Director | TechCorp Inc. | HR (Admin), Legal (Editor), Tech (Editor) |

### Demo Workspaces

1. **Demo Tech Workspace**
   - Focus: Software development contracts, NDAs, vendor agreements
   - Members: Sarah (Admin), Michael (Editor), Emily (Editor), David (Viewer)

2. **Demo Legal Workspace**
   - Focus: Corporate contracts and compliance documents
   - Members: Michael (Admin), Sarah (Editor), Lisa (Editor)

3. **Demo HR Workspace**
   - Focus: Employment contracts, NDAs, HR-related agreements
   - Members: Lisa (Admin), Sarah (Editor), Michael (Editor)

4. **Demo Consulting Workspace**
   - Focus: Client contracts and service agreements
   - Members: David (Admin), Emily (Viewer)

### Demo Templates

1. **Standard Non-Disclosure Agreement**
   - Type: NDA
   - Complexity: Medium
   - Industry: Technology
   - Features: Comprehensive confidentiality terms, mutual/unilateral options

2. **Professional Services Agreement**
   - Type: Service Agreement
   - Complexity: Complex
   - Industry: Technology
   - Features: Detailed scope, payment terms, IP provisions

3. **Employment Contract Template**
   - Type: Employment
   - Complexity: Medium
   - Industry: All
   - Features: Standard employment terms, benefits, confidentiality

### Demo Contracts

1. **NDA - TechCorp & DataSolutions Partnership**
   - Status: Active
   - Type: NDA
   - Value: N/A
   - Parties: TechCorp Inc. & DataSolutions Inc.

2. **Software Development Services - Mobile App Project**
   - Status: Active
   - Type: Service Agreement
   - Value: $75,000 USD
   - Parties: TechCorp Inc. & Kim Consulting LLC

3. **Employment Agreement - Senior Software Engineer**
   - Status: Pending Signature
   - Type: Employment
   - Value: $120,000 USD
   - Parties: TechCorp Inc. & Alex Thompson

## User Onboarding Flow

The demo data setup also includes a comprehensive user onboarding system:

### Onboarding Components

1. **OnboardingFlow.tsx** - Main onboarding wizard with 6 steps
2. **OnboardingContext.tsx** - Context provider for onboarding state
3. **GuidedTour.tsx** - Interactive guided tours for specific features

### Onboarding Steps

1. **Welcome** - Introduction to LegalAI platform
2. **Workspace** - Understanding workspaces and organization
3. **Contracts** - Contract management workflow
4. **Templates** - Template library and usage
5. **Collaboration** - Team features and approval workflows
6. **Security** - Security and compliance information

### Integration

To integrate onboarding into your app:

```tsx
import { OnboardingProvider, useOnboarding } from '@/components/onboarding/OnboardingContext';
import { OnboardingFlow } from '@/components/onboarding/OnboardingFlow';

// Wrap your app with OnboardingProvider
<OnboardingProvider>
  <App />
</OnboardingProvider>

// Use in components
const { showOnboarding, completeOnboarding } = useOnboarding();

{showOnboarding && (
  <OnboardingFlow onComplete={completeOnboarding} />
)}
```

## Customization

### Adding More Demo Data

To add more demo data, modify the seeding script:

1. **Users**: Edit `seed_demo_users()` in `backend/app/db/seed_data.py`
2. **Workspaces**: Edit `seed_demo_workspaces()` 
3. **Templates**: Edit `seed_demo_templates()`
4. **Contracts**: Edit `seed_demo_contracts()`

### Custom Contract Templates

Add new templates in `backend/app/db/contract_templates.py`:

```python
def get_custom_template() -> Dict[str, Any]:
    return {
        "id": "template-custom-001",
        "title": "Custom Template",
        "description": "Description of custom template",
        "type": "custom",
        "complexity": "medium",
        # ... rest of template structure
    }
```

## Troubleshooting

### Common Issues

1. **Environment Variables Not Set**
   ```
   Error: SUPABASE_URL and SUPABASE_KEY environment variables must be set
   ```
   Solution: Set the required environment variables or create a `.env` file

2. **Permission Errors**
   ```
   Error: Could not create user/workspace/template
   ```
   Solution: Check your Supabase service key has the required permissions

3. **Database Schema Issues**
   ```
   Error: relation "users" does not exist
   ```
   Solution: Ensure your database schema is up to date by running migrations

### Clearing Demo Data

To remove all demo data:

```bash
# Using the shell script
./scripts/seed_demo_data.sh --clear

# Using Python directly
python backend/seed_demo_data.py --clear
```

This will only remove data with demo-specific identifiers, leaving real user data intact.

## Security Notes

- Demo data uses email addresses with `@demo.legalai.com` domain
- All demo users have predictable IDs starting with `demo-`
- Demo workspaces are prefixed with "Demo "
- Never use demo credentials in production

## Next Steps

After setting up demo data:

1. Start the frontend and backend applications
2. Navigate to the application in your browser
3. The onboarding flow will automatically start for new users
4. Explore the demo contracts, templates, and workspaces
5. Test the various features with realistic data

For more information, see the main project documentation.
