# Theme Implementation Guide

## Overview

The LegalAI application now supports dual theme modes:
- **Landing Page**: Always uses light mode for consistent marketing appearance
- **Authenticated App**: Supports user's preferred theme (light/dark/system)

## Architecture

### Theme Providers

#### 1. Main Theme Provider (`src/lib/theme-provider.tsx`)
Enhanced with `forcedTheme` prop to override user preferences:
```tsx
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  forcedTheme?: Theme; // Forces a specific theme
}
```

#### 2. Landing Theme Provider (`src/lib/landing-theme-provider.tsx`)
Specialized provider that forces light mode for landing pages:
```tsx
export const LandingThemeProvider = ({ children }) => (
  <ThemeProvider 
    defaultTheme="light" 
    storageKey="legalai-theme"
    forcedTheme="light"
  >
    {children}
  </ThemeProvider>
);
```

### Implementation Details

#### Landing Page (`/`)
- Uses `LandingThemeProvider` to force light mode
- Includes its own `ClerkProvider` for authentication
- Theme cannot be changed by user on landing page

#### Authenticated App (`/app/*`)
- Uses regular `ThemeProvider` with user preferences
- Includes theme toggle functionality
- Preserves user's theme choice in localStorage

#### Auth Pages (`/sign-in`, `/sign-up`)
- Uses regular `ThemeProvider` to respect user preferences
- Provides smooth transition between landing and app

### Theme Transitions

#### CSS Transitions
Added smooth transitions for theme changes:
```css
:root {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
}
```

#### Navigation Flow
1. **Landing Page** → Light mode (forced)
2. **Sign In/Up** → User's preferred theme
3. **Authenticated App** → User's preferred theme with toggle

## Usage

### For Landing Page Components
Components automatically use light mode when wrapped in `LandingThemeProvider`:
```tsx
// No special handling needed - automatically light mode
const HeroSection = () => (
  <div className="bg-background text-foreground">
    {/* Always light mode */}
  </div>
);
```

### For Authenticated App Components
Components respect user's theme preference:
```tsx
// Uses user's preferred theme
const Dashboard = () => {
  const { effectiveTheme } = useTheme();
  
  return (
    <div className="bg-background text-foreground">
      {/* Respects user theme: light/dark */}
    </div>
  );
};
```

### Theme Toggle
Updated to use `effectiveTheme` for accurate state:
```tsx
const ThemeToggle = () => {
  const { effectiveTheme, setTheme } = useTheme();
  
  const toggleTheme = () => {
    setTheme(effectiveTheme === "dark" ? "light" : "dark");
  };
  
  return (
    <Button onClick={toggleTheme}>
      {effectiveTheme === "dark" ? <Sun /> : <Moon />}
    </Button>
  );
};
```

## Benefits

1. **Consistent Marketing**: Landing page always appears in light mode
2. **User Choice**: Authenticated users can choose their preferred theme
3. **Smooth Transitions**: CSS transitions provide smooth theme changes
4. **Preserved Preferences**: User theme choices are saved and restored
5. **Flexible Architecture**: Easy to extend or modify theme behavior

## Testing

Visit the application to test:
1. **Landing Page** (`/`) - Should always be light mode
2. **Sign In** (`/sign-in`) - Should respect system/saved preference
3. **App** (`/app/dashboard`) - Should have working theme toggle
4. **Navigation** - Smooth transitions between sections

The theme system now provides the best of both worlds: consistent marketing appearance and user customization options.
