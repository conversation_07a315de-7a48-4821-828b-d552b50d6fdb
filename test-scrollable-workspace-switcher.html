<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scrollable Workspace Switcher Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .demo-dropdown {
            position: relative;
            display: inline-block;
            margin: 20px;
        }
        .dropdown-trigger {
            background: white;
            border: 1px solid #ddd;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 220px;
            justify-content: space-between;
        }
        .dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            width: 220px;
            z-index: 1000;
            padding: 0;
            display: none;
        }
        .dropdown-content.show {
            display: block;
        }
        .dropdown-header {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            font-size: 14px;
            color: #374151;
        }
        .dropdown-list {
            max-height: 300px;
            overflow-y: auto;
            padding: 4px 0;
        }
        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            margin: 0 4px;
            border-radius: 4px;
        }
        .dropdown-item:hover {
            background-color: #f3f4f6;
        }
        .dropdown-item.active {
            background-color: #e0f2fe;
            border: 1px solid #0ea5e9;
        }
        .workspace-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #1f2937;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            flex-shrink: 0;
        }
        .workspace-info {
            flex: 1;
            min-width: 0;
        }
        .workspace-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .workspace-members {
            font-size: 12px;
            color: #6b7280;
        }
        .active-badge {
            background-color: #f0f9ff;
            color: #0369a1;
            border: 1px solid #bae6fd;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }
        .dropdown-footer {
            border-top: 1px solid #e5e7eb;
            padding: 4px;
        }
        .dropdown-footer .dropdown-item {
            margin: 0;
        }
        /* Custom scrollbar styles */
        .dropdown-list {
            scrollbar-width: thin;
            scrollbar-color: rgba(0,0,0,0.3) transparent;
        }
        .dropdown-list::-webkit-scrollbar {
            width: 6px;
        }
        .dropdown-list::-webkit-scrollbar-track {
            background: transparent;
        }
        .dropdown-list::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,0.3);
            border-radius: 3px;
            transition: background-color 0.2s ease;
        }
        .dropdown-list::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0,0,0,0.5);
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .test-metrics {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        .metric-label {
            font-weight: 500;
        }
        .metric-value {
            color: #007bff;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>🧪 Scrollable Workspace Switcher Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Scrollable Dropdown Demo</h2>
        <p>This demo simulates the workspace switcher with many workspaces to test scrolling behavior.</p>
        
        <div class="demo-dropdown">
            <div class="dropdown-trigger" onclick="toggleDropdown()">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div class="workspace-avatar">T</div>
                    <span>Test Workspace</span>
                </div>
                <span>▼</span>
            </div>
            
            <div class="dropdown-content" id="dropdown">
                <div class="dropdown-header">Workspaces</div>
                <div class="dropdown-list" id="workspace-list">
                    <!-- Workspaces will be populated by JavaScript -->
                </div>
                <div class="dropdown-footer">
                    <div class="dropdown-item">
                        <span>Manage Workspaces</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 2: Scrolling Behavior Analysis</h2>
        <button onclick="runScrollTests()">Run Scroll Tests</button>
        <div id="scroll-results"></div>
        
        <div class="test-grid">
            <div class="test-metrics">
                <h4>Dropdown Metrics</h4>
                <div class="metric">
                    <span class="metric-label">Total Workspaces:</span>
                    <span class="metric-value" id="total-workspaces">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Visible Height:</span>
                    <span class="metric-value" id="visible-height">0px</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Scroll Height:</span>
                    <span class="metric-value" id="scroll-height">0px</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Is Scrollable:</span>
                    <span class="metric-value" id="is-scrollable">No</span>
                </div>
            </div>
            
            <div class="test-metrics">
                <h4>Performance Metrics</h4>
                <div class="metric">
                    <span class="metric-label">Render Time:</span>
                    <span class="metric-value" id="render-time">0ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Scroll Performance:</span>
                    <span class="metric-value" id="scroll-performance">Good</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value" id="memory-usage">Normal</span>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 3: Responsive Behavior</h2>
        <button onclick="testResponsive()">Test Mobile View</button>
        <div id="responsive-results"></div>
    </div>

    <script>
        let isDropdownOpen = false;
        let workspaces = [];

        // Generate test workspaces
        function generateWorkspaces(count = 15) {
            const names = [
                'Legal Department', 'Contract Management', 'Sales Team', 'HR Department',
                'Finance Team', 'Marketing Division', 'Product Development', 'Customer Success',
                'Operations Team', 'Engineering', 'Quality Assurance', 'Business Development',
                'Compliance Team', 'Risk Management', 'Executive Office', 'IT Department',
                'Research & Development', 'Strategic Planning', 'Vendor Management'
            ];
            
            workspaces = [];
            for (let i = 0; i < count; i++) {
                workspaces.push({
                    id: `workspace-${i}`,
                    name: names[i % names.length] + (i >= names.length ? ` ${Math.floor(i / names.length) + 1}` : ''),
                    members: Math.floor(Math.random() * 50) + 1,
                    isActive: i === 0
                });
            }
            return workspaces;
        }

        function renderWorkspaces() {
            const startTime = performance.now();
            const list = document.getElementById('workspace-list');
            
            list.innerHTML = workspaces.map(workspace => `
                <div class="dropdown-item ${workspace.isActive ? 'active' : ''}" 
                     onclick="selectWorkspace('${workspace.id}')">
                    <div class="workspace-avatar">${workspace.name.charAt(0)}</div>
                    <div class="workspace-info">
                        <div class="workspace-name">${workspace.name}</div>
                        <div class="workspace-members">${workspace.members} member${workspace.members !== 1 ? 's' : ''}</div>
                    </div>
                    ${workspace.isActive ? '<div class="active-badge">Active</div>' : ''}
                </div>
            `).join('');
            
            const endTime = performance.now();
            document.getElementById('render-time').textContent = `${(endTime - startTime).toFixed(2)}ms`;
            updateMetrics();
        }

        function updateMetrics() {
            const list = document.getElementById('workspace-list');
            const totalWorkspaces = workspaces.length;
            const visibleHeight = list.clientHeight;
            const scrollHeight = list.scrollHeight;
            const isScrollable = scrollHeight > visibleHeight;

            document.getElementById('total-workspaces').textContent = totalWorkspaces;
            document.getElementById('visible-height').textContent = `${visibleHeight}px`;
            document.getElementById('scroll-height').textContent = `${scrollHeight}px`;
            document.getElementById('is-scrollable').textContent = isScrollable ? 'Yes' : 'No';
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('dropdown');
            isDropdownOpen = !isDropdownOpen;
            dropdown.classList.toggle('show', isDropdownOpen);
            
            if (isDropdownOpen && workspaces.length === 0) {
                generateWorkspaces(15);
                renderWorkspaces();
            }
        }

        function selectWorkspace(id) {
            workspaces.forEach(ws => ws.isActive = ws.id === id);
            const selected = workspaces.find(ws => ws.id === id);
            if (selected) {
                document.querySelector('.dropdown-trigger span').textContent = selected.name;
                document.querySelector('.workspace-avatar').textContent = selected.name.charAt(0);
            }
            renderWorkspaces();
            toggleDropdown();
        }

        function runScrollTests() {
            const results = document.getElementById('scroll-results');
            results.innerHTML = '<div class="test-result info">🔄 Running scroll tests...</div>';
            
            // Test with different workspace counts
            const tests = [
                { count: 5, expected: 'No scroll needed' },
                { count: 10, expected: 'Scroll should appear' },
                { count: 20, expected: 'Definite scrolling' }
            ];
            
            let testResults = [];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    generateWorkspaces(test.count);
                    renderWorkspaces();
                    
                    const list = document.getElementById('workspace-list');
                    const isScrollable = list.scrollHeight > list.clientHeight;
                    const result = isScrollable ? 'Scrollable' : 'Not scrollable';
                    
                    testResults.push(`
                        <div class="test-result ${isScrollable ? 'success' : 'info'}">
                            ✅ Test ${index + 1}: ${test.count} workspaces - ${result} (${test.expected})
                        </div>
                    `);
                    
                    if (index === tests.length - 1) {
                        results.innerHTML = testResults.join('');
                        results.innerHTML += '<div class="test-result success">✅ All scroll tests completed!</div>';
                    }
                }, index * 1000);
            });
        }

        function testResponsive() {
            const results = document.getElementById('responsive-results');
            const dropdown = document.querySelector('.demo-dropdown');
            
            results.innerHTML = '<div class="test-result info">🔄 Testing responsive behavior...</div>';
            
            // Simulate mobile viewport
            dropdown.style.transform = 'scale(0.8)';
            dropdown.style.transformOrigin = 'top left';
            
            setTimeout(() => {
                results.innerHTML = `
                    <div class="test-result success">✅ Responsive test completed</div>
                    <div class="test-result info">
                        📱 Mobile simulation applied. The dropdown maintains its functionality at smaller scales.
                        <br>• Fixed header and footer remain in position
                        <br>• Scrollable area adapts to available space
                        <br>• Touch scrolling works on mobile devices
                    </div>
                `;
                
                // Reset after 3 seconds
                setTimeout(() => {
                    dropdown.style.transform = '';
                    dropdown.style.transformOrigin = '';
                }, 3000);
            }, 1000);
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.querySelector('.demo-dropdown');
            if (!dropdown.contains(event.target)) {
                document.getElementById('dropdown').classList.remove('show');
                isDropdownOpen = false;
            }
        });

        // Initialize
        window.onload = function() {
            generateWorkspaces(15);
            console.log('Scrollable Workspace Switcher Test initialized');
        };
    </script>
</body>
</html>
