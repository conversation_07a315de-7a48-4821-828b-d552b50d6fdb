<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clerk Authentication Test</title>
    <script src="https://unpkg.com/@clerk/clerk-js@latest/dist/clerk.browser.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .user-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .api-test {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Clerk Authentication Test</h1>
        <p>This page tests the Clerk authentication setup for LegalAI V4.</p>
        
        <div id="status" class="status info">
            Initializing Clerk...
        </div>
        
        <div id="auth-section">
            <h2>Authentication</h2>
            <button id="sign-in-btn" onclick="signIn()" disabled>Sign In</button>
            <button id="sign-up-btn" onclick="signUp()" disabled>Sign Up</button>
            <button id="sign-out-btn" onclick="signOut()" disabled style="display: none;">Sign Out</button>
        </div>
        
        <div id="user-section" style="display: none;">
            <h2>User Information</h2>
            <div id="user-info" class="user-info"></div>
        </div>
        
        <div id="api-section" style="display: none;">
            <h2>API Testing</h2>
            <div class="api-test">
                <p>Test authenticated API calls to your backend:</p>
                <button onclick="testAuthEndpoint()">Test Auth Endpoint</button>
                <button onclick="testContractsEndpoint()">Test Contracts Endpoint</button>
                <button onclick="testDemoEndpoint()">Test Demo Endpoint</button>
                <div id="api-results"></div>
            </div>
        </div>
        
        <div id="setup-info">
            <h2>Setup Information</h2>
            <div class="info">
                <p><strong>Publishable Key:</strong> pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ</p>
                <p><strong>Backend URL:</strong> http://127.0.0.1:8000</p>
                <p><strong>Frontend URL:</strong> http://localhost:5173</p>
            </div>
        </div>
    </div>

    <script>
        const PUBLISHABLE_KEY = 'pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ';
        const BACKEND_URL = 'http://127.0.0.1:8000';
        
        let clerk;
        
        async function initializeClerk() {
            try {
                clerk = new window.Clerk(PUBLISHABLE_KEY);
                await clerk.load();
                
                updateStatus('Clerk initialized successfully!', 'success');
                
                // Enable buttons
                document.getElementById('sign-in-btn').disabled = false;
                document.getElementById('sign-up-btn').disabled = false;
                
                // Check if user is already signed in
                if (clerk.user) {
                    handleUserSignedIn();
                }
                
                // Listen for auth state changes
                clerk.addListener('user', handleUserSignedIn);
                
            } catch (error) {
                updateStatus(`Failed to initialize Clerk: ${error.message}`, 'error');
                console.error('Clerk initialization error:', error);
            }
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function handleUserSignedIn() {
            if (clerk.user) {
                // Show user info
                document.getElementById('user-section').style.display = 'block';
                document.getElementById('api-section').style.display = 'block';
                document.getElementById('sign-out-btn').style.display = 'inline-block';
                document.getElementById('sign-in-btn').style.display = 'none';
                document.getElementById('sign-up-btn').style.display = 'none';
                
                // Display user information
                const userInfo = document.getElementById('user-info');
                userInfo.innerHTML = `
                    <p><strong>ID:</strong> ${clerk.user.id}</p>
                    <p><strong>Email:</strong> ${clerk.user.primaryEmailAddress?.emailAddress || 'N/A'}</p>
                    <p><strong>Name:</strong> ${clerk.user.firstName || ''} ${clerk.user.lastName || ''}</p>
                    <p><strong>Created:</strong> ${new Date(clerk.user.createdAt).toLocaleString()}</p>
                `;
                
                updateStatus(`Welcome, ${clerk.user.firstName || 'User'}! You are signed in.`, 'success');
            } else {
                // Hide user info
                document.getElementById('user-section').style.display = 'none';
                document.getElementById('api-section').style.display = 'none';
                document.getElementById('sign-out-btn').style.display = 'none';
                document.getElementById('sign-in-btn').style.display = 'inline-block';
                document.getElementById('sign-up-btn').style.display = 'inline-block';
                
                updateStatus('Please sign in to test the authentication.', 'info');
            }
        }
        
        async function signIn() {
            try {
                await clerk.openSignIn();
            } catch (error) {
                updateStatus(`Sign in failed: ${error.message}`, 'error');
            }
        }
        
        async function signUp() {
            try {
                await clerk.openSignUp();
            } catch (error) {
                updateStatus(`Sign up failed: ${error.message}`, 'error');
            }
        }
        
        async function signOut() {
            try {
                await clerk.signOut();
                updateStatus('Signed out successfully!', 'success');
            } catch (error) {
                updateStatus(`Sign out failed: ${error.message}`, 'error');
            }
        }
        
        async function makeAuthenticatedRequest(endpoint) {
            try {
                if (!clerk.session) {
                    throw new Error('No active session');
                }
                
                const token = await clerk.session.getToken();
                
                const response = await fetch(`${BACKEND_URL}${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }
        
        async function testAuthEndpoint() {
            const result = await makeAuthenticatedRequest('/api/auth/test');
            displayApiResult('Auth Test', result);
        }
        
        async function testContractsEndpoint() {
            const result = await makeAuthenticatedRequest('/api/contracts');
            displayApiResult('Contracts', result);
        }
        
        async function testDemoEndpoint() {
            // Demo endpoint doesn't require auth
            try {
                const response = await fetch(`${BACKEND_URL}/api/demo/all`);
                const data = await response.json();
                
                displayApiResult('Demo Data', {
                    status: response.status,
                    ok: response.ok,
                    data: data
                });
            } catch (error) {
                displayApiResult('Demo Data', {
                    status: 0,
                    ok: false,
                    error: error.message
                });
            }
        }
        
        function displayApiResult(title, result) {
            const resultsEl = document.getElementById('api-results');
            
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h4>${title} Result:</h4>
                <p><strong>Status:</strong> ${result.status} ${result.ok ? '✅' : '❌'}</p>
                ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
                <hr>
            `;
            
            resultsEl.appendChild(resultDiv);
        }
        
        // Initialize when page loads
        window.addEventListener('load', initializeClerk);
    </script>
</body>
</html>
