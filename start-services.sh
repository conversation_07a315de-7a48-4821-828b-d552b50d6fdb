#!/bin/bash

# Script to start both backend and frontend services cleanly

echo "🚀 Starting LegalAI Services..."

# Function to kill processes on specific ports
kill_port() {
    local port=$1
    echo "Checking for processes on port $port..."
    
    if command -v lsof > /dev/null; then
        # macOS/Linux
        local pid=$(lsof -ti:$port)
        if [ ! -z "$pid" ]; then
            echo "Killing process $pid on port $port..."
            kill -9 $pid
            sleep 1
        fi
    fi
}

# Kill any existing processes
kill_port 8000
kill_port 5173

# Clear Vite cache
echo "Clearing Vite cache..."
rm -rf node_modules/.vite

# Start backend in background
echo "Starting backend..."
cd backend
python3 run_backend.py &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "Starting frontend..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ Services started successfully!"
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:8000"
echo ""
echo "To stop services, press Ctrl+C or run:"
echo "kill $BACKEND_PID $FRONTEND_PID"

# Wait for user to stop
wait
